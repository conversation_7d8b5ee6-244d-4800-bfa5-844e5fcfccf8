"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/chess.js@1.4.0";
exports.ids = ["vendor-chunks/chess.js@1.4.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/chess.js@1.4.0/node_modules/chess.js/dist/esm/chess.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/chess.js@1.4.0/node_modules/chess.js/dist/esm/chess.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BISHOP: () => (/* binding */ BISHOP),\n/* harmony export */   BLACK: () => (/* binding */ BLACK),\n/* harmony export */   Chess: () => (/* binding */ Chess),\n/* harmony export */   DEFAULT_POSITION: () => (/* binding */ DEFAULT_POSITION),\n/* harmony export */   KING: () => (/* binding */ KING),\n/* harmony export */   KNIGHT: () => (/* binding */ KNIGHT),\n/* harmony export */   Move: () => (/* binding */ Move),\n/* harmony export */   PAWN: () => (/* binding */ PAWN),\n/* harmony export */   QUEEN: () => (/* binding */ QUEEN),\n/* harmony export */   ROOK: () => (/* binding */ ROOK),\n/* harmony export */   SEVEN_TAG_ROSTER: () => (/* binding */ SEVEN_TAG_ROSTER),\n/* harmony export */   SQUARES: () => (/* binding */ SQUARES),\n/* harmony export */   WHITE: () => (/* binding */ WHITE),\n/* harmony export */   validateFen: () => (/* binding */ validateFen),\n/* harmony export */   xoroshiro128: () => (/* binding */ xoroshiro128)\n/* harmony export */ });\n// @generated by Peggy 4.2.0.\n//\n// https://peggyjs.org/\n\n\n\n  function rootNode(comment) {\n  \treturn comment !== null ? { comment, variations: [] } : { variations: []}\n  }\n\n  function node(move, suffix, nag, comment, variations) {\n  \tconst node = { move, variations };\n\n    if (suffix) {\n    \tnode.suffix = suffix;\n    }\n\n    if (nag) {\n    \tnode.nag = nag;\n    }\n\n    if (comment !== null) {\n    \tnode.comment = comment;\n    }\n\n    return node\n  }\n\n  function lineToTree(...nodes) {\n  \tconst [root, ...rest] = nodes;\n\n    let parent = root;\n\n    for (const child of rest) {\n    \tif (child !== null) {\n        \tparent.variations = [child, ...child.variations];\n            child.variations = [];\n            parent = child;\n        }\n    }\n\n  \treturn root\n  }\n\n  function pgn(headers, game) {\n  \tif (game.marker && game.marker.comment) {\n    \tlet node = game.root;\n        while (true) {\n        \tconst next = node.variations[0];\n            if (!next) {\n            \tnode.comment = game.marker.comment;\n            \tbreak\n            }\n            node = next;\n        }\n    }\n\n  \treturn {\n    \theaders,\n        root: game.root,\n        result: (game.marker && game.marker.result) ?? undefined\n    }\n  }\n\nfunction peg$subclass(child, parent) {\n  function C() { this.constructor = child; }\n  C.prototype = parent.prototype;\n  child.prototype = new C();\n}\n\nfunction peg$SyntaxError(message, expected, found, location) {\n  var self = Error.call(this, message);\n  // istanbul ignore next Check is a necessary evil to support older environments\n  if (Object.setPrototypeOf) {\n    Object.setPrototypeOf(self, peg$SyntaxError.prototype);\n  }\n  self.expected = expected;\n  self.found = found;\n  self.location = location;\n  self.name = \"SyntaxError\";\n  return self;\n}\n\npeg$subclass(peg$SyntaxError, Error);\n\nfunction peg$padEnd(str, targetLength, padString) {\n  padString = padString || \" \";\n  if (str.length > targetLength) { return str; }\n  targetLength -= str.length;\n  padString += padString.repeat(targetLength);\n  return str + padString.slice(0, targetLength);\n}\n\npeg$SyntaxError.prototype.format = function(sources) {\n  var str = \"Error: \" + this.message;\n  if (this.location) {\n    var src = null;\n    var k;\n    for (k = 0; k < sources.length; k++) {\n      if (sources[k].source === this.location.source) {\n        src = sources[k].text.split(/\\r\\n|\\n|\\r/g);\n        break;\n      }\n    }\n    var s = this.location.start;\n    var offset_s = (this.location.source && (typeof this.location.source.offset === \"function\"))\n      ? this.location.source.offset(s)\n      : s;\n    var loc = this.location.source + \":\" + offset_s.line + \":\" + offset_s.column;\n    if (src) {\n      var e = this.location.end;\n      var filler = peg$padEnd(\"\", offset_s.line.toString().length, ' ');\n      var line = src[s.line - 1];\n      var last = s.line === e.line ? e.column : line.length + 1;\n      var hatLen = (last - s.column) || 1;\n      str += \"\\n --> \" + loc + \"\\n\"\n          + filler + \" |\\n\"\n          + offset_s.line + \" | \" + line + \"\\n\"\n          + filler + \" | \" + peg$padEnd(\"\", s.column - 1, ' ')\n          + peg$padEnd(\"\", hatLen, \"^\");\n    } else {\n      str += \"\\n at \" + loc;\n    }\n  }\n  return str;\n};\n\npeg$SyntaxError.buildMessage = function(expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n    literal: function(expectation) {\n      return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n    },\n\n    class: function(expectation) {\n      var escapedParts = expectation.parts.map(function(part) {\n        return Array.isArray(part)\n          ? classEscape(part[0]) + \"-\" + classEscape(part[1])\n          : classEscape(part);\n      });\n\n      return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts.join(\"\") + \"]\";\n    },\n\n    any: function() {\n      return \"any character\";\n    },\n\n    end: function() {\n      return \"end of input\";\n    },\n\n    other: function(expectation) {\n      return expectation.description;\n    }\n  };\n\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n\n  function literalEscape(s) {\n    return s\n      .replace(/\\\\/g, \"\\\\\\\\\")\n      .replace(/\"/g,  \"\\\\\\\"\")\n      .replace(/\\0/g, \"\\\\0\")\n      .replace(/\\t/g, \"\\\\t\")\n      .replace(/\\n/g, \"\\\\n\")\n      .replace(/\\r/g, \"\\\\r\")\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return \"\\\\x0\" + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return \"\\\\x\"  + hex(ch); });\n  }\n\n  function classEscape(s) {\n    return s\n      .replace(/\\\\/g, \"\\\\\\\\\")\n      .replace(/\\]/g, \"\\\\]\")\n      .replace(/\\^/g, \"\\\\^\")\n      .replace(/-/g,  \"\\\\-\")\n      .replace(/\\0/g, \"\\\\0\")\n      .replace(/\\t/g, \"\\\\t\")\n      .replace(/\\n/g, \"\\\\n\")\n      .replace(/\\r/g, \"\\\\r\")\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return \"\\\\x0\" + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return \"\\\\x\"  + hex(ch); });\n  }\n\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n\n  function describeExpected(expected) {\n    var descriptions = expected.map(describeExpectation);\n    var i, j;\n\n    descriptions.sort();\n\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n\n      default:\n        return descriptions.slice(0, -1).join(\", \")\n          + \", or \"\n          + descriptions[descriptions.length - 1];\n    }\n  }\n\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\n\nfunction peg$parse(input, options) {\n  options = options !== undefined ? options : {};\n\n  var peg$FAILED = {};\n  var peg$source = options.grammarSource;\n\n  var peg$startRuleFunctions = { pgn: peg$parsepgn };\n  var peg$startRuleFunction = peg$parsepgn;\n\n  var peg$c0 = \"[\";\n  var peg$c1 = \"\\\"\";\n  var peg$c2 = \"]\";\n  var peg$c3 = \".\";\n  var peg$c4 = \"O-O-O\";\n  var peg$c5 = \"O-O\";\n  var peg$c6 = \"0-0-0\";\n  var peg$c7 = \"0-0\";\n  var peg$c8 = \"$\";\n  var peg$c9 = \"{\";\n  var peg$c10 = \"}\";\n  var peg$c11 = \";\";\n  var peg$c12 = \"(\";\n  var peg$c13 = \")\";\n  var peg$c14 = \"1-0\";\n  var peg$c15 = \"0-1\";\n  var peg$c16 = \"1/2-1/2\";\n  var peg$c17 = \"*\";\n\n  var peg$r0 = /^[a-zA-Z]/;\n  var peg$r1 = /^[^\"]/;\n  var peg$r2 = /^[0-9]/;\n  var peg$r3 = /^[.]/;\n  var peg$r4 = /^[a-zA-Z1-8\\-=]/;\n  var peg$r5 = /^[+#]/;\n  var peg$r6 = /^[!?]/;\n  var peg$r7 = /^[^}]/;\n  var peg$r8 = /^[^\\r\\n]/;\n  var peg$r9 = /^[ \\t\\r\\n]/;\n\n  var peg$e0 = peg$otherExpectation(\"tag pair\");\n  var peg$e1 = peg$literalExpectation(\"[\", false);\n  var peg$e2 = peg$literalExpectation(\"\\\"\", false);\n  var peg$e3 = peg$literalExpectation(\"]\", false);\n  var peg$e4 = peg$otherExpectation(\"tag name\");\n  var peg$e5 = peg$classExpectation([[\"a\", \"z\"], [\"A\", \"Z\"]], false, false);\n  var peg$e6 = peg$otherExpectation(\"tag value\");\n  var peg$e7 = peg$classExpectation([\"\\\"\"], true, false);\n  var peg$e8 = peg$otherExpectation(\"move number\");\n  var peg$e9 = peg$classExpectation([[\"0\", \"9\"]], false, false);\n  var peg$e10 = peg$literalExpectation(\".\", false);\n  var peg$e11 = peg$classExpectation([\".\"], false, false);\n  var peg$e12 = peg$otherExpectation(\"standard algebraic notation\");\n  var peg$e13 = peg$literalExpectation(\"O-O-O\", false);\n  var peg$e14 = peg$literalExpectation(\"O-O\", false);\n  var peg$e15 = peg$literalExpectation(\"0-0-0\", false);\n  var peg$e16 = peg$literalExpectation(\"0-0\", false);\n  var peg$e17 = peg$classExpectation([[\"a\", \"z\"], [\"A\", \"Z\"], [\"1\", \"8\"], \"-\", \"=\"], false, false);\n  var peg$e18 = peg$classExpectation([\"+\", \"#\"], false, false);\n  var peg$e19 = peg$otherExpectation(\"suffix annotation\");\n  var peg$e20 = peg$classExpectation([\"!\", \"?\"], false, false);\n  var peg$e21 = peg$otherExpectation(\"NAG\");\n  var peg$e22 = peg$literalExpectation(\"$\", false);\n  var peg$e23 = peg$otherExpectation(\"brace comment\");\n  var peg$e24 = peg$literalExpectation(\"{\", false);\n  var peg$e25 = peg$classExpectation([\"}\"], true, false);\n  var peg$e26 = peg$literalExpectation(\"}\", false);\n  var peg$e27 = peg$otherExpectation(\"rest of line comment\");\n  var peg$e28 = peg$literalExpectation(\";\", false);\n  var peg$e29 = peg$classExpectation([\"\\r\", \"\\n\"], true, false);\n  var peg$e30 = peg$otherExpectation(\"variation\");\n  var peg$e31 = peg$literalExpectation(\"(\", false);\n  var peg$e32 = peg$literalExpectation(\")\", false);\n  var peg$e33 = peg$otherExpectation(\"game termination marker\");\n  var peg$e34 = peg$literalExpectation(\"1-0\", false);\n  var peg$e35 = peg$literalExpectation(\"0-1\", false);\n  var peg$e36 = peg$literalExpectation(\"1/2-1/2\", false);\n  var peg$e37 = peg$literalExpectation(\"*\", false);\n  var peg$e38 = peg$otherExpectation(\"whitespace\");\n  var peg$e39 = peg$classExpectation([\" \", \"\\t\", \"\\r\", \"\\n\"], false, false);\n\n  var peg$f0 = function(headers, game) { return pgn(headers, game) };\n  var peg$f1 = function(tagPairs) { return Object.fromEntries(tagPairs) };\n  var peg$f2 = function(tagName, tagValue) { return [tagName, tagValue] };\n  var peg$f3 = function(root, marker) { return { root, marker} };\n  var peg$f4 = function(comment, moves) { return lineToTree(rootNode(comment), ...moves.flat()) };\n  var peg$f5 = function(san, suffix, nag, comment, variations) { return node(san, suffix, nag, comment, variations) };\n  var peg$f6 = function(nag) { return nag };\n  var peg$f7 = function(comment) { return comment.replace(/[\\r\\n]+/g, \" \") };\n  var peg$f8 = function(comment) { return comment.trim() };\n  var peg$f9 = function(line) { return line };\n  var peg$f10 = function(result, comment) { return { result, comment } };\n  var peg$currPos = options.peg$currPos | 0;\n  var peg$posDetailsCache = [{ line: 1, column: 1 }];\n  var peg$maxFailPos = peg$currPos;\n  var peg$maxFailExpected = options.peg$maxFailExpected || [];\n  var peg$silentFails = options.peg$silentFails | 0;\n\n  var peg$result;\n\n  if (options.startRule) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n\n  function peg$literalExpectation(text, ignoreCase) {\n    return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n  }\n\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n  }\n\n  function peg$endExpectation() {\n    return { type: \"end\" };\n  }\n\n  function peg$otherExpectation(description) {\n    return { type: \"other\", description: description };\n  }\n\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos];\n    var p;\n\n    if (details) {\n      return details;\n    } else {\n      if (pos >= peg$posDetailsCache.length) {\n        p = peg$posDetailsCache.length - 1;\n      } else {\n        p = pos;\n        while (!peg$posDetailsCache[--p]) {}\n      }\n\n      details = peg$posDetailsCache[p];\n      details = {\n        line: details.line,\n        column: details.column\n      };\n\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n\n        p++;\n      }\n\n      peg$posDetailsCache[pos] = details;\n\n      return details;\n    }\n  }\n\n  function peg$computeLocation(startPos, endPos, offset) {\n    var startPosDetails = peg$computePosDetails(startPos);\n    var endPosDetails = peg$computePosDetails(endPos);\n\n    var res = {\n      source: peg$source,\n      start: {\n        offset: startPos,\n        line: startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line: endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n    return res;\n  }\n\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) { return; }\n\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n\n    peg$maxFailExpected.push(expected);\n  }\n\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(\n      peg$SyntaxError.buildMessage(expected, found),\n      expected,\n      found,\n      location\n    );\n  }\n\n  function peg$parsepgn() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = peg$parsetagPairSection();\n    s2 = peg$parsemoveTextSection();\n    s0 = peg$f0(s1, s2);\n\n    return s0;\n  }\n\n  function peg$parsetagPairSection() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsetagPair();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsetagPair();\n    }\n    s2 = peg$parse_();\n    s0 = peg$f1(s1);\n\n    return s0;\n  }\n\n  function peg$parsetagPair() {\n    var s0, s2, s4, s6, s7, s8, s10;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    peg$parse_();\n    if (input.charCodeAt(peg$currPos) === 91) {\n      s2 = peg$c0;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e1); }\n    }\n    if (s2 !== peg$FAILED) {\n      peg$parse_();\n      s4 = peg$parsetagName();\n      if (s4 !== peg$FAILED) {\n        peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 34) {\n          s6 = peg$c1;\n          peg$currPos++;\n        } else {\n          s6 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e2); }\n        }\n        if (s6 !== peg$FAILED) {\n          s7 = peg$parsetagValue();\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s8 = peg$c1;\n            peg$currPos++;\n          } else {\n            s8 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e2); }\n          }\n          if (s8 !== peg$FAILED) {\n            peg$parse_();\n            if (input.charCodeAt(peg$currPos) === 93) {\n              s10 = peg$c2;\n              peg$currPos++;\n            } else {\n              s10 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$e3); }\n            }\n            if (s10 !== peg$FAILED) {\n              s0 = peg$f2(s4, s7);\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      if (peg$silentFails === 0) { peg$fail(peg$e0); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsetagName() {\n    var s0, s1, s2;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r0.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e5); }\n    }\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = input.charAt(peg$currPos);\n        if (peg$r0.test(s2)) {\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e5); }\n        }\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s0 = input.substring(s0, peg$currPos);\n    } else {\n      s0 = s1;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e4); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsetagValue() {\n    var s0, s1, s2;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r1.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e7); }\n    }\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = input.charAt(peg$currPos);\n      if (peg$r1.test(s2)) {\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e7); }\n      }\n    }\n    s0 = input.substring(s0, peg$currPos);\n    peg$silentFails--;\n    s1 = peg$FAILED;\n    if (peg$silentFails === 0) { peg$fail(peg$e6); }\n\n    return s0;\n  }\n\n  function peg$parsemoveTextSection() {\n    var s0, s1, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parseline();\n    peg$parse_();\n    s3 = peg$parsegameTerminationMarker();\n    if (s3 === peg$FAILED) {\n      s3 = null;\n    }\n    peg$parse_();\n    s0 = peg$f3(s1, s3);\n\n    return s0;\n  }\n\n  function peg$parseline() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecomment();\n    if (s1 === peg$FAILED) {\n      s1 = null;\n    }\n    s2 = [];\n    s3 = peg$parsemove();\n    while (s3 !== peg$FAILED) {\n      s2.push(s3);\n      s3 = peg$parsemove();\n    }\n    s0 = peg$f4(s1, s2);\n\n    return s0;\n  }\n\n  function peg$parsemove() {\n    var s0, s4, s5, s6, s7, s8, s9, s10;\n\n    s0 = peg$currPos;\n    peg$parse_();\n    peg$parsemoveNumber();\n    peg$parse_();\n    s4 = peg$parsesan();\n    if (s4 !== peg$FAILED) {\n      s5 = peg$parsesuffixAnnotation();\n      if (s5 === peg$FAILED) {\n        s5 = null;\n      }\n      s6 = [];\n      s7 = peg$parsenag();\n      while (s7 !== peg$FAILED) {\n        s6.push(s7);\n        s7 = peg$parsenag();\n      }\n      s7 = peg$parse_();\n      s8 = peg$parsecomment();\n      if (s8 === peg$FAILED) {\n        s8 = null;\n      }\n      s9 = [];\n      s10 = peg$parsevariation();\n      while (s10 !== peg$FAILED) {\n        s9.push(s10);\n        s10 = peg$parsevariation();\n      }\n      s0 = peg$f5(s4, s5, s6, s8, s9);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsemoveNumber() {\n    var s0, s1, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r2.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e9); }\n    }\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = input.charAt(peg$currPos);\n      if (peg$r2.test(s2)) {\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e9); }\n      }\n    }\n    if (input.charCodeAt(peg$currPos) === 46) {\n      s2 = peg$c3;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e10); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parse_();\n      s4 = [];\n      s5 = input.charAt(peg$currPos);\n      if (peg$r3.test(s5)) {\n        peg$currPos++;\n      } else {\n        s5 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e11); }\n      }\n      while (s5 !== peg$FAILED) {\n        s4.push(s5);\n        s5 = input.charAt(peg$currPos);\n        if (peg$r3.test(s5)) {\n          peg$currPos++;\n        } else {\n          s5 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e11); }\n        }\n      }\n      s1 = [s1, s2, s3, s4];\n      s0 = s1;\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e8); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsesan() {\n    var s0, s1, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c4) {\n      s2 = peg$c4;\n      peg$currPos += 5;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e13); }\n    }\n    if (s2 === peg$FAILED) {\n      if (input.substr(peg$currPos, 3) === peg$c5) {\n        s2 = peg$c5;\n        peg$currPos += 3;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e14); }\n      }\n      if (s2 === peg$FAILED) {\n        if (input.substr(peg$currPos, 5) === peg$c6) {\n          s2 = peg$c6;\n          peg$currPos += 5;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e15); }\n        }\n        if (s2 === peg$FAILED) {\n          if (input.substr(peg$currPos, 3) === peg$c7) {\n            s2 = peg$c7;\n            peg$currPos += 3;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e16); }\n          }\n          if (s2 === peg$FAILED) {\n            s2 = peg$currPos;\n            s3 = input.charAt(peg$currPos);\n            if (peg$r0.test(s3)) {\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$e5); }\n            }\n            if (s3 !== peg$FAILED) {\n              s4 = [];\n              s5 = input.charAt(peg$currPos);\n              if (peg$r4.test(s5)) {\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$e17); }\n              }\n              if (s5 !== peg$FAILED) {\n                while (s5 !== peg$FAILED) {\n                  s4.push(s5);\n                  s5 = input.charAt(peg$currPos);\n                  if (peg$r4.test(s5)) {\n                    peg$currPos++;\n                  } else {\n                    s5 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$e17); }\n                  }\n                }\n              } else {\n                s4 = peg$FAILED;\n              }\n              if (s4 !== peg$FAILED) {\n                s3 = [s3, s4];\n                s2 = s3;\n              } else {\n                peg$currPos = s2;\n                s2 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s2;\n              s2 = peg$FAILED;\n            }\n          }\n        }\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = input.charAt(peg$currPos);\n      if (peg$r5.test(s3)) {\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e18); }\n      }\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      s2 = [s2, s3];\n      s1 = s2;\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s0 = input.substring(s0, peg$currPos);\n    } else {\n      s0 = s1;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e12); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsesuffixAnnotation() {\n    var s0, s1, s2;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = input.charAt(peg$currPos);\n    if (peg$r6.test(s2)) {\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e20); }\n    }\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      if (s1.length >= 2) {\n        s2 = peg$FAILED;\n      } else {\n        s2 = input.charAt(peg$currPos);\n        if (peg$r6.test(s2)) {\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e20); }\n        }\n      }\n    }\n    if (s1.length < 1) {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    } else {\n      s0 = s1;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e19); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsenag() {\n    var s0, s2, s3, s4, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    peg$parse_();\n    if (input.charCodeAt(peg$currPos) === 36) {\n      s2 = peg$c8;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e22); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$currPos;\n      s4 = [];\n      s5 = input.charAt(peg$currPos);\n      if (peg$r2.test(s5)) {\n        peg$currPos++;\n      } else {\n        s5 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e9); }\n      }\n      if (s5 !== peg$FAILED) {\n        while (s5 !== peg$FAILED) {\n          s4.push(s5);\n          s5 = input.charAt(peg$currPos);\n          if (peg$r2.test(s5)) {\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e9); }\n          }\n        }\n      } else {\n        s4 = peg$FAILED;\n      }\n      if (s4 !== peg$FAILED) {\n        s3 = input.substring(s3, peg$currPos);\n      } else {\n        s3 = s4;\n      }\n      if (s3 !== peg$FAILED) {\n        s0 = peg$f6(s3);\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      if (peg$silentFails === 0) { peg$fail(peg$e21); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsecomment() {\n    var s0;\n\n    s0 = peg$parsebraceComment();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parserestOfLineComment();\n    }\n\n    return s0;\n  }\n\n  function peg$parsebraceComment() {\n    var s0, s1, s2, s3, s4;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    if (input.charCodeAt(peg$currPos) === 123) {\n      s1 = peg$c9;\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e24); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$currPos;\n      s3 = [];\n      s4 = input.charAt(peg$currPos);\n      if (peg$r7.test(s4)) {\n        peg$currPos++;\n      } else {\n        s4 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e25); }\n      }\n      while (s4 !== peg$FAILED) {\n        s3.push(s4);\n        s4 = input.charAt(peg$currPos);\n        if (peg$r7.test(s4)) {\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e25); }\n        }\n      }\n      s2 = input.substring(s2, peg$currPos);\n      if (input.charCodeAt(peg$currPos) === 125) {\n        s3 = peg$c10;\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e26); }\n      }\n      if (s3 !== peg$FAILED) {\n        s0 = peg$f7(s2);\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e23); }\n    }\n\n    return s0;\n  }\n\n  function peg$parserestOfLineComment() {\n    var s0, s1, s2, s3, s4;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    if (input.charCodeAt(peg$currPos) === 59) {\n      s1 = peg$c11;\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e28); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$currPos;\n      s3 = [];\n      s4 = input.charAt(peg$currPos);\n      if (peg$r8.test(s4)) {\n        peg$currPos++;\n      } else {\n        s4 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e29); }\n      }\n      while (s4 !== peg$FAILED) {\n        s3.push(s4);\n        s4 = input.charAt(peg$currPos);\n        if (peg$r8.test(s4)) {\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e29); }\n        }\n      }\n      s2 = input.substring(s2, peg$currPos);\n      s0 = peg$f8(s2);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e27); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsevariation() {\n    var s0, s2, s3, s5;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    peg$parse_();\n    if (input.charCodeAt(peg$currPos) === 40) {\n      s2 = peg$c12;\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e31); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseline();\n      if (s3 !== peg$FAILED) {\n        peg$parse_();\n        if (input.charCodeAt(peg$currPos) === 41) {\n          s5 = peg$c13;\n          peg$currPos++;\n        } else {\n          s5 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e32); }\n        }\n        if (s5 !== peg$FAILED) {\n          s0 = peg$f9(s3);\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      if (peg$silentFails === 0) { peg$fail(peg$e30); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsegameTerminationMarker() {\n    var s0, s1, s3;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 3) === peg$c14) {\n      s1 = peg$c14;\n      peg$currPos += 3;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e34); }\n    }\n    if (s1 === peg$FAILED) {\n      if (input.substr(peg$currPos, 3) === peg$c15) {\n        s1 = peg$c15;\n        peg$currPos += 3;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e35); }\n      }\n      if (s1 === peg$FAILED) {\n        if (input.substr(peg$currPos, 7) === peg$c16) {\n          s1 = peg$c16;\n          peg$currPos += 7;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$e36); }\n        }\n        if (s1 === peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 42) {\n            s1 = peg$c17;\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$e37); }\n          }\n        }\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$parse_();\n      s3 = peg$parsecomment();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      s0 = peg$f10(s1, s3);\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e33); }\n    }\n\n    return s0;\n  }\n\n  function peg$parse_() {\n    var s0, s1;\n\n    peg$silentFails++;\n    s0 = [];\n    s1 = input.charAt(peg$currPos);\n    if (peg$r9.test(s1)) {\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$e39); }\n    }\n    while (s1 !== peg$FAILED) {\n      s0.push(s1);\n      s1 = input.charAt(peg$currPos);\n      if (peg$r9.test(s1)) {\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$e39); }\n      }\n    }\n    peg$silentFails--;\n    s1 = peg$FAILED;\n    if (peg$silentFails === 0) { peg$fail(peg$e38); }\n\n    return s0;\n  }\n\n  peg$result = peg$startRuleFunction();\n\n  if (options.peg$library) {\n    return /** @type {any} */ ({\n      peg$result,\n      peg$currPos,\n      peg$FAILED,\n      peg$maxFailExpected,\n      peg$maxFailPos\n    });\n  }\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n\n    throw peg$buildStructuredError(\n      peg$maxFailExpected,\n      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n      peg$maxFailPos < input.length\n        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n    );\n  }\n}\n\n/**\n * @license\n * Copyright (c) 2025, Jeff Hlywa (<EMAIL>)\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n *    this list of conditions and the following disclaimer.\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n *    this list of conditions and the following disclaimer in the documentation\n *    and/or other materials provided with the distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\nconst MASK64 = 0xffffffffffffffffn;\nfunction rotl(x, k) {\n    return ((x << k) | (x >> (64n - k))) & 0xffffffffffffffffn;\n}\nfunction wrappingMul(x, y) {\n    return (x * y) & MASK64;\n}\n// xoroshiro128**\nfunction xoroshiro128(state) {\n    return function () {\n        let s0 = BigInt(state & MASK64);\n        let s1 = BigInt((state >> 64n) & MASK64);\n        const result = wrappingMul(rotl(wrappingMul(s0, 5n), 7n), 9n);\n        s1 ^= s0;\n        s0 = (rotl(s0, 24n) ^ s1 ^ (s1 << 16n)) & MASK64;\n        s1 = rotl(s1, 37n);\n        state = (s1 << 64n) | s0;\n        return result;\n    };\n}\nconst rand = xoroshiro128(0xa187eb39cdcaed8f31c4b365b102e01en);\nconst PIECE_KEYS = Array.from({ length: 2 }, () => Array.from({ length: 6 }, () => Array.from({ length: 128 }, () => rand())));\nconst EP_KEYS = Array.from({ length: 8 }, () => rand());\nconst CASTLING_KEYS = Array.from({ length: 16 }, () => rand());\nconst SIDE_KEY = rand();\nconst WHITE = 'w';\nconst BLACK = 'b';\nconst PAWN = 'p';\nconst KNIGHT = 'n';\nconst BISHOP = 'b';\nconst ROOK = 'r';\nconst QUEEN = 'q';\nconst KING = 'k';\nconst DEFAULT_POSITION = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';\nclass Move {\n    color;\n    from;\n    to;\n    piece;\n    captured;\n    promotion;\n    /**\n     * @deprecated This field is deprecated and will be removed in version 2.0.0.\n     * Please use move descriptor functions instead: `isCapture`, `isPromotion`,\n     * `isEnPassant`, `isKingsideCastle`, `isQueensideCastle`, `isCastle`, and\n     * `isBigPawn`\n     */\n    flags;\n    san;\n    lan;\n    before;\n    after;\n    constructor(chess, internal) {\n        const { color, piece, from, to, flags, captured, promotion } = internal;\n        const fromAlgebraic = algebraic(from);\n        const toAlgebraic = algebraic(to);\n        this.color = color;\n        this.piece = piece;\n        this.from = fromAlgebraic;\n        this.to = toAlgebraic;\n        /*\n         * HACK: The chess['_method']() calls below invoke private methods in the\n         * Chess class to generate SAN and FEN. It's a bit of a hack, but makes the\n         * code cleaner elsewhere.\n         */\n        this.san = chess['_moveToSan'](internal, chess['_moves']({ legal: true }));\n        this.lan = fromAlgebraic + toAlgebraic;\n        this.before = chess.fen();\n        // Generate the FEN for the 'after' key\n        chess['_makeMove'](internal);\n        this.after = chess.fen();\n        chess['_undoMove']();\n        // Build the text representation of the move flags\n        this.flags = '';\n        for (const flag in BITS) {\n            if (BITS[flag] & flags) {\n                this.flags += FLAGS[flag];\n            }\n        }\n        if (captured) {\n            this.captured = captured;\n        }\n        if (promotion) {\n            this.promotion = promotion;\n            this.lan += promotion;\n        }\n    }\n    isCapture() {\n        return this.flags.indexOf(FLAGS['CAPTURE']) > -1;\n    }\n    isPromotion() {\n        return this.flags.indexOf(FLAGS['PROMOTION']) > -1;\n    }\n    isEnPassant() {\n        return this.flags.indexOf(FLAGS['EP_CAPTURE']) > -1;\n    }\n    isKingsideCastle() {\n        return this.flags.indexOf(FLAGS['KSIDE_CASTLE']) > -1;\n    }\n    isQueensideCastle() {\n        return this.flags.indexOf(FLAGS['QSIDE_CASTLE']) > -1;\n    }\n    isBigPawn() {\n        return this.flags.indexOf(FLAGS['BIG_PAWN']) > -1;\n    }\n}\nconst EMPTY = -1;\nconst FLAGS = {\n    NORMAL: 'n',\n    CAPTURE: 'c',\n    BIG_PAWN: 'b',\n    EP_CAPTURE: 'e',\n    PROMOTION: 'p',\n    KSIDE_CASTLE: 'k',\n    QSIDE_CASTLE: 'q',\n    NULL_MOVE: '-',\n};\n// prettier-ignore\nconst SQUARES = [\n    'a8', 'b8', 'c8', 'd8', 'e8', 'f8', 'g8', 'h8',\n    'a7', 'b7', 'c7', 'd7', 'e7', 'f7', 'g7', 'h7',\n    'a6', 'b6', 'c6', 'd6', 'e6', 'f6', 'g6', 'h6',\n    'a5', 'b5', 'c5', 'd5', 'e5', 'f5', 'g5', 'h5',\n    'a4', 'b4', 'c4', 'd4', 'e4', 'f4', 'g4', 'h4',\n    'a3', 'b3', 'c3', 'd3', 'e3', 'f3', 'g3', 'h3',\n    'a2', 'b2', 'c2', 'd2', 'e2', 'f2', 'g2', 'h2',\n    'a1', 'b1', 'c1', 'd1', 'e1', 'f1', 'g1', 'h1'\n];\nconst BITS = {\n    NORMAL: 1,\n    CAPTURE: 2,\n    BIG_PAWN: 4,\n    EP_CAPTURE: 8,\n    PROMOTION: 16,\n    KSIDE_CASTLE: 32,\n    QSIDE_CASTLE: 64,\n    NULL_MOVE: 128,\n};\n/* eslint-disable @typescript-eslint/naming-convention */\n// these are required, according to spec\nconst SEVEN_TAG_ROSTER = {\n    Event: '?',\n    Site: '?',\n    Date: '????.??.??',\n    Round: '?',\n    White: '?',\n    Black: '?',\n    Result: '*',\n};\n/**\n * These nulls are placeholders to fix the order of tags (as they appear in PGN spec); null values will be\n * eliminated in getHeaders()\n */\nconst SUPLEMENTAL_TAGS = {\n    WhiteTitle: null,\n    BlackTitle: null,\n    WhiteElo: null,\n    BlackElo: null,\n    WhiteUSCF: null,\n    BlackUSCF: null,\n    WhiteNA: null,\n    BlackNA: null,\n    WhiteType: null,\n    BlackType: null,\n    EventDate: null,\n    EventSponsor: null,\n    Section: null,\n    Stage: null,\n    Board: null,\n    Opening: null,\n    Variation: null,\n    SubVariation: null,\n    ECO: null,\n    NIC: null,\n    Time: null,\n    UTCTime: null,\n    UTCDate: null,\n    TimeControl: null,\n    SetUp: null,\n    FEN: null,\n    Termination: null,\n    Annotator: null,\n    Mode: null,\n    PlyCount: null,\n};\nconst HEADER_TEMPLATE = {\n    ...SEVEN_TAG_ROSTER,\n    ...SUPLEMENTAL_TAGS,\n};\n/* eslint-enable @typescript-eslint/naming-convention */\n/*\n * NOTES ABOUT 0x88 MOVE GENERATION ALGORITHM\n * ----------------------------------------------------------------------------\n * From https://github.com/jhlywa/chess.js/issues/230\n *\n * A lot of people are confused when they first see the internal representation\n * of chess.js. It uses the 0x88 Move Generation Algorithm which internally\n * stores the board as an 8x16 array. This is purely for efficiency but has a\n * couple of interesting benefits:\n *\n * 1. 0x88 offers a very inexpensive \"off the board\" check. Bitwise AND (&) any\n *    square with 0x88, if the result is non-zero then the square is off the\n *    board. For example, assuming a knight square A8 (0 in 0x88 notation),\n *    there are 8 possible directions in which the knight can move. These\n *    directions are relative to the 8x16 board and are stored in the\n *    PIECE_OFFSETS map. One possible move is A8 - 18 (up one square, and two\n *    squares to the left - which is off the board). 0 - 18 = -18 & 0x88 = 0x88\n *    (because of two-complement representation of -18). The non-zero result\n *    means the square is off the board and the move is illegal. Take the\n *    opposite move (from A8 to C7), 0 + 18 = 18 & 0x88 = 0. A result of zero\n *    means the square is on the board.\n *\n * 2. The relative distance (or difference) between two squares on a 8x16 board\n *    is unique and can be used to inexpensively determine if a piece on a\n *    square can attack any other arbitrary square. For example, let's see if a\n *    pawn on E7 can attack E2. The difference between E7 (20) - E2 (100) is\n *    -80. We add 119 to make the ATTACKS array index non-negative (because the\n *    worst case difference is A8 - H1 = -119). The ATTACKS array contains a\n *    bitmask of pieces that can attack from that distance and direction.\n *    ATTACKS[-80 + 119=39] gives us 24 or 0b11000 in binary. Look at the\n *    PIECE_MASKS map to determine the mask for a given piece type. In our pawn\n *    example, we would check to see if 24 & 0x1 is non-zero, which it is\n *    not. So, naturally, a pawn on E7 can't attack a piece on E2. However, a\n *    rook can since 24 & 0x8 is non-zero. The only thing left to check is that\n *    there are no blocking pieces between E7 and E2. That's where the RAYS\n *    array comes in. It provides an offset (in this case 16) to add to E7 (20)\n *    to check for blocking pieces. E7 (20) + 16 = E6 (36) + 16 = E5 (52) etc.\n */\n// prettier-ignore\n// eslint-disable-next-line\nconst Ox88 = {\n    a8: 0, b8: 1, c8: 2, d8: 3, e8: 4, f8: 5, g8: 6, h8: 7,\n    a7: 16, b7: 17, c7: 18, d7: 19, e7: 20, f7: 21, g7: 22, h7: 23,\n    a6: 32, b6: 33, c6: 34, d6: 35, e6: 36, f6: 37, g6: 38, h6: 39,\n    a5: 48, b5: 49, c5: 50, d5: 51, e5: 52, f5: 53, g5: 54, h5: 55,\n    a4: 64, b4: 65, c4: 66, d4: 67, e4: 68, f4: 69, g4: 70, h4: 71,\n    a3: 80, b3: 81, c3: 82, d3: 83, e3: 84, f3: 85, g3: 86, h3: 87,\n    a2: 96, b2: 97, c2: 98, d2: 99, e2: 100, f2: 101, g2: 102, h2: 103,\n    a1: 112, b1: 113, c1: 114, d1: 115, e1: 116, f1: 117, g1: 118, h1: 119\n};\nconst PAWN_OFFSETS = {\n    b: [16, 32, 17, 15],\n    w: [-16, -32, -17, -15],\n};\nconst PIECE_OFFSETS = {\n    n: [-18, -33, -31, -14, 18, 33, 31, 14],\n    b: [-17, -15, 17, 15],\n    r: [-16, 1, 16, -1],\n    q: [-17, -16, -15, 1, 17, 16, 15, -1],\n    k: [-17, -16, -15, 1, 17, 16, 15, -1],\n};\n// prettier-ignore\nconst ATTACKS = [\n    20, 0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 20, 0,\n    0, 20, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 20, 0, 0,\n    0, 0, 20, 0, 0, 0, 0, 24, 0, 0, 0, 0, 20, 0, 0, 0,\n    0, 0, 0, 20, 0, 0, 0, 24, 0, 0, 0, 20, 0, 0, 0, 0,\n    0, 0, 0, 0, 20, 0, 0, 24, 0, 0, 20, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 0, 20, 2, 24, 2, 20, 0, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 0, 2, 53, 56, 53, 2, 0, 0, 0, 0, 0, 0,\n    24, 24, 24, 24, 24, 24, 56, 0, 56, 24, 24, 24, 24, 24, 24, 0,\n    0, 0, 0, 0, 0, 2, 53, 56, 53, 2, 0, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 0, 20, 2, 24, 2, 20, 0, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 20, 0, 0, 24, 0, 0, 20, 0, 0, 0, 0, 0,\n    0, 0, 0, 20, 0, 0, 0, 24, 0, 0, 0, 20, 0, 0, 0, 0,\n    0, 0, 20, 0, 0, 0, 0, 24, 0, 0, 0, 0, 20, 0, 0, 0,\n    0, 20, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 20, 0, 0,\n    20, 0, 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 20\n];\n// prettier-ignore\nconst RAYS = [\n    17, 0, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 0, 15, 0,\n    0, 17, 0, 0, 0, 0, 0, 16, 0, 0, 0, 0, 0, 15, 0, 0,\n    0, 0, 17, 0, 0, 0, 0, 16, 0, 0, 0, 0, 15, 0, 0, 0,\n    0, 0, 0, 17, 0, 0, 0, 16, 0, 0, 0, 15, 0, 0, 0, 0,\n    0, 0, 0, 0, 17, 0, 0, 16, 0, 0, 15, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 0, 17, 0, 16, 0, 15, 0, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 0, 0, 17, 16, 15, 0, 0, 0, 0, 0, 0, 0,\n    1, 1, 1, 1, 1, 1, 1, 0, -1, -1, -1, -1, -1, -1, -1, 0,\n    0, 0, 0, 0, 0, 0, -15, -16, -17, 0, 0, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, 0, -15, 0, -16, 0, -17, 0, 0, 0, 0, 0, 0,\n    0, 0, 0, 0, -15, 0, 0, -16, 0, 0, -17, 0, 0, 0, 0, 0,\n    0, 0, 0, -15, 0, 0, 0, -16, 0, 0, 0, -17, 0, 0, 0, 0,\n    0, 0, -15, 0, 0, 0, 0, -16, 0, 0, 0, 0, -17, 0, 0, 0,\n    0, -15, 0, 0, 0, 0, 0, -16, 0, 0, 0, 0, 0, -17, 0, 0,\n    -15, 0, 0, 0, 0, 0, 0, -16, 0, 0, 0, 0, 0, 0, -17\n];\nconst PIECE_MASKS = { p: 0x1, n: 0x2, b: 0x4, r: 0x8, q: 0x10, k: 0x20 };\nconst SYMBOLS = 'pnbrqkPNBRQK';\nconst PROMOTIONS = [KNIGHT, BISHOP, ROOK, QUEEN];\nconst RANK_1 = 7;\nconst RANK_2 = 6;\n/*\n * const RANK_3 = 5\n * const RANK_4 = 4\n * const RANK_5 = 3\n * const RANK_6 = 2\n */\nconst RANK_7 = 1;\nconst RANK_8 = 0;\nconst SIDES = {\n    [KING]: BITS.KSIDE_CASTLE,\n    [QUEEN]: BITS.QSIDE_CASTLE,\n};\nconst ROOKS = {\n    w: [\n        { square: Ox88.a1, flag: BITS.QSIDE_CASTLE },\n        { square: Ox88.h1, flag: BITS.KSIDE_CASTLE },\n    ],\n    b: [\n        { square: Ox88.a8, flag: BITS.QSIDE_CASTLE },\n        { square: Ox88.h8, flag: BITS.KSIDE_CASTLE },\n    ],\n};\nconst SECOND_RANK = { b: RANK_7, w: RANK_2 };\nconst SAN_NULLMOVE = '--';\n// Extracts the zero-based rank of an 0x88 square.\nfunction rank(square) {\n    return square >> 4;\n}\n// Extracts the zero-based file of an 0x88 square.\nfunction file(square) {\n    return square & 0xf;\n}\nfunction isDigit(c) {\n    return '0123456789'.indexOf(c) !== -1;\n}\n// Converts a 0x88 square to algebraic notation.\nfunction algebraic(square) {\n    const f = file(square);\n    const r = rank(square);\n    return ('abcdefgh'.substring(f, f + 1) +\n        '87654321'.substring(r, r + 1));\n}\nfunction swapColor(color) {\n    return color === WHITE ? BLACK : WHITE;\n}\nfunction validateFen(fen) {\n    // 1st criterion: 6 space-seperated fields?\n    const tokens = fen.split(/\\s+/);\n    if (tokens.length !== 6) {\n        return {\n            ok: false,\n            error: 'Invalid FEN: must contain six space-delimited fields',\n        };\n    }\n    // 2nd criterion: move number field is a integer value > 0?\n    const moveNumber = parseInt(tokens[5], 10);\n    if (isNaN(moveNumber) || moveNumber <= 0) {\n        return {\n            ok: false,\n            error: 'Invalid FEN: move number must be a positive integer',\n        };\n    }\n    // 3rd criterion: half move counter is an integer >= 0?\n    const halfMoves = parseInt(tokens[4], 10);\n    if (isNaN(halfMoves) || halfMoves < 0) {\n        return {\n            ok: false,\n            error: 'Invalid FEN: half move counter number must be a non-negative integer',\n        };\n    }\n    // 4th criterion: 4th field is a valid e.p.-string?\n    if (!/^(-|[abcdefgh][36])$/.test(tokens[3])) {\n        return { ok: false, error: 'Invalid FEN: en-passant square is invalid' };\n    }\n    // 5th criterion: 3th field is a valid castle-string?\n    if (/[^kKqQ-]/.test(tokens[2])) {\n        return { ok: false, error: 'Invalid FEN: castling availability is invalid' };\n    }\n    // 6th criterion: 2nd field is \"w\" (white) or \"b\" (black)?\n    if (!/^(w|b)$/.test(tokens[1])) {\n        return { ok: false, error: 'Invalid FEN: side-to-move is invalid' };\n    }\n    // 7th criterion: 1st field contains 8 rows?\n    const rows = tokens[0].split('/');\n    if (rows.length !== 8) {\n        return {\n            ok: false,\n            error: \"Invalid FEN: piece data does not contain 8 '/'-delimited rows\",\n        };\n    }\n    // 8th criterion: every row is valid?\n    for (let i = 0; i < rows.length; i++) {\n        // check for right sum of fields AND not two numbers in succession\n        let sumFields = 0;\n        let previousWasNumber = false;\n        for (let k = 0; k < rows[i].length; k++) {\n            if (isDigit(rows[i][k])) {\n                if (previousWasNumber) {\n                    return {\n                        ok: false,\n                        error: 'Invalid FEN: piece data is invalid (consecutive number)',\n                    };\n                }\n                sumFields += parseInt(rows[i][k], 10);\n                previousWasNumber = true;\n            }\n            else {\n                if (!/^[prnbqkPRNBQK]$/.test(rows[i][k])) {\n                    return {\n                        ok: false,\n                        error: 'Invalid FEN: piece data is invalid (invalid piece)',\n                    };\n                }\n                sumFields += 1;\n                previousWasNumber = false;\n            }\n        }\n        if (sumFields !== 8) {\n            return {\n                ok: false,\n                error: 'Invalid FEN: piece data is invalid (too many squares in rank)',\n            };\n        }\n    }\n    // 9th criterion: is en-passant square legal?\n    if ((tokens[3][1] == '3' && tokens[1] == 'w') ||\n        (tokens[3][1] == '6' && tokens[1] == 'b')) {\n        return { ok: false, error: 'Invalid FEN: illegal en-passant square' };\n    }\n    // 10th criterion: does chess position contain exact two kings?\n    const kings = [\n        { color: 'white', regex: /K/g },\n        { color: 'black', regex: /k/g },\n    ];\n    for (const { color, regex } of kings) {\n        if (!regex.test(tokens[0])) {\n            return { ok: false, error: `Invalid FEN: missing ${color} king` };\n        }\n        if ((tokens[0].match(regex) || []).length > 1) {\n            return { ok: false, error: `Invalid FEN: too many ${color} kings` };\n        }\n    }\n    // 11th criterion: are any pawns on the first or eighth rows?\n    if (Array.from(rows[0] + rows[7]).some((char) => char.toUpperCase() === 'P')) {\n        return {\n            ok: false,\n            error: 'Invalid FEN: some pawns are on the edge rows',\n        };\n    }\n    return { ok: true };\n}\n// this function is used to uniquely identify ambiguous moves\nfunction getDisambiguator(move, moves) {\n    const from = move.from;\n    const to = move.to;\n    const piece = move.piece;\n    let ambiguities = 0;\n    let sameRank = 0;\n    let sameFile = 0;\n    for (let i = 0, len = moves.length; i < len; i++) {\n        const ambigFrom = moves[i].from;\n        const ambigTo = moves[i].to;\n        const ambigPiece = moves[i].piece;\n        /*\n         * if a move of the same piece type ends on the same to square, we'll need\n         * to add a disambiguator to the algebraic notation\n         */\n        if (piece === ambigPiece && from !== ambigFrom && to === ambigTo) {\n            ambiguities++;\n            if (rank(from) === rank(ambigFrom)) {\n                sameRank++;\n            }\n            if (file(from) === file(ambigFrom)) {\n                sameFile++;\n            }\n        }\n    }\n    if (ambiguities > 0) {\n        if (sameRank > 0 && sameFile > 0) {\n            /*\n             * if there exists a similar moving piece on the same rank and file as\n             * the move in question, use the square as the disambiguator\n             */\n            return algebraic(from);\n        }\n        else if (sameFile > 0) {\n            /*\n             * if the moving piece rests on the same file, use the rank symbol as the\n             * disambiguator\n             */\n            return algebraic(from).charAt(1);\n        }\n        else {\n            // else use the file symbol\n            return algebraic(from).charAt(0);\n        }\n    }\n    return '';\n}\nfunction addMove(moves, color, from, to, piece, captured = undefined, flags = BITS.NORMAL) {\n    const r = rank(to);\n    if (piece === PAWN && (r === RANK_1 || r === RANK_8)) {\n        for (let i = 0; i < PROMOTIONS.length; i++) {\n            const promotion = PROMOTIONS[i];\n            moves.push({\n                color,\n                from,\n                to,\n                piece,\n                captured,\n                promotion,\n                flags: flags | BITS.PROMOTION,\n            });\n        }\n    }\n    else {\n        moves.push({\n            color,\n            from,\n            to,\n            piece,\n            captured,\n            flags,\n        });\n    }\n}\nfunction inferPieceType(san) {\n    let pieceType = san.charAt(0);\n    if (pieceType >= 'a' && pieceType <= 'h') {\n        const matches = san.match(/[a-h]\\d.*[a-h]\\d/);\n        if (matches) {\n            return undefined;\n        }\n        return PAWN;\n    }\n    pieceType = pieceType.toLowerCase();\n    if (pieceType === 'o') {\n        return KING;\n    }\n    return pieceType;\n}\n// parses all of the decorators out of a SAN string\nfunction strippedSan(move) {\n    return move.replace(/=/, '').replace(/[+#]?[?!]*$/, '');\n}\nclass Chess {\n    _board = new Array(128);\n    _turn = WHITE;\n    _header = {};\n    _kings = { w: EMPTY, b: EMPTY };\n    _epSquare = -1;\n    _halfMoves = 0;\n    _moveNumber = 0;\n    _history = [];\n    _comments = {};\n    _castling = { w: 0, b: 0 };\n    _hash = 0n;\n    // tracks number of times a position has been seen for repetition checking\n    _positionCount = new Map();\n    constructor(fen = DEFAULT_POSITION, { skipValidation = false } = {}) {\n        this.load(fen, { skipValidation });\n    }\n    clear({ preserveHeaders = false } = {}) {\n        this._board = new Array(128);\n        this._kings = { w: EMPTY, b: EMPTY };\n        this._turn = WHITE;\n        this._castling = { w: 0, b: 0 };\n        this._epSquare = EMPTY;\n        this._halfMoves = 0;\n        this._moveNumber = 1;\n        this._history = [];\n        this._comments = {};\n        this._header = preserveHeaders ? this._header : { ...HEADER_TEMPLATE };\n        this._hash = this._computeHash();\n        this._positionCount = new Map();\n        /*\n         * Delete the SetUp and FEN headers (if preserved), the board is empty and\n         * these headers don't make sense in this state. They'll get added later\n         * via .load() or .put()\n         */\n        this._header['SetUp'] = null;\n        this._header['FEN'] = null;\n    }\n    load(fen, { skipValidation = false, preserveHeaders = false } = {}) {\n        let tokens = fen.split(/\\s+/);\n        // append commonly omitted fen tokens\n        if (tokens.length >= 2 && tokens.length < 6) {\n            const adjustments = ['-', '-', '0', '1'];\n            fen = tokens.concat(adjustments.slice(-(6 - tokens.length))).join(' ');\n        }\n        tokens = fen.split(/\\s+/);\n        if (!skipValidation) {\n            const { ok, error } = validateFen(fen);\n            if (!ok) {\n                throw new Error(error);\n            }\n        }\n        const position = tokens[0];\n        let square = 0;\n        this.clear({ preserveHeaders });\n        for (let i = 0; i < position.length; i++) {\n            const piece = position.charAt(i);\n            if (piece === '/') {\n                square += 8;\n            }\n            else if (isDigit(piece)) {\n                square += parseInt(piece, 10);\n            }\n            else {\n                const color = piece < 'a' ? WHITE : BLACK;\n                this._put({ type: piece.toLowerCase(), color }, algebraic(square));\n                square++;\n            }\n        }\n        this._turn = tokens[1];\n        if (tokens[2].indexOf('K') > -1) {\n            this._castling.w |= BITS.KSIDE_CASTLE;\n        }\n        if (tokens[2].indexOf('Q') > -1) {\n            this._castling.w |= BITS.QSIDE_CASTLE;\n        }\n        if (tokens[2].indexOf('k') > -1) {\n            this._castling.b |= BITS.KSIDE_CASTLE;\n        }\n        if (tokens[2].indexOf('q') > -1) {\n            this._castling.b |= BITS.QSIDE_CASTLE;\n        }\n        this._epSquare = tokens[3] === '-' ? EMPTY : Ox88[tokens[3]];\n        this._halfMoves = parseInt(tokens[4], 10);\n        this._moveNumber = parseInt(tokens[5], 10);\n        this._hash = this._computeHash();\n        this._updateSetup(fen);\n        this._incPositionCount();\n    }\n    fen({ forceEnpassantSquare = false, } = {}) {\n        let empty = 0;\n        let fen = '';\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            if (this._board[i]) {\n                if (empty > 0) {\n                    fen += empty;\n                    empty = 0;\n                }\n                const { color, type: piece } = this._board[i];\n                fen += color === WHITE ? piece.toUpperCase() : piece.toLowerCase();\n            }\n            else {\n                empty++;\n            }\n            if ((i + 1) & 0x88) {\n                if (empty > 0) {\n                    fen += empty;\n                }\n                if (i !== Ox88.h1) {\n                    fen += '/';\n                }\n                empty = 0;\n                i += 8;\n            }\n        }\n        let castling = '';\n        if (this._castling[WHITE] & BITS.KSIDE_CASTLE) {\n            castling += 'K';\n        }\n        if (this._castling[WHITE] & BITS.QSIDE_CASTLE) {\n            castling += 'Q';\n        }\n        if (this._castling[BLACK] & BITS.KSIDE_CASTLE) {\n            castling += 'k';\n        }\n        if (this._castling[BLACK] & BITS.QSIDE_CASTLE) {\n            castling += 'q';\n        }\n        // do we have an empty castling flag?\n        castling = castling || '-';\n        let epSquare = '-';\n        /*\n         * only print the ep square if en passant is a valid move (pawn is present\n         * and ep capture is not pinned)\n         */\n        if (this._epSquare !== EMPTY) {\n            if (forceEnpassantSquare) {\n                epSquare = algebraic(this._epSquare);\n            }\n            else {\n                const bigPawnSquare = this._epSquare + (this._turn === WHITE ? 16 : -16);\n                const squares = [bigPawnSquare + 1, bigPawnSquare - 1];\n                for (const square of squares) {\n                    // is the square off the board?\n                    if (square & 0x88) {\n                        continue;\n                    }\n                    const color = this._turn;\n                    // is there a pawn that can capture the epSquare?\n                    if (this._board[square]?.color === color &&\n                        this._board[square]?.type === PAWN) {\n                        // if the pawn makes an ep capture, does it leave its king in check?\n                        this._makeMove({\n                            color,\n                            from: square,\n                            to: this._epSquare,\n                            piece: PAWN,\n                            captured: PAWN,\n                            flags: BITS.EP_CAPTURE,\n                        });\n                        const isLegal = !this._isKingAttacked(color);\n                        this._undoMove();\n                        // if ep is legal, break and set the ep square in the FEN output\n                        if (isLegal) {\n                            epSquare = algebraic(this._epSquare);\n                            break;\n                        }\n                    }\n                }\n            }\n        }\n        return [\n            fen,\n            this._turn,\n            castling,\n            epSquare,\n            this._halfMoves,\n            this._moveNumber,\n        ].join(' ');\n    }\n    _pieceKey(i) {\n        if (!this._board[i]) {\n            return 0n;\n        }\n        const { color, type } = this._board[i];\n        const colorIndex = {\n            w: 0,\n            b: 1,\n        }[color];\n        const typeIndex = {\n            p: 0,\n            n: 1,\n            b: 2,\n            r: 3,\n            q: 4,\n            k: 5,\n        }[type];\n        return PIECE_KEYS[colorIndex][typeIndex][i];\n    }\n    _epKey() {\n        return this._epSquare === EMPTY ? 0n : EP_KEYS[this._epSquare & 7];\n    }\n    _castlingKey() {\n        const index = (this._castling.w >> 5) | (this._castling.b >> 3);\n        return CASTLING_KEYS[index];\n    }\n    _computeHash() {\n        let hash = 0n;\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            // did we run off the end of the board\n            if (i & 0x88) {\n                i += 7;\n                continue;\n            }\n            if (this._board[i]) {\n                hash ^= this._pieceKey(i);\n            }\n        }\n        hash ^= this._epKey();\n        hash ^= this._castlingKey();\n        if (this._turn === 'b') {\n            hash ^= SIDE_KEY;\n        }\n        return hash;\n    }\n    /*\n     * Called when the initial board setup is changed with put() or remove().\n     * modifies the SetUp and FEN properties of the header object. If the FEN\n     * is equal to the default position, the SetUp and FEN are deleted the setup\n     * is only updated if history.length is zero, ie moves haven't been made.\n     */\n    _updateSetup(fen) {\n        if (this._history.length > 0)\n            return;\n        if (fen !== DEFAULT_POSITION) {\n            this._header['SetUp'] = '1';\n            this._header['FEN'] = fen;\n        }\n        else {\n            this._header['SetUp'] = null;\n            this._header['FEN'] = null;\n        }\n    }\n    reset() {\n        this.load(DEFAULT_POSITION);\n    }\n    get(square) {\n        return this._board[Ox88[square]];\n    }\n    findPiece(piece) {\n        const squares = [];\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            // did we run off the end of the board\n            if (i & 0x88) {\n                i += 7;\n                continue;\n            }\n            // if empty square or wrong color\n            if (!this._board[i] || this._board[i]?.color !== piece.color) {\n                continue;\n            }\n            // check if square contains the requested piece\n            if (this._board[i].color === piece.color &&\n                this._board[i].type === piece.type) {\n                squares.push(algebraic(i));\n            }\n        }\n        return squares;\n    }\n    put({ type, color }, square) {\n        if (this._put({ type, color }, square)) {\n            this._updateCastlingRights();\n            this._updateEnPassantSquare();\n            this._updateSetup(this.fen());\n            return true;\n        }\n        return false;\n    }\n    _set(sq, piece) {\n        this._hash ^= this._pieceKey(sq);\n        this._board[sq] = piece;\n        this._hash ^= this._pieceKey(sq);\n    }\n    _put({ type, color }, square) {\n        // check for piece\n        if (SYMBOLS.indexOf(type.toLowerCase()) === -1) {\n            return false;\n        }\n        // check for valid square\n        if (!(square in Ox88)) {\n            return false;\n        }\n        const sq = Ox88[square];\n        // don't let the user place more than one king\n        if (type == KING &&\n            !(this._kings[color] == EMPTY || this._kings[color] == sq)) {\n            return false;\n        }\n        const currentPieceOnSquare = this._board[sq];\n        // if one of the kings will be replaced by the piece from args, set the `_kings` respective entry to `EMPTY`\n        if (currentPieceOnSquare && currentPieceOnSquare.type === KING) {\n            this._kings[currentPieceOnSquare.color] = EMPTY;\n        }\n        this._set(sq, { type: type, color: color });\n        if (type === KING) {\n            this._kings[color] = sq;\n        }\n        return true;\n    }\n    _clear(sq) {\n        this._hash ^= this._pieceKey(sq);\n        delete this._board[sq];\n    }\n    remove(square) {\n        const piece = this.get(square);\n        this._clear(Ox88[square]);\n        if (piece && piece.type === KING) {\n            this._kings[piece.color] = EMPTY;\n        }\n        this._updateCastlingRights();\n        this._updateEnPassantSquare();\n        this._updateSetup(this.fen());\n        return piece;\n    }\n    _updateCastlingRights() {\n        this._hash ^= this._castlingKey();\n        const whiteKingInPlace = this._board[Ox88.e1]?.type === KING &&\n            this._board[Ox88.e1]?.color === WHITE;\n        const blackKingInPlace = this._board[Ox88.e8]?.type === KING &&\n            this._board[Ox88.e8]?.color === BLACK;\n        if (!whiteKingInPlace ||\n            this._board[Ox88.a1]?.type !== ROOK ||\n            this._board[Ox88.a1]?.color !== WHITE) {\n            this._castling.w &= -65;\n        }\n        if (!whiteKingInPlace ||\n            this._board[Ox88.h1]?.type !== ROOK ||\n            this._board[Ox88.h1]?.color !== WHITE) {\n            this._castling.w &= -33;\n        }\n        if (!blackKingInPlace ||\n            this._board[Ox88.a8]?.type !== ROOK ||\n            this._board[Ox88.a8]?.color !== BLACK) {\n            this._castling.b &= -65;\n        }\n        if (!blackKingInPlace ||\n            this._board[Ox88.h8]?.type !== ROOK ||\n            this._board[Ox88.h8]?.color !== BLACK) {\n            this._castling.b &= -33;\n        }\n        this._hash ^= this._castlingKey();\n    }\n    _updateEnPassantSquare() {\n        if (this._epSquare === EMPTY) {\n            return;\n        }\n        const startSquare = this._epSquare + (this._turn === WHITE ? -16 : 16);\n        const currentSquare = this._epSquare + (this._turn === WHITE ? 16 : -16);\n        const attackers = [currentSquare + 1, currentSquare - 1];\n        if (this._board[startSquare] !== null ||\n            this._board[this._epSquare] !== null ||\n            this._board[currentSquare]?.color !== swapColor(this._turn) ||\n            this._board[currentSquare]?.type !== PAWN) {\n            this._hash ^= this._epKey();\n            this._epSquare = EMPTY;\n            return;\n        }\n        const canCapture = (square) => !(square & 0x88) &&\n            this._board[square]?.color === this._turn &&\n            this._board[square]?.type === PAWN;\n        if (!attackers.some(canCapture)) {\n            this._hash ^= this._epKey();\n            this._epSquare = EMPTY;\n        }\n    }\n    _attacked(color, square, verbose) {\n        const attackers = [];\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            // did we run off the end of the board\n            if (i & 0x88) {\n                i += 7;\n                continue;\n            }\n            // if empty square or wrong color\n            if (this._board[i] === undefined || this._board[i].color !== color) {\n                continue;\n            }\n            const piece = this._board[i];\n            const difference = i - square;\n            // skip - to/from square are the same\n            if (difference === 0) {\n                continue;\n            }\n            const index = difference + 119;\n            if (ATTACKS[index] & PIECE_MASKS[piece.type]) {\n                if (piece.type === PAWN) {\n                    if ((difference > 0 && piece.color === WHITE) ||\n                        (difference <= 0 && piece.color === BLACK)) {\n                        if (!verbose) {\n                            return true;\n                        }\n                        else {\n                            attackers.push(algebraic(i));\n                        }\n                    }\n                    continue;\n                }\n                // if the piece is a knight or a king\n                if (piece.type === 'n' || piece.type === 'k') {\n                    if (!verbose) {\n                        return true;\n                    }\n                    else {\n                        attackers.push(algebraic(i));\n                        continue;\n                    }\n                }\n                const offset = RAYS[index];\n                let j = i + offset;\n                let blocked = false;\n                while (j !== square) {\n                    if (this._board[j] != null) {\n                        blocked = true;\n                        break;\n                    }\n                    j += offset;\n                }\n                if (!blocked) {\n                    if (!verbose) {\n                        return true;\n                    }\n                    else {\n                        attackers.push(algebraic(i));\n                        continue;\n                    }\n                }\n            }\n        }\n        if (verbose) {\n            return attackers;\n        }\n        else {\n            return false;\n        }\n    }\n    attackers(square, attackedBy) {\n        if (!attackedBy) {\n            return this._attacked(this._turn, Ox88[square], true);\n        }\n        else {\n            return this._attacked(attackedBy, Ox88[square], true);\n        }\n    }\n    _isKingAttacked(color) {\n        const square = this._kings[color];\n        return square === -1 ? false : this._attacked(swapColor(color), square);\n    }\n    hash() {\n        return this._hash.toString(16);\n    }\n    isAttacked(square, attackedBy) {\n        return this._attacked(attackedBy, Ox88[square]);\n    }\n    isCheck() {\n        return this._isKingAttacked(this._turn);\n    }\n    inCheck() {\n        return this.isCheck();\n    }\n    isCheckmate() {\n        return this.isCheck() && this._moves().length === 0;\n    }\n    isStalemate() {\n        return !this.isCheck() && this._moves().length === 0;\n    }\n    isInsufficientMaterial() {\n        /*\n         * k.b. vs k.b. (of opposite colors) with mate in 1:\n         * 8/8/8/8/1b6/8/B1k5/K7 b - - 0 1\n         *\n         * k.b. vs k.n. with mate in 1:\n         * 8/8/8/8/1n6/8/B7/K1k5 b - - 2 1\n         */\n        const pieces = {\n            b: 0,\n            n: 0,\n            r: 0,\n            q: 0,\n            k: 0,\n            p: 0,\n        };\n        const bishops = [];\n        let numPieces = 0;\n        let squareColor = 0;\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            squareColor = (squareColor + 1) % 2;\n            if (i & 0x88) {\n                i += 7;\n                continue;\n            }\n            const piece = this._board[i];\n            if (piece) {\n                pieces[piece.type] = piece.type in pieces ? pieces[piece.type] + 1 : 1;\n                if (piece.type === BISHOP) {\n                    bishops.push(squareColor);\n                }\n                numPieces++;\n            }\n        }\n        // k vs. k\n        if (numPieces === 2) {\n            return true;\n        }\n        else if (\n        // k vs. kn .... or .... k vs. kb\n        numPieces === 3 &&\n            (pieces[BISHOP] === 1 || pieces[KNIGHT] === 1)) {\n            return true;\n        }\n        else if (numPieces === pieces[BISHOP] + 2) {\n            // kb vs. kb where any number of bishops are all on the same color\n            let sum = 0;\n            const len = bishops.length;\n            for (let i = 0; i < len; i++) {\n                sum += bishops[i];\n            }\n            if (sum === 0 || sum === len) {\n                return true;\n            }\n        }\n        return false;\n    }\n    isThreefoldRepetition() {\n        return this._getPositionCount(this._hash) >= 3;\n    }\n    isDrawByFiftyMoves() {\n        return this._halfMoves >= 100; // 50 moves per side = 100 half moves\n    }\n    isDraw() {\n        return (this.isDrawByFiftyMoves() ||\n            this.isStalemate() ||\n            this.isInsufficientMaterial() ||\n            this.isThreefoldRepetition());\n    }\n    isGameOver() {\n        return this.isCheckmate() || this.isDraw();\n    }\n    moves({ verbose = false, square = undefined, piece = undefined, } = {}) {\n        const moves = this._moves({ square, piece });\n        if (verbose) {\n            return moves.map((move) => new Move(this, move));\n        }\n        else {\n            return moves.map((move) => this._moveToSan(move, moves));\n        }\n    }\n    _moves({ legal = true, piece = undefined, square = undefined, } = {}) {\n        const forSquare = square ? square.toLowerCase() : undefined;\n        const forPiece = piece?.toLowerCase();\n        const moves = [];\n        const us = this._turn;\n        const them = swapColor(us);\n        let firstSquare = Ox88.a8;\n        let lastSquare = Ox88.h1;\n        let singleSquare = false;\n        // are we generating moves for a single square?\n        if (forSquare) {\n            // illegal square, return empty moves\n            if (!(forSquare in Ox88)) {\n                return [];\n            }\n            else {\n                firstSquare = lastSquare = Ox88[forSquare];\n                singleSquare = true;\n            }\n        }\n        for (let from = firstSquare; from <= lastSquare; from++) {\n            // did we run off the end of the board\n            if (from & 0x88) {\n                from += 7;\n                continue;\n            }\n            // empty square or opponent, skip\n            if (!this._board[from] || this._board[from].color === them) {\n                continue;\n            }\n            const { type } = this._board[from];\n            let to;\n            if (type === PAWN) {\n                if (forPiece && forPiece !== type)\n                    continue;\n                // single square, non-capturing\n                to = from + PAWN_OFFSETS[us][0];\n                if (!this._board[to]) {\n                    addMove(moves, us, from, to, PAWN);\n                    // double square\n                    to = from + PAWN_OFFSETS[us][1];\n                    if (SECOND_RANK[us] === rank(from) && !this._board[to]) {\n                        addMove(moves, us, from, to, PAWN, undefined, BITS.BIG_PAWN);\n                    }\n                }\n                // pawn captures\n                for (let j = 2; j < 4; j++) {\n                    to = from + PAWN_OFFSETS[us][j];\n                    if (to & 0x88)\n                        continue;\n                    if (this._board[to]?.color === them) {\n                        addMove(moves, us, from, to, PAWN, this._board[to].type, BITS.CAPTURE);\n                    }\n                    else if (to === this._epSquare) {\n                        addMove(moves, us, from, to, PAWN, PAWN, BITS.EP_CAPTURE);\n                    }\n                }\n            }\n            else {\n                if (forPiece && forPiece !== type)\n                    continue;\n                for (let j = 0, len = PIECE_OFFSETS[type].length; j < len; j++) {\n                    const offset = PIECE_OFFSETS[type][j];\n                    to = from;\n                    while (true) {\n                        to += offset;\n                        if (to & 0x88)\n                            break;\n                        if (!this._board[to]) {\n                            addMove(moves, us, from, to, type);\n                        }\n                        else {\n                            // own color, stop loop\n                            if (this._board[to].color === us)\n                                break;\n                            addMove(moves, us, from, to, type, this._board[to].type, BITS.CAPTURE);\n                            break;\n                        }\n                        /* break, if knight or king */\n                        if (type === KNIGHT || type === KING)\n                            break;\n                    }\n                }\n            }\n        }\n        /*\n         * check for castling if we're:\n         *   a) generating all moves, or\n         *   b) doing single square move generation on the king's square\n         */\n        if (forPiece === undefined || forPiece === KING) {\n            if (!singleSquare || lastSquare === this._kings[us]) {\n                // king-side castling\n                if (this._castling[us] & BITS.KSIDE_CASTLE) {\n                    const castlingFrom = this._kings[us];\n                    const castlingTo = castlingFrom + 2;\n                    if (!this._board[castlingFrom + 1] &&\n                        !this._board[castlingTo] &&\n                        !this._attacked(them, this._kings[us]) &&\n                        !this._attacked(them, castlingFrom + 1) &&\n                        !this._attacked(them, castlingTo)) {\n                        addMove(moves, us, this._kings[us], castlingTo, KING, undefined, BITS.KSIDE_CASTLE);\n                    }\n                }\n                // queen-side castling\n                if (this._castling[us] & BITS.QSIDE_CASTLE) {\n                    const castlingFrom = this._kings[us];\n                    const castlingTo = castlingFrom - 2;\n                    if (!this._board[castlingFrom - 1] &&\n                        !this._board[castlingFrom - 2] &&\n                        !this._board[castlingFrom - 3] &&\n                        !this._attacked(them, this._kings[us]) &&\n                        !this._attacked(them, castlingFrom - 1) &&\n                        !this._attacked(them, castlingTo)) {\n                        addMove(moves, us, this._kings[us], castlingTo, KING, undefined, BITS.QSIDE_CASTLE);\n                    }\n                }\n            }\n        }\n        /*\n         * return all pseudo-legal moves (this includes moves that allow the king\n         * to be captured)\n         */\n        if (!legal || this._kings[us] === -1) {\n            return moves;\n        }\n        // filter out illegal moves\n        const legalMoves = [];\n        for (let i = 0, len = moves.length; i < len; i++) {\n            this._makeMove(moves[i]);\n            if (!this._isKingAttacked(us)) {\n                legalMoves.push(moves[i]);\n            }\n            this._undoMove();\n        }\n        return legalMoves;\n    }\n    move(move, { strict = false } = {}) {\n        /*\n         * The move function can be called with in the following parameters:\n         *\n         * .move('Nxb7')       <- argument is a case-sensitive SAN string\n         *\n         * .move({ from: 'h7', <- argument is a move object\n         *         to :'h8',\n         *         promotion: 'q' })\n         *\n         *\n         * An optional strict argument may be supplied to tell chess.js to\n         * strictly follow the SAN specification.\n         */\n        let moveObj = null;\n        if (typeof move === 'string') {\n            moveObj = this._moveFromSan(move, strict);\n        }\n        else if (move === null) {\n            moveObj = this._moveFromSan(SAN_NULLMOVE, strict);\n        }\n        else if (typeof move === 'object') {\n            const moves = this._moves();\n            // convert the pretty move object to an ugly move object\n            for (let i = 0, len = moves.length; i < len; i++) {\n                if (move.from === algebraic(moves[i].from) &&\n                    move.to === algebraic(moves[i].to) &&\n                    (!('promotion' in moves[i]) || move.promotion === moves[i].promotion)) {\n                    moveObj = moves[i];\n                    break;\n                }\n            }\n        }\n        // failed to find move\n        if (!moveObj) {\n            if (typeof move === 'string') {\n                throw new Error(`Invalid move: ${move}`);\n            }\n            else {\n                throw new Error(`Invalid move: ${JSON.stringify(move)}`);\n            }\n        }\n        //disallow null moves when in check\n        if (this.isCheck() && moveObj.flags & BITS.NULL_MOVE) {\n            throw new Error('Null move not allowed when in check');\n        }\n        /*\n         * need to make a copy of move because we can't generate SAN after the move\n         * is made\n         */\n        const prettyMove = new Move(this, moveObj);\n        this._makeMove(moveObj);\n        this._incPositionCount();\n        return prettyMove;\n    }\n    _push(move) {\n        this._history.push({\n            move,\n            kings: { b: this._kings.b, w: this._kings.w },\n            turn: this._turn,\n            castling: { b: this._castling.b, w: this._castling.w },\n            epSquare: this._epSquare,\n            halfMoves: this._halfMoves,\n            moveNumber: this._moveNumber,\n        });\n    }\n    _movePiece(from, to) {\n        this._hash ^= this._pieceKey(from);\n        this._board[to] = this._board[from];\n        delete this._board[from];\n        this._hash ^= this._pieceKey(to);\n    }\n    _makeMove(move) {\n        const us = this._turn;\n        const them = swapColor(us);\n        this._push(move);\n        if (move.flags & BITS.NULL_MOVE) {\n            if (us === BLACK) {\n                this._moveNumber++;\n            }\n            this._halfMoves++;\n            this._turn = them;\n            this._epSquare = EMPTY;\n            return;\n        }\n        this._hash ^= this._epKey();\n        this._hash ^= this._castlingKey();\n        if (move.captured) {\n            this._hash ^= this._pieceKey(move.to);\n        }\n        this._movePiece(move.from, move.to);\n        // if ep capture, remove the captured pawn\n        if (move.flags & BITS.EP_CAPTURE) {\n            if (this._turn === BLACK) {\n                this._clear(move.to - 16);\n            }\n            else {\n                this._clear(move.to + 16);\n            }\n        }\n        // if pawn promotion, replace with new piece\n        if (move.promotion) {\n            this._clear(move.to);\n            this._set(move.to, { type: move.promotion, color: us });\n        }\n        // if we moved the king\n        if (this._board[move.to].type === KING) {\n            this._kings[us] = move.to;\n            // if we castled, move the rook next to the king\n            if (move.flags & BITS.KSIDE_CASTLE) {\n                const castlingTo = move.to - 1;\n                const castlingFrom = move.to + 1;\n                this._movePiece(castlingFrom, castlingTo);\n            }\n            else if (move.flags & BITS.QSIDE_CASTLE) {\n                const castlingTo = move.to + 1;\n                const castlingFrom = move.to - 2;\n                this._movePiece(castlingFrom, castlingTo);\n            }\n            // turn off castling\n            this._castling[us] = 0;\n        }\n        // turn off castling if we move a rook\n        if (this._castling[us]) {\n            for (let i = 0, len = ROOKS[us].length; i < len; i++) {\n                if (move.from === ROOKS[us][i].square &&\n                    this._castling[us] & ROOKS[us][i].flag) {\n                    this._castling[us] ^= ROOKS[us][i].flag;\n                    break;\n                }\n            }\n        }\n        // turn off castling if we capture a rook\n        if (this._castling[them]) {\n            for (let i = 0, len = ROOKS[them].length; i < len; i++) {\n                if (move.to === ROOKS[them][i].square &&\n                    this._castling[them] & ROOKS[them][i].flag) {\n                    this._castling[them] ^= ROOKS[them][i].flag;\n                    break;\n                }\n            }\n        }\n        this._hash ^= this._castlingKey();\n        // if big pawn move, update the en passant square\n        if (move.flags & BITS.BIG_PAWN) {\n            let epSquare;\n            if (us === BLACK) {\n                epSquare = move.to - 16;\n            }\n            else {\n                epSquare = move.to + 16;\n            }\n            if ((!((move.to - 1) & 0x88) &&\n                this._board[move.to - 1]?.type === PAWN &&\n                this._board[move.to - 1]?.color === them) ||\n                (!((move.to + 1) & 0x88) &&\n                    this._board[move.to + 1]?.type === PAWN &&\n                    this._board[move.to + 1]?.color === them)) {\n                this._epSquare = epSquare;\n                this._hash ^= this._epKey();\n            }\n            else {\n                this._epSquare = EMPTY;\n            }\n        }\n        else {\n            this._epSquare = EMPTY;\n        }\n        // reset the 50 move counter if a pawn is moved or a piece is captured\n        if (move.piece === PAWN) {\n            this._halfMoves = 0;\n        }\n        else if (move.flags & (BITS.CAPTURE | BITS.EP_CAPTURE)) {\n            this._halfMoves = 0;\n        }\n        else {\n            this._halfMoves++;\n        }\n        if (us === BLACK) {\n            this._moveNumber++;\n        }\n        this._turn = them;\n        this._hash ^= SIDE_KEY;\n    }\n    undo() {\n        const hash = this._hash;\n        const move = this._undoMove();\n        if (move) {\n            const prettyMove = new Move(this, move);\n            this._decPositionCount(hash);\n            return prettyMove;\n        }\n        return null;\n    }\n    _undoMove() {\n        const old = this._history.pop();\n        if (old === undefined) {\n            return null;\n        }\n        this._hash ^= this._epKey();\n        this._hash ^= this._castlingKey();\n        const move = old.move;\n        this._kings = old.kings;\n        this._turn = old.turn;\n        this._castling = old.castling;\n        this._epSquare = old.epSquare;\n        this._halfMoves = old.halfMoves;\n        this._moveNumber = old.moveNumber;\n        this._hash ^= this._epKey();\n        this._hash ^= this._castlingKey();\n        this._hash ^= SIDE_KEY;\n        const us = this._turn;\n        const them = swapColor(us);\n        if (move.flags & BITS.NULL_MOVE) {\n            return move;\n        }\n        this._movePiece(move.to, move.from);\n        // to undo any promotions\n        if (move.piece) {\n            this._clear(move.from);\n            this._set(move.from, { type: move.piece, color: us });\n        }\n        if (move.captured) {\n            if (move.flags & BITS.EP_CAPTURE) {\n                // en passant capture\n                let index;\n                if (us === BLACK) {\n                    index = move.to - 16;\n                }\n                else {\n                    index = move.to + 16;\n                }\n                this._set(index, { type: PAWN, color: them });\n            }\n            else {\n                // regular capture\n                this._set(move.to, { type: move.captured, color: them });\n            }\n        }\n        if (move.flags & (BITS.KSIDE_CASTLE | BITS.QSIDE_CASTLE)) {\n            let castlingTo, castlingFrom;\n            if (move.flags & BITS.KSIDE_CASTLE) {\n                castlingTo = move.to + 1;\n                castlingFrom = move.to - 1;\n            }\n            else {\n                castlingTo = move.to - 2;\n                castlingFrom = move.to + 1;\n            }\n            this._movePiece(castlingFrom, castlingTo);\n        }\n        return move;\n    }\n    pgn({ newline = '\\n', maxWidth = 0, } = {}) {\n        /*\n         * using the specification from http://www.chessclub.com/help/PGN-spec\n         * example for html usage: .pgn({ max_width: 72, newline_char: \"<br />\" })\n         */\n        const result = [];\n        let headerExists = false;\n        /* add the PGN header information */\n        for (const i in this._header) {\n            /*\n             * TODO: order of enumerated properties in header object is not\n             * guaranteed, see ECMA-262 spec (section 12.6.4)\n             *\n             * By using HEADER_TEMPLATE, the order of tags should be preserved; we\n             * do have to check for null placeholders, though, and omit them\n             */\n            const headerTag = this._header[i];\n            if (headerTag)\n                result.push(`[${i} \"${this._header[i]}\"]` + newline);\n            headerExists = true;\n        }\n        if (headerExists && this._history.length) {\n            result.push(newline);\n        }\n        const appendComment = (moveString) => {\n            const comment = this._comments[this.fen()];\n            if (typeof comment !== 'undefined') {\n                const delimiter = moveString.length > 0 ? ' ' : '';\n                moveString = `${moveString}${delimiter}{${comment}}`;\n            }\n            return moveString;\n        };\n        // pop all of history onto reversed_history\n        const reversedHistory = [];\n        while (this._history.length > 0) {\n            reversedHistory.push(this._undoMove());\n        }\n        const moves = [];\n        let moveString = '';\n        // special case of a commented starting position with no moves\n        if (reversedHistory.length === 0) {\n            moves.push(appendComment(''));\n        }\n        // build the list of moves.  a move_string looks like: \"3. e3 e6\"\n        while (reversedHistory.length > 0) {\n            moveString = appendComment(moveString);\n            const move = reversedHistory.pop();\n            // make TypeScript stop complaining about move being undefined\n            if (!move) {\n                break;\n            }\n            // if the position started with black to move, start PGN with #. ...\n            if (!this._history.length && move.color === 'b') {\n                const prefix = `${this._moveNumber}. ...`;\n                // is there a comment preceding the first move?\n                moveString = moveString ? `${moveString} ${prefix}` : prefix;\n            }\n            else if (move.color === 'w') {\n                // store the previous generated move_string if we have one\n                if (moveString.length) {\n                    moves.push(moveString);\n                }\n                moveString = this._moveNumber + '.';\n            }\n            moveString =\n                moveString + ' ' + this._moveToSan(move, this._moves({ legal: true }));\n            this._makeMove(move);\n        }\n        // are there any other leftover moves?\n        if (moveString.length) {\n            moves.push(appendComment(moveString));\n        }\n        // is there a result? (there ALWAYS has to be a result according to spec; see Seven Tag Roster)\n        moves.push(this._header.Result || '*');\n        /*\n         * history should be back to what it was before we started generating PGN,\n         * so join together moves\n         */\n        if (maxWidth === 0) {\n            return result.join('') + moves.join(' ');\n        }\n        // TODO (jah): huh?\n        const strip = function () {\n            if (result.length > 0 && result[result.length - 1] === ' ') {\n                result.pop();\n                return true;\n            }\n            return false;\n        };\n        // NB: this does not preserve comment whitespace.\n        const wrapComment = function (width, move) {\n            for (const token of move.split(' ')) {\n                if (!token) {\n                    continue;\n                }\n                if (width + token.length > maxWidth) {\n                    while (strip()) {\n                        width--;\n                    }\n                    result.push(newline);\n                    width = 0;\n                }\n                result.push(token);\n                width += token.length;\n                result.push(' ');\n                width++;\n            }\n            if (strip()) {\n                width--;\n            }\n            return width;\n        };\n        // wrap the PGN output at max_width\n        let currentWidth = 0;\n        for (let i = 0; i < moves.length; i++) {\n            if (currentWidth + moves[i].length > maxWidth) {\n                if (moves[i].includes('{')) {\n                    currentWidth = wrapComment(currentWidth, moves[i]);\n                    continue;\n                }\n            }\n            // if the current move will push past max_width\n            if (currentWidth + moves[i].length > maxWidth && i !== 0) {\n                // don't end the line with whitespace\n                if (result[result.length - 1] === ' ') {\n                    result.pop();\n                }\n                result.push(newline);\n                currentWidth = 0;\n            }\n            else if (i !== 0) {\n                result.push(' ');\n                currentWidth++;\n            }\n            result.push(moves[i]);\n            currentWidth += moves[i].length;\n        }\n        return result.join('');\n    }\n    /**\n     * @deprecated Use `setHeader` and `getHeaders` instead. This method will return null header tags (which is not what you want)\n     */\n    header(...args) {\n        for (let i = 0; i < args.length; i += 2) {\n            if (typeof args[i] === 'string' && typeof args[i + 1] === 'string') {\n                this._header[args[i]] = args[i + 1];\n            }\n        }\n        return this._header;\n    }\n    // TODO: value validation per spec\n    setHeader(key, value) {\n        this._header[key] = value ?? SEVEN_TAG_ROSTER[key] ?? null;\n        return this.getHeaders();\n    }\n    removeHeader(key) {\n        if (key in this._header) {\n            this._header[key] = SEVEN_TAG_ROSTER[key] || null;\n            return true;\n        }\n        return false;\n    }\n    // return only non-null headers (omit placemarker nulls)\n    getHeaders() {\n        const nonNullHeaders = {};\n        for (const [key, value] of Object.entries(this._header)) {\n            if (value !== null) {\n                nonNullHeaders[key] = value;\n            }\n        }\n        return nonNullHeaders;\n    }\n    loadPgn(pgn, { strict = false, newlineChar = '\\r?\\n', } = {}) {\n        // If newlineChar is not the default, replace all instances with \\n\n        if (newlineChar !== '\\r?\\n') {\n            pgn = pgn.replace(new RegExp(newlineChar, 'g'), '\\n');\n        }\n        const parsedPgn = peg$parse(pgn);\n        // Put the board in the starting position\n        this.reset();\n        // parse PGN header\n        const headers = parsedPgn.headers;\n        let fen = '';\n        for (const key in headers) {\n            // check to see user is including fen (possibly with wrong tag case)\n            if (key.toLowerCase() === 'fen') {\n                fen = headers[key];\n            }\n            this.header(key, headers[key]);\n        }\n        /*\n         * the permissive parser should attempt to load a fen tag, even if it's the\n         * wrong case and doesn't include a corresponding [SetUp \"1\"] tag\n         */\n        if (!strict) {\n            if (fen) {\n                this.load(fen, { preserveHeaders: true });\n            }\n        }\n        else {\n            /*\n             * strict parser - load the starting position indicated by [Setup '1']\n             * and [FEN position]\n             */\n            if (headers['SetUp'] === '1') {\n                if (!('FEN' in headers)) {\n                    throw new Error('Invalid PGN: FEN tag must be supplied with SetUp tag');\n                }\n                // don't clear the headers when loading\n                this.load(headers['FEN'], { preserveHeaders: true });\n            }\n        }\n        let node = parsedPgn.root;\n        while (node) {\n            if (node.move) {\n                const move = this._moveFromSan(node.move, strict);\n                if (move == null) {\n                    throw new Error(`Invalid move in PGN: ${node.move}`);\n                }\n                else {\n                    this._makeMove(move);\n                    this._incPositionCount();\n                }\n            }\n            if (node.comment !== undefined) {\n                this._comments[this.fen()] = node.comment;\n            }\n            node = node.variations[0];\n        }\n        /*\n         * Per section 8.2.6 of the PGN spec, the Result tag pair must match match\n         * the termination marker. Only do this when headers are present, but the\n         * result tag is missing\n         */\n        const result = parsedPgn.result;\n        if (result &&\n            Object.keys(this._header).length &&\n            this._header['Result'] !== result) {\n            this.setHeader('Result', result);\n        }\n    }\n    /*\n     * Convert a move from 0x88 coordinates to Standard Algebraic Notation\n     * (SAN)\n     *\n     * @param {boolean} strict Use the strict SAN parser. It will throw errors\n     * on overly disambiguated moves (see below):\n     *\n     * r1bqkbnr/ppp2ppp/2n5/1B1pP3/4P3/8/PPPP2PP/RNBQK1NR b KQkq - 2 4\n     * 4. ... Nge7 is overly disambiguated because the knight on c6 is pinned\n     * 4. ... Ne7 is technically the valid SAN\n     */\n    _moveToSan(move, moves) {\n        let output = '';\n        if (move.flags & BITS.KSIDE_CASTLE) {\n            output = 'O-O';\n        }\n        else if (move.flags & BITS.QSIDE_CASTLE) {\n            output = 'O-O-O';\n        }\n        else if (move.flags & BITS.NULL_MOVE) {\n            return SAN_NULLMOVE;\n        }\n        else {\n            if (move.piece !== PAWN) {\n                const disambiguator = getDisambiguator(move, moves);\n                output += move.piece.toUpperCase() + disambiguator;\n            }\n            if (move.flags & (BITS.CAPTURE | BITS.EP_CAPTURE)) {\n                if (move.piece === PAWN) {\n                    output += algebraic(move.from)[0];\n                }\n                output += 'x';\n            }\n            output += algebraic(move.to);\n            if (move.promotion) {\n                output += '=' + move.promotion.toUpperCase();\n            }\n        }\n        this._makeMove(move);\n        if (this.isCheck()) {\n            if (this.isCheckmate()) {\n                output += '#';\n            }\n            else {\n                output += '+';\n            }\n        }\n        this._undoMove();\n        return output;\n    }\n    // convert a move from Standard Algebraic Notation (SAN) to 0x88 coordinates\n    _moveFromSan(move, strict = false) {\n        // strip off any move decorations: e.g Nf3+?! becomes Nf3\n        let cleanMove = strippedSan(move);\n        if (!strict) {\n            if (cleanMove === '0-0') {\n                cleanMove = 'O-O';\n            }\n            else if (cleanMove === '0-0-0') {\n                cleanMove = 'O-O-O';\n            }\n        }\n        //first implementation of null with a dummy move (black king moves from a8 to a8), maybe this can be implemented better\n        if (cleanMove == SAN_NULLMOVE) {\n            const res = {\n                color: this._turn,\n                from: 0,\n                to: 0,\n                piece: 'k',\n                flags: BITS.NULL_MOVE,\n            };\n            return res;\n        }\n        let pieceType = inferPieceType(cleanMove);\n        let moves = this._moves({ legal: true, piece: pieceType });\n        // strict parser\n        for (let i = 0, len = moves.length; i < len; i++) {\n            if (cleanMove === strippedSan(this._moveToSan(moves[i], moves))) {\n                return moves[i];\n            }\n        }\n        // the strict parser failed\n        if (strict) {\n            return null;\n        }\n        let piece = undefined;\n        let matches = undefined;\n        let from = undefined;\n        let to = undefined;\n        let promotion = undefined;\n        /*\n         * The default permissive (non-strict) parser allows the user to parse\n         * non-standard chess notations. This parser is only run after the strict\n         * Standard Algebraic Notation (SAN) parser has failed.\n         *\n         * When running the permissive parser, we'll run a regex to grab the piece, the\n         * to/from square, and an optional promotion piece. This regex will\n         * parse common non-standard notation like: Pe2-e4, Rc1c4, Qf3xf7,\n         * f7f8q, b1c3\n         *\n         * NOTE: Some positions and moves may be ambiguous when using the permissive\n         * parser. For example, in this position: 6k1/8/8/B7/8/8/8/BN4K1 w - - 0 1,\n         * the move b1c3 may be interpreted as Nc3 or B1c3 (a disambiguated bishop\n         * move). In these cases, the permissive parser will default to the most\n         * basic interpretation (which is b1c3 parsing to Nc3).\n         */\n        let overlyDisambiguated = false;\n        matches = cleanMove.match(/([pnbrqkPNBRQK])?([a-h][1-8])x?-?([a-h][1-8])([qrbnQRBN])?/);\n        if (matches) {\n            piece = matches[1];\n            from = matches[2];\n            to = matches[3];\n            promotion = matches[4];\n            if (from.length == 1) {\n                overlyDisambiguated = true;\n            }\n        }\n        else {\n            /*\n             * The [a-h]?[1-8]? portion of the regex below handles moves that may be\n             * overly disambiguated (e.g. Nge7 is unnecessary and non-standard when\n             * there is one legal knight move to e7). In this case, the value of\n             * 'from' variable will be a rank or file, not a square.\n             */\n            matches = cleanMove.match(/([pnbrqkPNBRQK])?([a-h]?[1-8]?)x?-?([a-h][1-8])([qrbnQRBN])?/);\n            if (matches) {\n                piece = matches[1];\n                from = matches[2];\n                to = matches[3];\n                promotion = matches[4];\n                if (from.length == 1) {\n                    overlyDisambiguated = true;\n                }\n            }\n        }\n        pieceType = inferPieceType(cleanMove);\n        moves = this._moves({\n            legal: true,\n            piece: piece ? piece : pieceType,\n        });\n        if (!to) {\n            return null;\n        }\n        for (let i = 0, len = moves.length; i < len; i++) {\n            if (!from) {\n                // if there is no from square, it could be just 'x' missing from a capture\n                if (cleanMove ===\n                    strippedSan(this._moveToSan(moves[i], moves)).replace('x', '')) {\n                    return moves[i];\n                }\n                // hand-compare move properties with the results from our permissive regex\n            }\n            else if ((!piece || piece.toLowerCase() == moves[i].piece) &&\n                Ox88[from] == moves[i].from &&\n                Ox88[to] == moves[i].to &&\n                (!promotion || promotion.toLowerCase() == moves[i].promotion)) {\n                return moves[i];\n            }\n            else if (overlyDisambiguated) {\n                /*\n                 * SPECIAL CASE: we parsed a move string that may have an unneeded\n                 * rank/file disambiguator (e.g. Nge7).  The 'from' variable will\n                 */\n                const square = algebraic(moves[i].from);\n                if ((!piece || piece.toLowerCase() == moves[i].piece) &&\n                    Ox88[to] == moves[i].to &&\n                    (from == square[0] || from == square[1]) &&\n                    (!promotion || promotion.toLowerCase() == moves[i].promotion)) {\n                    return moves[i];\n                }\n            }\n        }\n        return null;\n    }\n    ascii() {\n        let s = '   +------------------------+\\n';\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            // display the rank\n            if (file(i) === 0) {\n                s += ' ' + '87654321'[rank(i)] + ' |';\n            }\n            if (this._board[i]) {\n                const piece = this._board[i].type;\n                const color = this._board[i].color;\n                const symbol = color === WHITE ? piece.toUpperCase() : piece.toLowerCase();\n                s += ' ' + symbol + ' ';\n            }\n            else {\n                s += ' . ';\n            }\n            if ((i + 1) & 0x88) {\n                s += '|\\n';\n                i += 8;\n            }\n        }\n        s += '   +------------------------+\\n';\n        s += '     a  b  c  d  e  f  g  h';\n        return s;\n    }\n    perft(depth) {\n        const moves = this._moves({ legal: false });\n        let nodes = 0;\n        const color = this._turn;\n        for (let i = 0, len = moves.length; i < len; i++) {\n            this._makeMove(moves[i]);\n            if (!this._isKingAttacked(color)) {\n                if (depth - 1 > 0) {\n                    nodes += this.perft(depth - 1);\n                }\n                else {\n                    nodes++;\n                }\n            }\n            this._undoMove();\n        }\n        return nodes;\n    }\n    setTurn(color) {\n        if (this._turn == color) {\n            return false;\n        }\n        this.move('--');\n        return true;\n    }\n    turn() {\n        return this._turn;\n    }\n    board() {\n        const output = [];\n        let row = [];\n        for (let i = Ox88.a8; i <= Ox88.h1; i++) {\n            if (this._board[i] == null) {\n                row.push(null);\n            }\n            else {\n                row.push({\n                    square: algebraic(i),\n                    type: this._board[i].type,\n                    color: this._board[i].color,\n                });\n            }\n            if ((i + 1) & 0x88) {\n                output.push(row);\n                row = [];\n                i += 8;\n            }\n        }\n        return output;\n    }\n    squareColor(square) {\n        if (square in Ox88) {\n            const sq = Ox88[square];\n            return (rank(sq) + file(sq)) % 2 === 0 ? 'light' : 'dark';\n        }\n        return null;\n    }\n    history({ verbose = false } = {}) {\n        const reversedHistory = [];\n        const moveHistory = [];\n        while (this._history.length > 0) {\n            reversedHistory.push(this._undoMove());\n        }\n        while (true) {\n            const move = reversedHistory.pop();\n            if (!move) {\n                break;\n            }\n            if (verbose) {\n                moveHistory.push(new Move(this, move));\n            }\n            else {\n                moveHistory.push(this._moveToSan(move, this._moves()));\n            }\n            this._makeMove(move);\n        }\n        return moveHistory;\n    }\n    /*\n     * Keeps track of position occurrence counts for the purpose of repetition\n     * checking. Old positions are removed from the map if their counts are reduced to 0.\n     */\n    _getPositionCount(hash) {\n        return this._positionCount.get(hash) ?? 0;\n    }\n    _incPositionCount() {\n        this._positionCount.set(this._hash, (this._positionCount.get(this._hash) ?? 0) + 1);\n    }\n    _decPositionCount(hash) {\n        const currentCount = this._positionCount.get(hash) ?? 0;\n        if (currentCount === 1) {\n            this._positionCount.delete(hash);\n        }\n        else {\n            this._positionCount.set(hash, currentCount - 1);\n        }\n    }\n    _pruneComments() {\n        const reversedHistory = [];\n        const currentComments = {};\n        const copyComment = (fen) => {\n            if (fen in this._comments) {\n                currentComments[fen] = this._comments[fen];\n            }\n        };\n        while (this._history.length > 0) {\n            reversedHistory.push(this._undoMove());\n        }\n        copyComment(this.fen());\n        while (true) {\n            const move = reversedHistory.pop();\n            if (!move) {\n                break;\n            }\n            this._makeMove(move);\n            copyComment(this.fen());\n        }\n        this._comments = currentComments;\n    }\n    getComment() {\n        return this._comments[this.fen()];\n    }\n    setComment(comment) {\n        this._comments[this.fen()] = comment.replace('{', '[').replace('}', ']');\n    }\n    /**\n     * @deprecated Renamed to `removeComment` for consistency\n     */\n    deleteComment() {\n        return this.removeComment();\n    }\n    removeComment() {\n        const comment = this._comments[this.fen()];\n        delete this._comments[this.fen()];\n        return comment;\n    }\n    getComments() {\n        this._pruneComments();\n        return Object.keys(this._comments).map((fen) => {\n            return { fen: fen, comment: this._comments[fen] };\n        });\n    }\n    /**\n     * @deprecated Renamed to `removeComments` for consistency\n     */\n    deleteComments() {\n        return this.removeComments();\n    }\n    removeComments() {\n        this._pruneComments();\n        return Object.keys(this._comments).map((fen) => {\n            const comment = this._comments[fen];\n            delete this._comments[fen];\n            return { fen: fen, comment: comment };\n        });\n    }\n    setCastlingRights(color, rights) {\n        for (const side of [KING, QUEEN]) {\n            if (rights[side] !== undefined) {\n                if (rights[side]) {\n                    this._castling[color] |= SIDES[side];\n                }\n                else {\n                    this._castling[color] &= ~SIDES[side];\n                }\n            }\n        }\n        this._updateCastlingRights();\n        const result = this.getCastlingRights(color);\n        return ((rights[KING] === undefined || rights[KING] === result[KING]) &&\n            (rights[QUEEN] === undefined || rights[QUEEN] === result[QUEEN]));\n    }\n    getCastlingRights(color) {\n        return {\n            [KING]: (this._castling[color] & SIDES[KING]) !== 0,\n            [QUEEN]: (this._castling[color] & SIDES[QUEEN]) !== 0,\n        };\n    }\n    moveNumber() {\n        return this._moveNumber;\n    }\n}\n\n\n//# sourceMappingURL=chess.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/chess.js@1.4.0/node_modules/chess.js/dist/esm/chess.js\n");

/***/ })

};
;
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+postgrest-js@1.21.0";
exports.ids = ["vendor-chunks/@supabase+postgrest-js@1.21.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js ***!
  \***************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// @ts-ignore\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! @supabase/node-fetch */ \"(rsc)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\"));\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nclass PostgrestBuilder {\n    constructor(builder) {\n        var _a, _b;\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = new Headers(builder.headers);\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = (_a = builder.shouldThrowOnError) !== null && _a !== void 0 ? _a : false;\n        this.signal = builder.signal;\n        this.isMaybeSingle = (_b = builder.isMaybeSingle) !== null && _b !== void 0 ? _b : false;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        }\n        else if (typeof fetch === 'undefined') {\n            this.fetch = node_fetch_1.default;\n        }\n        else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */\n    throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */\n    setHeader(name, value) {\n        this.headers = new Headers(this.headers);\n        this.headers.set(name, value);\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n            // skip\n        }\n        else if (['GET', 'HEAD'].includes(this.method)) {\n            this.headers.set('Accept-Profile', this.schema);\n        }\n        else {\n            this.headers.set('Content-Profile', this.schema);\n        }\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.headers.set('Content-Type', 'application/json');\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal,\n        }).then(async (res) => {\n            var _a, _b, _c, _d;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== 'HEAD') {\n                    const body = await res.text();\n                    if (body === '') {\n                        // Prefer: return=minimal\n                    }\n                    else if (this.headers.get('Accept') === 'text/csv') {\n                        data = body;\n                    }\n                    else if (this.headers.get('Accept') &&\n                        ((_a = this.headers.get('Accept')) === null || _a === void 0 ? void 0 : _a.includes('application/vnd.pgrst.plan+text'))) {\n                        data = body;\n                    }\n                    else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_b = this.headers.get('Prefer')) === null || _b === void 0 ? void 0 : _b.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_c = res.headers.get('content-range')) === null || _c === void 0 ? void 0 : _c.split('/');\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: 'PGRST116',\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: 'JSON object requested, multiple (or no) rows returned',\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = 'Not Acceptable';\n                    }\n                    else if (data.length === 1) {\n                        data = data[0];\n                    }\n                    else {\n                        data = null;\n                    }\n                }\n            }\n            else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = 'OK';\n                    }\n                }\n                catch (_e) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === '') {\n                        status = 204;\n                        statusText = 'No Content';\n                    }\n                    else {\n                        error = {\n                            message: body,\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_d = error === null || error === void 0 ? void 0 : error.details) === null || _d === void 0 ? void 0 : _d.includes('0 rows'))) {\n                    error = null;\n                    status = 200;\n                    statusText = 'OK';\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText,\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError) => {\n                var _a, _b, _c;\n                return ({\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n                        hint: '',\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`,\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: '',\n                });\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        /* istanbul ignore next */\n        return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */\n    overrideTypes() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = new Headers(headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: new Headers(this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        var _a;\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = new Headers(this.headers);\n        if (count) {\n            headers.set('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n}\nexports[\"default\"] = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports[\"default\"] = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjIxLjAvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9janMvUG9zdGdyZXN0RXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZW5kb2Fyc2FuZGlcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hlc3NsbG1cXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4yMS4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccG9zdGdyZXN0LWpzXFxkaXN0XFxjanNcXFBvc3RncmVzdEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLyoqXG4gKiBFcnJvciBmb3JtYXRcbiAqXG4gKiB7QGxpbmsgaHR0cHM6Ly9wb3N0Z3Jlc3Qub3JnL2VuL3N0YWJsZS9hcGkuaHRtbD9oaWdobGlnaHQ9b3B0aW9ucyNlcnJvcnMtYW5kLWh0dHAtc3RhdHVzLWNvZGVzfVxuICovXG5jbGFzcyBQb3N0Z3Jlc3RFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcihjb250ZXh0KSB7XG4gICAgICAgIHN1cGVyKGNvbnRleHQubWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdQb3N0Z3Jlc3RFcnJvcic7XG4gICAgICAgIHRoaXMuZGV0YWlscyA9IGNvbnRleHQuZGV0YWlscztcbiAgICAgICAgdGhpcy5oaW50ID0gY29udGV4dC5oaW50O1xuICAgICAgICB0aGlzLmNvZGUgPSBjb250ZXh0LmNvZGU7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gUG9zdGdyZXN0RXJyb3I7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Qb3N0Z3Jlc3RFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js ***!
  \*********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestFilterBuilder;\n//# sourceMappingURL=PostgrestFilterBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js ***!
  \********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch, }) {\n        this.url = url;\n        this.headers = new Headers(headers);\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    select(columns, { head = false, count, } = {}) {\n        const method = head ? 'HEAD' : 'GET';\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */\n    insert(values, { count, defaultToNull = true, } = {}) {\n        var _a;\n        const method = 'POST';\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        if (!defaultToNull) {\n            this.headers.append('Prefer', `missing=default`);\n        }\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */\n    upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true, } = {}) {\n        var _a;\n        const method = 'POST';\n        this.headers.append('Prefer', `resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`);\n        if (onConflict !== undefined)\n            this.url.searchParams.set('on_conflict', onConflict);\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        if (!defaultToNull) {\n            this.headers.append('Prefer', 'missing=default');\n        }\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    update(values, { count, } = {}) {\n        var _a;\n        const method = 'PATCH';\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    delete({ count, } = {}) {\n        var _a;\n        const method = 'DELETE';\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n}\nexports[\"default\"] = PostgrestQueryBuilder;\n//# sourceMappingURL=PostgrestQueryBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */\n    select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        this.headers.append('Prefer', 'return=representation');\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : 'order';\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    limit(count, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    range(from, to, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */\n    abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */\n    single() {\n        this.headers.set('Accept', 'application/vnd.pgrst.object+json');\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */\n    maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === 'GET') {\n            this.headers.set('Accept', 'application/json');\n        }\n        else {\n            this.headers.set('Accept', 'application/vnd.pgrst.object+json');\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */\n    csv() {\n        this.headers.set('Accept', 'text/csv');\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */\n    geojson() {\n        this.headers.set('Accept', 'application/geo+json');\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */\n    explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = 'text', } = {}) {\n        var _a;\n        const options = [\n            analyze ? 'analyze' : null,\n            verbose ? 'verbose' : null,\n            settings ? 'settings' : null,\n            buffers ? 'buffers' : null,\n            wal ? 'wal' : null,\n        ]\n            .filter(Boolean)\n            .join('|');\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers.get('Accept')) !== null && _a !== void 0 ? _a : 'application/json';\n        this.headers.set('Accept', `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`);\n        if (format === 'json') {\n            return this;\n        }\n        else {\n            return this;\n        }\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */\n    rollback() {\n        this.headers.append('Prefer', 'tx=rollback');\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        return this;\n    }\n    /**\n     * Set the maximum number of rows that can be affected by the query.\n     * Only available in PostgREST v13+ and only works with PATCH and DELETE methods.\n     *\n     * @param value - The maximum number of rows that can be affected\n     */\n    maxAffected(value) {\n        this.headers.append('Prefer', 'handling=strict');\n        this.headers.append('Prefer', `max-affected=${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestTransformBuilder;\n//# sourceMappingURL=PostgrestTransformBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(__webpack_require__(/*! ./PostgrestClient */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports[\"default\"] = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default,\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgrestBuilder: () => (/* binding */ PostgrestBuilder),\n/* harmony export */   PostgrestClient: () => (/* binding */ PostgrestClient),\n/* harmony export */   PostgrestError: () => (/* binding */ PostgrestError),\n/* harmony export */   PostgrestFilterBuilder: () => (/* binding */ PostgrestFilterBuilder),\n/* harmony export */   PostgrestQueryBuilder: () => (/* binding */ PostgrestQueryBuilder),\n/* harmony export */   PostgrestTransformBuilder: () => (/* binding */ PostgrestTransformBuilder),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cjs/index.js */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js\");\n\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__\n\n\n\n// compatibility with CJS output\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjIxLjAvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9lc20vd3JhcHBlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLEVBQUUsMENBQUs7O0FBU1I7O0FBRUQ7QUFDQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVuZG9hcnNhbmRpXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoZXNzbGxtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMjEuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHBvc3RncmVzdC1qc1xcZGlzdFxcZXNtXFx3cmFwcGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaW5kZXggZnJvbSAnLi4vY2pzL2luZGV4LmpzJ1xuY29uc3Qge1xuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59ID0gaW5kZXhcblxuZXhwb3J0IHtcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RGaWx0ZXJCdWlsZGVyLFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufVxuXG4vLyBjb21wYXRpYmlsaXR5IHdpdGggQ0pTIG91dHB1dFxuZXhwb3J0IGRlZmF1bHQge1xuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js ***!
  \***************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// @ts-ignore\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\"));\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nclass PostgrestBuilder {\n    constructor(builder) {\n        var _a, _b;\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = new Headers(builder.headers);\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = (_a = builder.shouldThrowOnError) !== null && _a !== void 0 ? _a : false;\n        this.signal = builder.signal;\n        this.isMaybeSingle = (_b = builder.isMaybeSingle) !== null && _b !== void 0 ? _b : false;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        }\n        else if (typeof fetch === 'undefined') {\n            this.fetch = node_fetch_1.default;\n        }\n        else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */\n    throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */\n    setHeader(name, value) {\n        this.headers = new Headers(this.headers);\n        this.headers.set(name, value);\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n            // skip\n        }\n        else if (['GET', 'HEAD'].includes(this.method)) {\n            this.headers.set('Accept-Profile', this.schema);\n        }\n        else {\n            this.headers.set('Content-Profile', this.schema);\n        }\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.headers.set('Content-Type', 'application/json');\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal,\n        }).then(async (res) => {\n            var _a, _b, _c, _d;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== 'HEAD') {\n                    const body = await res.text();\n                    if (body === '') {\n                        // Prefer: return=minimal\n                    }\n                    else if (this.headers.get('Accept') === 'text/csv') {\n                        data = body;\n                    }\n                    else if (this.headers.get('Accept') &&\n                        ((_a = this.headers.get('Accept')) === null || _a === void 0 ? void 0 : _a.includes('application/vnd.pgrst.plan+text'))) {\n                        data = body;\n                    }\n                    else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_b = this.headers.get('Prefer')) === null || _b === void 0 ? void 0 : _b.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_c = res.headers.get('content-range')) === null || _c === void 0 ? void 0 : _c.split('/');\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: 'PGRST116',\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: 'JSON object requested, multiple (or no) rows returned',\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = 'Not Acceptable';\n                    }\n                    else if (data.length === 1) {\n                        data = data[0];\n                    }\n                    else {\n                        data = null;\n                    }\n                }\n            }\n            else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = 'OK';\n                    }\n                }\n                catch (_e) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === '') {\n                        status = 204;\n                        statusText = 'No Content';\n                    }\n                    else {\n                        error = {\n                            message: body,\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_d = error === null || error === void 0 ? void 0 : error.details) === null || _d === void 0 ? void 0 : _d.includes('0 rows'))) {\n                    error = null;\n                    status = 200;\n                    statusText = 'OK';\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText,\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError) => {\n                var _a, _b, _c;\n                return ({\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n                        hint: '',\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`,\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: '',\n                });\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        /* istanbul ignore next */\n        return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */\n    overrideTypes() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = new Headers(headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: new Headers(this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        var _a;\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = new Headers(this.headers);\n        if (count) {\n            headers.set('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n}\nexports[\"default\"] = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports[\"default\"] = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjIxLjAvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9janMvUG9zdGdyZXN0RXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZW5kb2Fyc2FuZGlcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hlc3NsbG1cXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4yMS4wXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccG9zdGdyZXN0LWpzXFxkaXN0XFxjanNcXFBvc3RncmVzdEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLyoqXG4gKiBFcnJvciBmb3JtYXRcbiAqXG4gKiB7QGxpbmsgaHR0cHM6Ly9wb3N0Z3Jlc3Qub3JnL2VuL3N0YWJsZS9hcGkuaHRtbD9oaWdobGlnaHQ9b3B0aW9ucyNlcnJvcnMtYW5kLWh0dHAtc3RhdHVzLWNvZGVzfVxuICovXG5jbGFzcyBQb3N0Z3Jlc3RFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcihjb250ZXh0KSB7XG4gICAgICAgIHN1cGVyKGNvbnRleHQubWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdQb3N0Z3Jlc3RFcnJvcic7XG4gICAgICAgIHRoaXMuZGV0YWlscyA9IGNvbnRleHQuZGV0YWlscztcbiAgICAgICAgdGhpcy5oaW50ID0gY29udGV4dC5oaW50O1xuICAgICAgICB0aGlzLmNvZGUgPSBjb250ZXh0LmNvZGU7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gUG9zdGdyZXN0RXJyb3I7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Qb3N0Z3Jlc3RFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js ***!
  \*********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestFilterBuilder;\n//# sourceMappingURL=PostgrestFilterBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js ***!
  \********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch, }) {\n        this.url = url;\n        this.headers = new Headers(headers);\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    select(columns, { head = false, count, } = {}) {\n        const method = head ? 'HEAD' : 'GET';\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */\n    insert(values, { count, defaultToNull = true, } = {}) {\n        var _a;\n        const method = 'POST';\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        if (!defaultToNull) {\n            this.headers.append('Prefer', `missing=default`);\n        }\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */\n    upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true, } = {}) {\n        var _a;\n        const method = 'POST';\n        this.headers.append('Prefer', `resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`);\n        if (onConflict !== undefined)\n            this.url.searchParams.set('on_conflict', onConflict);\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        if (!defaultToNull) {\n            this.headers.append('Prefer', 'missing=default');\n        }\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    update(values, { count, } = {}) {\n        var _a;\n        const method = 'PATCH';\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    delete({ count, } = {}) {\n        var _a;\n        const method = 'DELETE';\n        if (count) {\n            this.headers.append('Prefer', `count=${count}`);\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: (_a = this.fetch) !== null && _a !== void 0 ? _a : fetch,\n        });\n    }\n}\nexports[\"default\"] = PostgrestQueryBuilder;\n//# sourceMappingURL=PostgrestQueryBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */\n    select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        this.headers.append('Prefer', 'return=representation');\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : 'order';\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    limit(count, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    range(from, to, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */\n    abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */\n    single() {\n        this.headers.set('Accept', 'application/vnd.pgrst.object+json');\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */\n    maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === 'GET') {\n            this.headers.set('Accept', 'application/json');\n        }\n        else {\n            this.headers.set('Accept', 'application/vnd.pgrst.object+json');\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */\n    csv() {\n        this.headers.set('Accept', 'text/csv');\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */\n    geojson() {\n        this.headers.set('Accept', 'application/geo+json');\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */\n    explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = 'text', } = {}) {\n        var _a;\n        const options = [\n            analyze ? 'analyze' : null,\n            verbose ? 'verbose' : null,\n            settings ? 'settings' : null,\n            buffers ? 'buffers' : null,\n            wal ? 'wal' : null,\n        ]\n            .filter(Boolean)\n            .join('|');\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers.get('Accept')) !== null && _a !== void 0 ? _a : 'application/json';\n        this.headers.set('Accept', `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`);\n        if (format === 'json') {\n            return this;\n        }\n        else {\n            return this;\n        }\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */\n    rollback() {\n        this.headers.append('Prefer', 'tx=rollback');\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        return this;\n    }\n    /**\n     * Set the maximum number of rows that can be affected by the query.\n     * Only available in PostgREST v13+ and only works with PATCH and DELETE methods.\n     *\n     * @param value - The maximum number of rows that can be affected\n     */\n    maxAffected(value) {\n        this.headers.append('Prefer', 'handling=strict');\n        this.headers.append('Prefer', `max-affected=${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestTransformBuilder;\n//# sourceMappingURL=PostgrestTransformBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(__webpack_require__(/*! ./PostgrestClient */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports[\"default\"] = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default,\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgrestBuilder: () => (/* binding */ PostgrestBuilder),\n/* harmony export */   PostgrestClient: () => (/* binding */ PostgrestClient),\n/* harmony export */   PostgrestError: () => (/* binding */ PostgrestError),\n/* harmony export */   PostgrestFilterBuilder: () => (/* binding */ PostgrestFilterBuilder),\n/* harmony export */   PostgrestQueryBuilder: () => (/* binding */ PostgrestQueryBuilder),\n/* harmony export */   PostgrestTransformBuilder: () => (/* binding */ PostgrestTransformBuilder),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cjs/index.js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/cjs/index.js\");\n\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__\n\n\n\n// compatibility with CJS output\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjIxLjAvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9lc20vd3JhcHBlci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLEVBQUUsMENBQUs7O0FBU1I7O0FBRUQ7QUFDQSxpRUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVuZG9hcnNhbmRpXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoZXNzbGxtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMjEuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHBvc3RncmVzdC1qc1xcZGlzdFxcZXNtXFx3cmFwcGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaW5kZXggZnJvbSAnLi4vY2pzL2luZGV4LmpzJ1xuY29uc3Qge1xuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59ID0gaW5kZXhcblxuZXhwb3J0IHtcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RGaWx0ZXJCdWlsZGVyLFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufVxuXG4vLyBjb21wYXRpYmlsaXR5IHdpdGggQ0pTIG91dHB1dFxuZXhwb3J0IGRlZmF1bHQge1xuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\n");

/***/ })

};
;
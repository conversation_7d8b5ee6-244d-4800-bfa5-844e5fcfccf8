/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chess-orchestrator/route";
exports.ids = ["app/api/chess-orchestrator/route"];
exports.modules = {

/***/ "(rsc)/./app/api/chess-orchestrator/route.ts":
/*!*********************************************!*\
  !*** ./app/api/chess-orchestrator/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var chess_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! chess.js */ \"(rsc)/./node_modules/.pnpm/chess.js@1.4.0/node_modules/chess.js/dist/esm/chess.js\");\n/* harmony import */ var _ai_sdk_google__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @ai-sdk/google */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+google@1.2.22_zod@3.25.76/node_modules/@ai-sdk/google/dist/index.mjs\");\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ai */ \"(rsc)/./node_modules/.pnpm/ai@4.3.17_react@19.1.0_zod@3.25.76/node_modules/ai/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/types.js\");\n/* harmony import */ var _lib_llm_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/llm-providers */ \"(rsc)/./lib/llm-providers.ts\");\n\n\n\n\n\n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://gkhscuawgjvqmjxadmdg.supabase.co\" || 0, process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-key');\nconst moveSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    move: zod__WEBPACK_IMPORTED_MODULE_4__.string().describe(\"The chess move in standard algebraic notation (e.g., e4, Nf3, O-O)\"),\n    thinking: zod__WEBPACK_IMPORTED_MODULE_4__.string().describe(\"Your detailed thought process for choosing this move\"),\n    evaluation: zod__WEBPACK_IMPORTED_MODULE_4__.number().describe(\"Position evaluation from -10 to +10 (positive favors white)\")\n});\nconst MAX_ILLEGAL_ATTEMPTS = 5;\nasync function POST(req) {\n    try {\n        const { gameId } = await req.json();\n        if (!gameId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Game ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get current game state\n        const { data: game, error: gameError } = await supabase.from(\"games\").select(\"*\").eq(\"id\", gameId).single();\n        if (gameError || !game) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Game not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (game.status !== \"in_progress\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Game is not in progress\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize chess engine with current position\n        const chess = new chess_js__WEBPACK_IMPORTED_MODULE_1__.Chess(game.fen);\n        // Get move history for context\n        const { data: moves } = await supabase.from(\"moves\").select(\"*\").eq(\"game_id\", gameId).order(\"move_number\", {\n            ascending: true\n        });\n        const moveHistory = moves?.map((m)=>m.move_san).join(\" \") || \"\";\n        const currentPlayer = game.current_turn;\n        const currentLLM = currentPlayer === \"white\" ? game.white_player_llm : game.black_player_llm;\n        const illegalAttempts = currentPlayer === \"white\" ? game.white_illegal_attempts : game.black_illegal_attempts;\n        console.log(`Processing move for ${currentPlayer} using ${currentLLM}`);\n        // Check if player has exceeded max attempts\n        if (illegalAttempts >= MAX_ILLEGAL_ATTEMPTS) {\n            const winner = currentPlayer === \"white\" ? \"black\" : \"white\";\n            await supabase.from(\"games\").update({\n                status: \"resigned\",\n                winner: winner\n            }).eq(\"id\", gameId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: `${currentPlayer} resigned after ${MAX_ILLEGAL_ATTEMPTS} illegal move attempts`,\n                winner\n            });\n        }\n        // Attempt to get a legal move (with retry logic)\n        let attempt = 0;\n        let successfulMove = false;\n        while(attempt < MAX_ILLEGAL_ATTEMPTS && !successfulMove){\n            attempt++;\n            console.log(`Attempt ${attempt} for ${currentPlayer} (${currentLLM})`);\n            try {\n                // Create prompt based on attempt number\n                let prompt = createPrompt(currentPlayer, currentLLM, game.fen, moveHistory, chess.moves());\n                if (attempt > 1) {\n                    prompt += `\\n\\nIMPORTANT: Your previous ${attempt - 1} attempt(s) resulted in illegal moves. Please be extra careful and only choose from the legal moves listed above.`;\n                }\n                // Get AI response based on the model\n                let result;\n                if (currentLLM.includes(\"gemini\")) {\n                    console.log(`Calling Gemini with model: ${currentLLM}`);\n                    // Use Google AI for Gemini - try multiple model variations\n                    const modelVariations = [\n                        currentLLM,\n                        \"gemini-2.5-flash\",\n                        \"gemini-2.5-pro\",\n                        \"gemini-pro\"\n                    ];\n                    let geminiError = null;\n                    for (const modelId of modelVariations){\n                        try {\n                            console.log(`Trying Gemini model: ${modelId}`);\n                            result = await (0,ai__WEBPACK_IMPORTED_MODULE_5__.generateObject)({\n                                model: (0,_ai_sdk_google__WEBPACK_IMPORTED_MODULE_6__.google)(modelId, {\n                                    apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY\n                                }),\n                                schema: moveSchema,\n                                prompt\n                            });\n                            console.log(`Gemini ${modelId} responded successfully`);\n                            break;\n                        } catch (error) {\n                            console.error(`Gemini ${modelId} failed:`, error);\n                            geminiError = error;\n                            continue;\n                        }\n                    }\n                    if (!result) {\n                        throw geminiError || new Error(\"All Gemini model variations failed\");\n                    }\n                } else if (currentLLM.startsWith(\"openai/\")) {\n                    console.log(`Calling OpenAI with model: ${currentLLM}`);\n                    // Use OpenRouter for OpenAI models\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`OpenAI model responded successfully`);\n                } else if (currentLLM.startsWith(\"anthropic/\")) {\n                    console.log(`Calling Anthropic with model: ${currentLLM}`);\n                    // Use OpenRouter for Anthropic models\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`Anthropic model responded successfully`);\n                } else if (currentLLM.startsWith(\"deepseek/\") || currentLLM.includes(\"deepseek\")) {\n                    console.log(`Calling DeepSeek with model: ${currentLLM}`);\n                    // Use OpenRouter for DeepSeek\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`DeepSeek responded successfully`);\n                } else if (currentLLM.startsWith(\"meta-llama/\") || currentLLM.includes(\"llama\")) {\n                    console.log(`Calling Llama with model: ${currentLLM}`);\n                    // Use OpenRouter for Llama models\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`Llama model responded successfully`);\n                } else if (currentLLM.startsWith(\"qwen/\") || currentLLM.includes(\"qwen\")) {\n                    console.log(`Calling Qwen with model: ${currentLLM}`);\n                    // Use OpenRouter for Qwen models\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`Qwen model responded successfully`);\n                } else if (currentLLM.startsWith(\"mistralai/\") || currentLLM.includes(\"mistral\")) {\n                    console.log(`Calling Mistral with model: ${currentLLM}`);\n                    // Use OpenRouter for Mistral models\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`Mistral model responded successfully`);\n                } else {\n                    // Fallback to OpenRouter for any other model\n                    console.log(`Calling unknown model via OpenRouter: ${currentLLM}`);\n                    result = await callOpenRouter(currentLLM, prompt);\n                    console.log(`Model responded successfully`);\n                }\n                // ----- helper: clean raw string -> pure SAN -----\n                function sanitizeMove(raw) {\n                    // strip JSON quotes, prefixes like \"move:\", bullet points, etc.\n                    const clean = raw.replace(/^[^\\w]*move[:-]?\\s*/i, \"\") // remove leading \"move:\"\n                    .replace(/^[-*\\d.\\s]+/, \"\") // bullets / numbers\n                    .split(/\\s+/)[0] // keep first token\n                    .trim();\n                    return clean;\n                }\n                const proposedMove = sanitizeMove(result.object.move);\n                const legalMoves = chess.moves();\n                if (!proposedMove || !legalMoves.includes(proposedMove)) {\n                    console.log(`Attempt ${attempt}: AI proposed illegal/empty move \"${proposedMove}\". Legal moves:`, legalMoves.slice(0, 10));\n                    // increment illegal counter without throwing\n                    const updateField = currentPlayer === \"white\" ? \"white_illegal_attempts\" : \"black_illegal_attempts\";\n                    await supabase.from(\"games\").update({\n                        [updateField]: illegalAttempts + 1\n                    }).eq(\"id\", gameId);\n                    continue; // retry next loop iteration\n                }\n                const thinking = result.object.thinking;\n                const evaluation = result.object.evaluation;\n                console.log(`${currentPlayer} proposed move: ${proposedMove}`);\n                // Validate and make the move\n                const moveResult = chess.move(proposedMove);\n                if (moveResult) {\n                    // Legal move! Update database\n                    successfulMove = true;\n                    console.log(`Legal move confirmed: ${moveResult.san}`);\n                    const newFen = chess.fen();\n                    const nextTurn = currentPlayer === \"white\" ? \"black\" : \"white\";\n                    const moveNumber = (moves?.length || 0) + 1;\n                    // Check for game end conditions\n                    let gameStatus = \"in_progress\";\n                    let winner = null;\n                    if (chess.isCheckmate()) {\n                        gameStatus = \"completed\";\n                        winner = currentPlayer;\n                    } else if (chess.isDraw() || chess.isStalemate()) {\n                        gameStatus = \"draw\";\n                    }\n                    // Update game state\n                    await supabase.from(\"games\").update({\n                        fen: newFen,\n                        current_turn: nextTurn,\n                        status: gameStatus,\n                        winner: winner,\n                        // Reset illegal attempts for current player\n                        ...currentPlayer === \"white\" ? {\n                            white_illegal_attempts: 0\n                        } : {\n                            black_illegal_attempts: 0\n                        }\n                    }).eq(\"id\", gameId);\n                    // Record the move\n                    await supabase.from(\"moves\").insert({\n                        game_id: gameId,\n                        move_number: moveNumber,\n                        player_color: currentPlayer,\n                        move_pgn: proposedMove,\n                        move_san: moveResult.san,\n                        llm_thinking_process: thinking\n                    });\n                    console.log(`Move recorded successfully: ${moveResult.san}`);\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        move: moveResult.san,\n                        thinking,\n                        evaluation,\n                        gameStatus,\n                        winner\n                    });\n                } else {\n                    // Illegal move, increment attempt counter\n                    console.log(`Illegal move attempt ${attempt}: ${proposedMove}`);\n                    // Update illegal attempts counter\n                    const updateField = currentPlayer === \"white\" ? \"white_illegal_attempts\" : \"black_illegal_attempts\";\n                    await supabase.from(\"games\").update({\n                        [updateField]: illegalAttempts + attempt\n                    }).eq(\"id\", gameId);\n                }\n            } catch (error) {\n                console.error(`Error on attempt ${attempt}:`, error);\n            }\n        }\n        // If we get here, all attempts failed\n        const winner = currentPlayer === \"white\" ? \"black\" : \"white\";\n        await supabase.from(\"games\").update({\n            status: \"resigned\",\n            winner: winner\n        }).eq(\"id\", gameId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: `${currentPlayer} resigned after ${MAX_ILLEGAL_ATTEMPTS} illegal move attempts`,\n            winner\n        });\n    } catch (error) {\n        console.error(\"Error in chess orchestrator:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function callOpenRouter(model, prompt) {\n    console.log(`Calling OpenRouter with model: ${model}`);\n    if (!process.env.OPENROUTER_API_KEY) {\n        throw new Error(\"OPENROUTER_API_KEY is not set\");\n    }\n    const res = await fetch(\"https://openrouter.ai/api/v1/chat/completions\", {\n        method: \"POST\",\n        headers: {\n            Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,\n            \"Content-Type\": \"application/json\",\n            \"X-Title\": \"Chess AI Battle\",\n            \"HTTP-Referer\": \"https://chessllm.vercel.app\"\n        },\n        body: JSON.stringify({\n            model,\n            stream: false,\n            temperature: 0.7,\n            max_tokens: 1500,\n            messages: [\n                {\n                    role: \"system\",\n                    content: \"You are a chess grandmaster. Analyze the position carefully and respond with your move and reasoning. Be precise and follow the exact format requested.\"\n                },\n                {\n                    role: \"user\",\n                    content: `${prompt}\n\nIMPORTANT: Respond with a JSON object in this EXACT format:\n{\n  \"move\": \"your_move_in_algebraic_notation\",\n  \"thinking\": \"your_detailed_analysis_and_reasoning\",\n  \"evaluation\": numerical_evaluation_from_-10_to_10\n}\n\nExample:\n{\n  \"move\": \"e4\",\n  \"thinking\": \"I'm opening with the king's pawn to control the center and develop quickly...\",\n  \"evaluation\": 0.3\n}`\n                }\n            ]\n        })\n    });\n    if (!res.ok) {\n        const errorText = await res.text();\n        console.error(`OpenRouter API error ${res.status}:`, errorText);\n        throw new Error(`OpenRouter API error ${res.status}: ${res.statusText}`);\n    }\n    const raw = await res.text();\n    console.log(\"↩︎ OpenRouter raw (truncated):\", raw.slice(0, 300).replace(/\\n/g, \" \"));\n    return parseOpenRouterResponse(raw);\n}\n/* -------- helper to convert OpenRouter response -> { object: … } ---------- */ function parseOpenRouterResponse(raw) {\n    try {\n        // First try to parse as OpenRouter API response\n        const apiResponse = JSON.parse(raw);\n        if (apiResponse.choices && apiResponse.choices[0] && apiResponse.choices[0].message) {\n            const content = apiResponse.choices[0].message.content;\n            return parseContentForChessMove(content);\n        }\n    } catch (e) {\n        console.log(\"Not a valid OpenRouter API response, trying direct parsing\");\n    }\n    // Fallback to direct content parsing\n    return parseContentForChessMove(raw);\n}\nfunction parseContentForChessMove(content) {\n    // Try to extract JSON from the content\n    const jsonMatch = content.match(/\\{[\\s\\S]*?\\}/);\n    if (jsonMatch) {\n        try {\n            const parsed = JSON.parse(jsonMatch[0]);\n            return {\n                object: {\n                    move: parsed.move || \"\",\n                    thinking: parsed.thinking || content,\n                    evaluation: Number(parsed.evaluation) || 0\n                }\n            };\n        } catch (e) {\n            console.log(\"Failed to parse JSON, falling back to regex extraction\");\n        }\n    }\n    // Fallback: extract move using regex patterns\n    const movePatterns = [\n        /(?:move|Move):?\\s*[\"']?([NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?|O-O(?:-O)?[+#]?)[\"']?/i,\n        /[\"']([NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?|O-O(?:-O)?[+#]?)[\"']/,\n        /\\b([NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?[+#]?|O-O(?:-O)?[+#]?)\\b/\n    ];\n    let extractedMove = \"\";\n    for (const pattern of movePatterns){\n        const match = content.match(pattern);\n        if (match && match[1]) {\n            extractedMove = match[1];\n            break;\n        }\n    }\n    return {\n        object: {\n            move: extractedMove,\n            thinking: content,\n            evaluation: 0\n        }\n    };\n}\nfunction createPrompt(currentPlayer, llmModel, fen, moveHistory, legalMoves) {\n    // Get model information from our providers list\n    const modelInfo = (0,_lib_llm_providers__WEBPACK_IMPORTED_MODULE_2__.getModelById)(llmModel);\n    const modelName = modelInfo ? modelInfo.name : llmModel.split('/').pop() || llmModel;\n    const providerName = modelInfo ? modelInfo.provider : llmModel.split('/')[0] || \"AI\";\n    return `You are a chess Grandmaster representing ${modelName} by ${providerName}, playing as ${currentPlayer.toUpperCase()}.\n\nCurrent Position (FEN): ${fen}\nGame History: ${moveHistory || \"Game just started\"}\n\nLegal moves available: ${legalMoves.join(\", \")}\n\nThis is a high-stakes AI vs AI chess battle! Analyze this position with maximum depth and choose your best move. Consider:\n\n1. **Tactical Analysis**: Look for immediate threats, pins, forks, skewers, and tactical motifs\n2. **Strategic Elements**: Piece activity, pawn structure, king safety, control of key squares\n3. **Positional Factors**: Space advantage, weak squares, piece coordination\n4. **Endgame Considerations**: If applicable, evaluate the resulting endgame\n5. **Psychological Pressure**: This is a battle between AI models - play your strongest move!\n\nThink like a world champion and provide your detailed analysis, then choose your move.\n\nCRITICAL REQUIREMENTS:\n- You MUST choose from the legal moves listed above\n- Use standard algebraic notation (e.g., e4, Nf3, Bxf7+, O-O, Qh5#)\n- Do not use coordinate notation (e.g., e2-e4)\n- Double-check that your chosen move is in the legal moves list\n\nShow your analytical prowess and make ${modelName} proud!`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chess-orchestrator/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/llm-providers.ts":
/*!******************************!*\
  !*** ./lib/llm-providers.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS: () => (/* binding */ LLM_PROVIDERS),\n/* harmony export */   getFreeModels: () => (/* binding */ getFreeModels),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getModelsByCategory: () => (/* binding */ getModelsByCategory),\n/* harmony export */   getPaidModels: () => (/* binding */ getPaidModels),\n/* harmony export */   getRecommendedChessModels: () => (/* binding */ getRecommendedChessModels)\n/* harmony export */ });\n// OpenRouter LLM Providers Configuration\n// Updated: July 2025\nconst LLM_PROVIDERS = [\n    // FREE MODELS\n    {\n        id: \"deepseek/deepseek-r1:free\",\n        name: \"DeepSeek R1 (Free)\",\n        description: \"DeepSeek's reasoning model with strong analytical capabilities\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"reasoning\",\n            \"math\",\n            \"coding\",\n            \"analysis\"\n        ],\n        category: \"reasoning\",\n        provider: \"DeepSeek\"\n    },\n    {\n        id: \"deepseek/deepseek-r1-distill-llama-70b:free\",\n        name: \"DeepSeek R1 Distill Llama 70B (Free)\",\n        description: \"Distilled version of DeepSeek R1 based on Llama architecture\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"reasoning\",\n            \"general\",\n            \"coding\"\n        ],\n        category: \"reasoning\",\n        provider: \"DeepSeek\"\n    },\n    {\n        id: \"deepseek/deepseek-r1-distill-qwen-14b:free\",\n        name: \"DeepSeek R1 Distill Qwen 14B (Free)\",\n        description: \"Distilled reasoning model based on Qwen architecture\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"reasoning\",\n            \"general\",\n            \"coding\"\n        ],\n        category: \"reasoning\",\n        provider: \"DeepSeek\"\n    },\n    {\n        id: \"deepseek/deepseek-r1-distill-qwen-7b:free\",\n        name: \"DeepSeek R1 Distill Qwen 7B (Free)\",\n        description: \"Smaller distilled reasoning model for faster inference\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"reasoning\",\n            \"general\"\n        ],\n        category: \"fast\",\n        provider: \"DeepSeek\"\n    },\n    {\n        id: \"deepseek/deepseek-r1-distill-llama-8b:free\",\n        name: \"DeepSeek R1 Distill Llama 8B (Free)\",\n        description: \"Compact distilled model for efficient reasoning\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"reasoning\",\n            \"general\"\n        ],\n        category: \"fast\",\n        provider: \"DeepSeek\"\n    },\n    {\n        id: \"deepseek/deepseek-v3:free\",\n        name: \"DeepSeek V3 (Free)\",\n        description: \"Latest general-purpose model from DeepSeek\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"general\",\n            \"coding\",\n            \"reasoning\"\n        ],\n        category: \"general\",\n        provider: \"DeepSeek\"\n    },\n    {\n        id: \"meta-llama/llama-3.3-70b-instruct:free\",\n        name: \"Llama 3.3 70B Instruct (Free)\",\n        description: \"Meta's latest Llama model with strong instruction following\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"general\",\n            \"coding\",\n            \"reasoning\"\n        ],\n        category: \"general\",\n        provider: \"Meta\"\n    },\n    {\n        id: \"meta-llama/llama-3.1-8b-instruct:free\",\n        name: \"Llama 3.1 8B Instruct (Free)\",\n        description: \"Efficient Llama model for general tasks\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"general\",\n            \"coding\"\n        ],\n        category: \"fast\",\n        provider: \"Meta\"\n    },\n    {\n        id: \"qwen/qwen-2.5-coder-32b-instruct:free\",\n        name: \"Qwen 2.5 Coder 32B (Free)\",\n        description: \"Specialized coding model from Alibaba\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"coding\",\n            \"programming\",\n            \"debugging\"\n        ],\n        category: \"coding\",\n        provider: \"Alibaba\"\n    },\n    {\n        id: \"qwen/qwen3-32b:free\",\n        name: \"Qwen 3 32B (Free)\",\n        description: \"Latest general-purpose model from Qwen series\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"general\",\n            \"reasoning\",\n            \"coding\"\n        ],\n        category: \"general\",\n        provider: \"Alibaba\"\n    },\n    {\n        id: \"mistralai/mistral-small-3.2-24b:free\",\n        name: \"Mistral Small 3.2 24B (Free)\",\n        description: \"Efficient model from Mistral AI with good performance\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"general\",\n            \"reasoning\"\n        ],\n        category: \"general\",\n        provider: \"Mistral AI\"\n    },\n    {\n        id: \"mistralai/mistral-nemo:free\",\n        name: \"Mistral Nemo (Free)\",\n        description: \"Compact model optimized for efficiency\",\n        isFree: true,\n        contextLength: 131072,\n        capabilities: [\n            \"general\",\n            \"fast\"\n        ],\n        category: \"fast\",\n        provider: \"Mistral AI\"\n    },\n    // PAID MODELS (Popular ones for chess)\n    {\n        id: \"openai/gpt-4o\",\n        name: \"GPT-4o\",\n        description: \"OpenAI's flagship multimodal model with excellent reasoning\",\n        isFree: false,\n        pricing: {\n            prompt: \"0.0000025\",\n            completion: \"0.00001\"\n        },\n        contextLength: 128000,\n        capabilities: [\n            \"reasoning\",\n            \"coding\",\n            \"analysis\",\n            \"multimodal\"\n        ],\n        category: \"reasoning\",\n        provider: \"OpenAI\"\n    },\n    {\n        id: \"openai/gpt-4o-mini\",\n        name: \"GPT-4o Mini\",\n        description: \"Faster and more affordable version of GPT-4o\",\n        isFree: false,\n        pricing: {\n            prompt: \"0.00000015\",\n            completion: \"0.0000006\"\n        },\n        contextLength: 128000,\n        capabilities: [\n            \"reasoning\",\n            \"coding\",\n            \"general\"\n        ],\n        category: \"fast\",\n        provider: \"OpenAI\"\n    },\n    {\n        id: \"anthropic/claude-3.5-sonnet\",\n        name: \"Claude 3.5 Sonnet\",\n        description: \"Anthropic's balanced model with strong reasoning and coding\",\n        isFree: false,\n        pricing: {\n            prompt: \"0.000003\",\n            completion: \"0.000015\"\n        },\n        contextLength: 200000,\n        capabilities: [\n            \"reasoning\",\n            \"coding\",\n            \"analysis\",\n            \"creative\"\n        ],\n        category: \"reasoning\",\n        provider: \"Anthropic\"\n    },\n    {\n        id: \"google/gemini-2.5-flash\",\n        name: \"Gemini 2.5 Flash\",\n        description: \"Google's fast and efficient model with good reasoning\",\n        isFree: false,\n        pricing: {\n            prompt: \"0.000000075\",\n            completion: \"0.0000003\"\n        },\n        contextLength: 1048576,\n        capabilities: [\n            \"reasoning\",\n            \"coding\",\n            \"multimodal\",\n            \"fast\"\n        ],\n        category: \"fast\",\n        provider: \"Google\"\n    },\n    {\n        id: \"google/gemini-2.5-pro\",\n        name: \"Gemini 2.5 Pro\",\n        description: \"Google's most capable model with advanced reasoning\",\n        isFree: false,\n        pricing: {\n            prompt: \"0.00000125\",\n            completion: \"0.000005\"\n        },\n        contextLength: 2097152,\n        capabilities: [\n            \"reasoning\",\n            \"coding\",\n            \"analysis\",\n            \"multimodal\"\n        ],\n        category: \"reasoning\",\n        provider: \"Google\"\n    },\n    {\n        id: \"deepseek/deepseek-r1\",\n        name: \"DeepSeek R1\",\n        description: \"Full version of DeepSeek's reasoning model\",\n        isFree: false,\n        pricing: {\n            prompt: \"0.000002\",\n            completion: \"0.000008\"\n        },\n        contextLength: 131072,\n        capabilities: [\n            \"reasoning\",\n            \"math\",\n            \"coding\",\n            \"analysis\"\n        ],\n        category: \"reasoning\",\n        provider: \"DeepSeek\"\n    }\n];\n// Helper functions\nfunction getFreeModels() {\n    return LLM_PROVIDERS.filter((model)=>model.isFree);\n}\nfunction getPaidModels() {\n    return LLM_PROVIDERS.filter((model)=>!model.isFree);\n}\nfunction getModelsByCategory(category) {\n    return LLM_PROVIDERS.filter((model)=>model.category === category);\n}\nfunction getModelById(id) {\n    return LLM_PROVIDERS.find((model)=>model.id === id);\n}\nfunction getRecommendedChessModels() {\n    // Models that are particularly good for chess reasoning\n    return LLM_PROVIDERS.filter((model)=>model.capabilities.includes('reasoning') && (model.isFree || model.id.includes('gpt-4') || model.id.includes('claude') || model.id.includes('gemini')));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/llm-providers.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchess-orchestrator%2Froute&page=%2Fapi%2Fchess-orchestrator%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchess-orchestrator%2Froute.ts&appDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchess-orchestrator%2Froute&page=%2Fapi%2Fchess-orchestrator%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchess-orchestrator%2Froute.ts&appDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_rendoarsandi_Documents_augment_projects_chessllm_app_api_chess_orchestrator_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chess-orchestrator/route.ts */ \"(rsc)/./app/api/chess-orchestrator/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chess-orchestrator/route\",\n        pathname: \"/api/chess-orchestrator\",\n        filename: \"route\",\n        bundlePath: \"app/api/chess-orchestrator/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\api\\\\chess-orchestrator\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_rendoarsandi_Documents_augment_projects_chessllm_app_api_chess_orchestrator_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchess-orchestrator%2Froute&page=%2Fapi%2Fchess-orchestrator%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchess-orchestrator%2Froute.ts&appDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?08b1":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?5fe5":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/tr46@0.0.3","vendor-chunks/@supabase+auth-js@2.70.0","vendor-chunks/ws@8.18.3","vendor-chunks/chess.js@1.4.0","vendor-chunks/@supabase+realtime-js@2.11.15","vendor-chunks/@supabase+postgrest-js@1.21.0","vendor-chunks/@supabase+node-fetch@2.6.15","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@supabase+supabase-js@2.50.4","vendor-chunks/@supabase+functions-js@2.4.5","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/isows@1.0.7_ws@8.18.3","vendor-chunks/zod-to-json-schema@3.24.6_zod@3.25.76","vendor-chunks/zod@3.25.76","vendor-chunks/nanoid@3.3.11","vendor-chunks/ai@4.3.17_react@19.1.0_zod@3.25.76","vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.25.76","vendor-chunks/@ai-sdk+provider@1.1.3","vendor-chunks/@ai-sdk+provider-utils@2.2.8_zod@3.25.76","vendor-chunks/@ai-sdk+google@1.2.22_zod@3.25.76","vendor-chunks/secure-json-parse@2.7.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchess-orchestrator%2Froute&page=%2Fapi%2Fchess-orchestrator%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchess-orchestrator%2Froute.ts&appDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Crendoarsandi%5CDocuments%5Caugment-projects%5Cchessllm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
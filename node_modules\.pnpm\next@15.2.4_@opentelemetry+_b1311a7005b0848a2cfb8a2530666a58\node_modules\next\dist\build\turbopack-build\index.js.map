{"version": 3, "sources": ["../../../src/build/turbopack-build/index.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../../server/lib/utils'\nimport { Worker } from '../../lib/worker'\nimport { NextBuildContext } from '../build-context'\n\nasync function turbopackBuildWithWorker() {\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n\n  try {\n    const worker = new Worker(path.join(__dirname, 'impl.js'), {\n      exposedMethods: ['workerMain', 'waitForShutdown'],\n      numWorkers: 1,\n      maxRetries: 0,\n      forkOptions: {\n        env: {\n          ...process.env,\n          NEXT_PRIVATE_BUILD_WORKER: '1',\n          NODE_OPTIONS: formatNodeOptions(nodeOptions),\n        },\n      },\n    }) as Worker & typeof import('./impl')\n    const { nextBuildSpan, ...prunedBuildContext } = NextBuildContext\n    const result = await worker.workerMain({\n      buildContext: prunedBuildContext,\n    })\n\n    // destroy worker when <PERSON><PERSON> has shutdown so it's not sticking around using memory\n    // We need to wait for shutdown to make sure persistent cache is flushed\n    result.shutdownPromise = worker.waitForShutdown().then(() => {\n      worker.end()\n    })\n\n    return result\n  } catch (err: any) {\n    // When the error is a serialized `Error` object we need to recreate the `Error` instance\n    // in order to keep the consistent error reporting behavior.\n    if (err.type === 'Error') {\n      const error = new Error(err.message)\n      if (err.name) {\n        error.name = err.name\n      }\n      if (err.cause) {\n        error.cause = err.cause\n      }\n      error.message = err.message\n      error.stack = err.stack\n      throw error\n    }\n    throw err\n  }\n}\n\nexport function turbopackBuild(\n  withWorker: boolean\n): ReturnType<typeof import('./impl').turbopackBuild> {\n  if (withWorker) {\n    return turbopackBuildWithWorker()\n  } else {\n    const build = (require('./impl') as typeof import('./impl')).turbopackBuild\n    return build()\n  }\n}\n"], "names": ["turbopackBuild", "turbopackBuildWithWorker", "nodeOptions", "getParsedNodeOptionsWithoutInspect", "worker", "Worker", "path", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "NODE_OPTIONS", "formatNodeOptions", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "result", "worker<PERSON>ain", "buildContext", "shutdownPromise", "waitForShutdown", "then", "end", "err", "type", "error", "Error", "message", "name", "cause", "stack", "with<PERSON><PERSON>ker", "build", "require"], "mappings": ";;;;+BAuDgBA;;;eAAAA;;;6DAvDC;uBAIV;wBACgB;8BACU;;;;;;AAEjC,eAAeC;IACb,MAAMC,cAAcC,IAAAA,yCAAkC;IAEtD,IAAI;QACF,MAAMC,SAAS,IAAIC,cAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;gBAAc;aAAkB;YACjDC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;oBAC3BC,cAAcC,IAAAA,wBAAiB,EAACf;gBAClC;YACF;QACF;QACA,MAAM,EAAEgB,aAAa,EAAE,GAAGC,oBAAoB,GAAGC,8BAAgB;QACjE,MAAMC,SAAS,MAAMjB,OAAOkB,UAAU,CAAC;YACrCC,cAAcJ;QAChB;QAEA,sFAAsF;QACtF,wEAAwE;QACxEE,OAAOG,eAAe,GAAGpB,OAAOqB,eAAe,GAAGC,IAAI,CAAC;YACrDtB,OAAOuB,GAAG;QACZ;QAEA,OAAON;IACT,EAAE,OAAOO,KAAU;QACjB,yFAAyF;QACzF,4DAA4D;QAC5D,IAAIA,IAAIC,IAAI,KAAK,SAAS;YACxB,MAAMC,QAAQ,qBAAsB,CAAtB,IAAIC,MAAMH,IAAII,OAAO,GAArB,qBAAA;uBAAA;4BAAA;8BAAA;YAAqB;YACnC,IAAIJ,IAAIK,IAAI,EAAE;gBACZH,MAAMG,IAAI,GAAGL,IAAIK,IAAI;YACvB;YACA,IAAIL,IAAIM,KAAK,EAAE;gBACbJ,MAAMI,KAAK,GAAGN,IAAIM,KAAK;YACzB;YACAJ,MAAME,OAAO,GAAGJ,IAAII,OAAO;YAC3BF,MAAMK,KAAK,GAAGP,IAAIO,KAAK;YACvB,MAAML;QACR;QACA,MAAMF;IACR;AACF;AAEO,SAAS5B,eACdoC,UAAmB;IAEnB,IAAIA,YAAY;QACd,OAAOnC;IACT,OAAO;QACL,MAAMoC,QAAQ,AAACC,QAAQ,UAAsCtC,cAAc;QAC3E,OAAOqC;IACT;AACF"}
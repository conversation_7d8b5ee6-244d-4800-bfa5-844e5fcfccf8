{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/file-resolve.ts"], "sourcesContent": ["export function cssFileResolve(\n  url: string,\n  _resourcePath: string,\n  urlImports: any\n) {\n  if (url.startsWith('/')) {\n    return false\n  }\n  if (!urlImports && /^[a-z][a-z0-9+.-]*:/i.test(url)) {\n    return false\n  }\n  return true\n}\n"], "names": ["cssFileResolve", "url", "_resourcePath", "urlImports", "startsWith", "test"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,eACdC,GAAW,EACXC,aAAqB,EACrBC,UAAe;IAEf,IAAIF,IAAIG,UAAU,CAAC,MAAM;QACvB,OAAO;IACT;IACA,IAAI,CAACD,cAAc,uBAAuBE,IAAI,CAACJ,MAAM;QACnD,OAAO;IACT;IACA,OAAO;AACT"}
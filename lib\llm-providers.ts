// OpenRouter LLM Providers Configuration
// Updated: July 2025

export interface LLMProvider {
  id: string
  name: string
  description: string
  isFree: boolean
  pricing?: {
    prompt: string
    completion: string
  }
  contextLength: number
  capabilities: string[]
  category: 'reasoning' | 'coding' | 'general' | 'creative' | 'fast'
  provider: string
}

export const LLM_PROVIDERS: LLMProvider[] = [
  // FREE MODELS
  {
    id: "deepseek/deepseek-r1:free",
    name: "DeepSeek R1 (Free)",
    description: "DeepSeek's reasoning model with strong analytical capabilities",
    isFree: true,
    contextLength: 131072,
    capabilities: ["reasoning", "math", "coding", "analysis"],
    category: "reasoning",
    provider: "DeepSeek"
  },
  {
    id: "deepseek/deepseek-r1-distill-llama-70b:free",
    name: "DeepSeek R1 Distill Llama 70B (Free)",
    description: "Distilled version of DeepSeek R1 based on Llama architecture",
    isFree: true,
    contextLength: 131072,
    capabilities: ["reasoning", "general", "coding"],
    category: "reasoning",
    provider: "DeepSeek"
  },
  {
    id: "deepseek/deepseek-r1-distill-qwen-14b:free",
    name: "DeepSeek R1 Distill Qwen 14B (Free)",
    description: "Distilled reasoning model based on Qwen architecture",
    isFree: true,
    contextLength: 131072,
    capabilities: ["reasoning", "general", "coding"],
    category: "reasoning",
    provider: "DeepSeek"
  },
  {
    id: "deepseek/deepseek-r1-distill-qwen-7b:free",
    name: "DeepSeek R1 Distill Qwen 7B (Free)",
    description: "Smaller distilled reasoning model for faster inference",
    isFree: true,
    contextLength: 131072,
    capabilities: ["reasoning", "general"],
    category: "fast",
    provider: "DeepSeek"
  },
  {
    id: "deepseek/deepseek-r1-distill-llama-8b:free",
    name: "DeepSeek R1 Distill Llama 8B (Free)",
    description: "Compact distilled model for efficient reasoning",
    isFree: true,
    contextLength: 131072,
    capabilities: ["reasoning", "general"],
    category: "fast",
    provider: "DeepSeek"
  },
  {
    id: "deepseek/deepseek-v3:free",
    name: "DeepSeek V3 (Free)",
    description: "Latest general-purpose model from DeepSeek",
    isFree: true,
    contextLength: 131072,
    capabilities: ["general", "coding", "reasoning"],
    category: "general",
    provider: "DeepSeek"
  },
  {
    id: "meta-llama/llama-3.3-70b-instruct:free",
    name: "Llama 3.3 70B Instruct (Free)",
    description: "Meta's latest Llama model with strong instruction following",
    isFree: true,
    contextLength: 131072,
    capabilities: ["general", "coding", "reasoning"],
    category: "general",
    provider: "Meta"
  },
  {
    id: "meta-llama/llama-3.1-8b-instruct:free",
    name: "Llama 3.1 8B Instruct (Free)",
    description: "Efficient Llama model for general tasks",
    isFree: true,
    contextLength: 131072,
    capabilities: ["general", "coding"],
    category: "fast",
    provider: "Meta"
  },
  {
    id: "qwen/qwen-2.5-coder-32b-instruct:free",
    name: "Qwen 2.5 Coder 32B (Free)",
    description: "Specialized coding model from Alibaba",
    isFree: true,
    contextLength: 131072,
    capabilities: ["coding", "programming", "debugging"],
    category: "coding",
    provider: "Alibaba"
  },
  {
    id: "qwen/qwen3-32b:free",
    name: "Qwen 3 32B (Free)",
    description: "Latest general-purpose model from Qwen series",
    isFree: true,
    contextLength: 131072,
    capabilities: ["general", "reasoning", "coding"],
    category: "general",
    provider: "Alibaba"
  },
  {
    id: "mistralai/mistral-small-3.2-24b:free",
    name: "Mistral Small 3.2 24B (Free)",
    description: "Efficient model from Mistral AI with good performance",
    isFree: true,
    contextLength: 131072,
    capabilities: ["general", "reasoning"],
    category: "general",
    provider: "Mistral AI"
  },
  {
    id: "mistralai/mistral-nemo:free",
    name: "Mistral Nemo (Free)",
    description: "Compact model optimized for efficiency",
    isFree: true,
    contextLength: 131072,
    capabilities: ["general", "fast"],
    category: "fast",
    provider: "Mistral AI"
  },

  // PAID MODELS (Popular ones for chess)
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    description: "OpenAI's flagship multimodal model with excellent reasoning",
    isFree: false,
    pricing: {
      prompt: "0.0000025",
      completion: "0.00001"
    },
    contextLength: 128000,
    capabilities: ["reasoning", "coding", "analysis", "multimodal"],
    category: "reasoning",
    provider: "OpenAI"
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    description: "Faster and more affordable version of GPT-4o",
    isFree: false,
    pricing: {
      prompt: "0.00000015",
      completion: "0.0000006"
    },
    contextLength: 128000,
    capabilities: ["reasoning", "coding", "general"],
    category: "fast",
    provider: "OpenAI"
  },
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    description: "Anthropic's balanced model with strong reasoning and coding",
    isFree: false,
    pricing: {
      prompt: "0.000003",
      completion: "0.000015"
    },
    contextLength: 200000,
    capabilities: ["reasoning", "coding", "analysis", "creative"],
    category: "reasoning",
    provider: "Anthropic"
  },
  {
    id: "google/gemini-2.5-flash",
    name: "Gemini 2.5 Flash",
    description: "Google's fast and efficient model with good reasoning",
    isFree: false,
    pricing: {
      prompt: "0.000000075",
      completion: "0.0000003"
    },
    contextLength: 1048576,
    capabilities: ["reasoning", "coding", "multimodal", "fast"],
    category: "fast",
    provider: "Google"
  },
  {
    id: "google/gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    description: "Google's most capable model with advanced reasoning",
    isFree: false,
    pricing: {
      prompt: "0.00000125",
      completion: "0.000005"
    },
    contextLength: 2097152,
    capabilities: ["reasoning", "coding", "analysis", "multimodal"],
    category: "reasoning",
    provider: "Google"
  },
  {
    id: "deepseek/deepseek-r1",
    name: "DeepSeek R1",
    description: "Full version of DeepSeek's reasoning model",
    isFree: false,
    pricing: {
      prompt: "0.000002",
      completion: "0.000008"
    },
    contextLength: 131072,
    capabilities: ["reasoning", "math", "coding", "analysis"],
    category: "reasoning",
    provider: "DeepSeek"
  }
]

// Helper functions
export function getFreeModels(): LLMProvider[] {
  return LLM_PROVIDERS.filter(model => model.isFree)
}

export function getPaidModels(): LLMProvider[] {
  return LLM_PROVIDERS.filter(model => !model.isFree)
}

export function getModelsByCategory(category: LLMProvider['category']): LLMProvider[] {
  return LLM_PROVIDERS.filter(model => model.category === category)
}

export function getModelById(id: string): LLMProvider | undefined {
  return LLM_PROVIDERS.find(model => model.id === id)
}

export function getRecommendedChessModels(): LLMProvider[] {
  // Models that are particularly good for chess reasoning
  return LLM_PROVIDERS.filter(model => 
    model.capabilities.includes('reasoning') && 
    (model.isFree || model.id.includes('gpt-4') || model.id.includes('claude') || model.id.includes('gemini'))
  )
}

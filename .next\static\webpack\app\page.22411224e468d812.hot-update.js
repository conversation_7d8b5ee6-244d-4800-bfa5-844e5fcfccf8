"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChessGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Crown,DollarSign,Loader2,Pause,Play,RotateCcw,Settings,Star,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var react_chessboard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-chessboard */ \"(app-pages-browser)/./node_modules/.pnpm/react-chessboard@5.1.0_reac_e00cae35ad1507d8573e9e34d9d8798b/node_modules/react-chessboard/dist/index.esm.js\");\n/* harmony import */ var chess_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! chess.js */ \"(app-pages-browser)/./node_modules/.pnpm/chess.js@1.4.0/node_modules/chess.js/dist/esm/chess.js\");\n/* harmony import */ var _lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/llm-providers */ \"(app-pages-browser)/./lib/llm-providers.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ChessGame() {\n    _s();\n    const [games, setGames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentGame, setCurrentGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [moves, setMoves] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingTables, setMissingTables] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [chess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new chess_js__WEBPACK_IMPORTED_MODULE_9__.Chess());\n    const [isCreatingGame, setIsCreatingGame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcessingMove, setIsProcessingMove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoPlay, setAutoPlay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentThinking, setCurrentThinking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showModelSelector, setShowModelSelector] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWhiteModel, setSelectedWhiteModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"google/gemini-2.5-flash\");\n    const [selectedBlackModel, setSelectedBlackModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"deepseek/deepseek-r1:free\");\n    const [modelFilter, setModelFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"recommended\");\n    // Load games on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChessGame.useEffect\": ()=>{\n            loadGames();\n        }\n    }[\"ChessGame.useEffect\"], []);\n    // Auto-play effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChessGame.useEffect\": ()=>{\n            if (autoPlay && currentGame && currentGame.status === \"in_progress\" && !isProcessingMove) {\n                const timer = setTimeout({\n                    \"ChessGame.useEffect.timer\": ()=>{\n                        triggerNextMove(currentGame.id);\n                    }\n                }[\"ChessGame.useEffect.timer\"], 2000) // 2 second delay between moves\n                ;\n                return ({\n                    \"ChessGame.useEffect\": ()=>clearTimeout(timer)\n                })[\"ChessGame.useEffect\"];\n            }\n        }\n    }[\"ChessGame.useEffect\"], [\n        autoPlay,\n        currentGame,\n        moves,\n        isProcessingMove\n    ]);\n    // Subscribe to real-time updates for current game\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChessGame.useEffect\": ()=>{\n            if (!currentGame) return;\n            const gameChannel = _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.channel(\"game-\".concat(currentGame.id)).on(\"postgres_changes\", {\n                event: \"*\",\n                schema: \"public\",\n                table: \"games\",\n                filter: \"id=eq.\".concat(currentGame.id)\n            }, {\n                \"ChessGame.useEffect.gameChannel\": (payload)=>{\n                    if (payload.eventType === \"UPDATE\") {\n                        setCurrentGame(payload.new);\n                        chess.load(payload.new.fen);\n                    }\n                }\n            }[\"ChessGame.useEffect.gameChannel\"]).on(\"postgres_changes\", {\n                event: \"INSERT\",\n                schema: \"public\",\n                table: \"moves\",\n                filter: \"game_id=eq.\".concat(currentGame.id)\n            }, {\n                \"ChessGame.useEffect.gameChannel\": (payload)=>{\n                    const newMove = payload.new;\n                    setMoves({\n                        \"ChessGame.useEffect.gameChannel\": (prev)=>[\n                                ...prev,\n                                newMove\n                            ]\n                    }[\"ChessGame.useEffect.gameChannel\"]);\n                    if (newMove.llm_thinking_process) {\n                        setCurrentThinking(newMove.llm_thinking_process);\n                    }\n                }\n            }[\"ChessGame.useEffect.gameChannel\"]).subscribe();\n            return ({\n                \"ChessGame.useEffect\": ()=>{\n                    _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.removeChannel(gameChannel);\n                }\n            })[\"ChessGame.useEffect\"];\n        }\n    }[\"ChessGame.useEffect\"], [\n        currentGame,\n        chess\n    ]);\n    const loadGames = async ()=>{\n        try {\n            // Check if Supabase is properly configured\n            if (false) {}\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"games\").select(\"*\").order(\"created_at\", {\n                ascending: false\n            }).limit(10);\n            if (error) {\n                if (error.code === \"42P01\") {\n                    console.warn(\"Table 'games' not found. Please run the DB migration.\");\n                    setMissingTables(true);\n                } else {\n                    console.error(\"Error loading games:\", error.message || error);\n                }\n                return;\n            }\n            setGames(data || []);\n            setMissingTables(false);\n        } catch (err) {\n            console.error(\"Failed to load games:\", err);\n            setMissingTables(true);\n        }\n    };\n    const createNewGame = async ()=>{\n        setIsCreatingGame(true);\n        try {\n            // Check if Supabase is properly configured\n            if (false) {}\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"games\").insert({\n                white_player_llm: selectedWhiteModel,\n                black_player_llm: selectedBlackModel\n            }).select().single();\n            if (error) {\n                console.error(\"Error creating game:\", error.message || error);\n                alert(\"Failed to create game: \".concat(error.message || 'Unknown error'));\n                setIsCreatingGame(false);\n                return;\n            }\n            if (!data) {\n                console.error(\"No data returned from game creation\");\n                alert(\"Failed to create game: No data returned\");\n                setIsCreatingGame(false);\n                return;\n            }\n            setCurrentGame(data);\n            chess.reset();\n            setMoves([]);\n            setCurrentThinking(\"\");\n            setIsCreatingGame(false);\n            setAutoPlay(true) // Auto-start the battle\n            ;\n            setShowModelSelector(false) // Close the model selector\n            ;\n            // Start the game by triggering the first move\n            await triggerNextMove(data.id);\n        } catch (err) {\n            console.error(\"Failed to create game:\", err);\n            alert(\"Failed to create game. Please check your configuration.\");\n            setIsCreatingGame(false);\n        }\n    };\n    const selectGame = async (game)=>{\n        setCurrentGame(game);\n        chess.load(game.fen);\n        setAutoPlay(false) // Stop auto-play when selecting different game\n        ;\n        // Load moves for this game\n        const { data: movesData, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"moves\").select(\"*\").eq(\"game_id\", game.id).order(\"move_number\", {\n            ascending: true\n        });\n        if (error) {\n            console.error(\"Error loading moves:\", error);\n            return;\n        }\n        setMoves(movesData || []);\n        // Get the latest thinking process\n        const latestMove = movesData === null || movesData === void 0 ? void 0 : movesData[movesData.length - 1];\n        if (latestMove === null || latestMove === void 0 ? void 0 : latestMove.llm_thinking_process) {\n            setCurrentThinking(latestMove.llm_thinking_process);\n        }\n    };\n    const triggerNextMove = async (gameId)=>{\n        if (isProcessingMove) return; // Prevent multiple simultaneous requests\n        setIsProcessingMove(true);\n        try {\n            const response = await fetch(\"/api/chess-orchestrator\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    gameId\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                console.error(\"Failed to trigger next move:\", result.error);\n                throw new Error(result.error || \"Failed to trigger next move\");\n            }\n            console.log(\"Move result:\", result);\n            // If game ended, stop auto-play\n            if (result.gameStatus && result.gameStatus !== \"in_progress\") {\n                setAutoPlay(false);\n            }\n        } catch (error) {\n            console.error(\"Error triggering next move:\", error);\n            setAutoPlay(false) // Stop auto-play on error\n            ;\n        } finally{\n            setIsProcessingMove(false);\n        }\n    };\n    const getFilteredModels = ()=>{\n        switch(modelFilter){\n            case \"free\":\n                return (0,_lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__.getFreeModels)();\n            case \"paid\":\n                return (0,_lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__.getPaidModels)();\n            case \"recommended\":\n                return (0,_lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__.getRecommendedChessModels)();\n            default:\n                return _lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__.LLM_PROVIDERS;\n        }\n    };\n    const getModelDisplayName = (modelId)=>{\n        const model = _lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__.LLM_PROVIDERS.find((m)=>m.id === modelId);\n        return model ? model.name : modelId;\n    };\n    const getModelIcon = (modelId)=>{\n        const model = _lib_llm_providers__WEBPACK_IMPORTED_MODULE_10__.LLM_PROVIDERS.find((m)=>m.id === modelId);\n        if (!model) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"w-3 h-3\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n            lineNumber: 253,\n            columnNumber: 24\n        }, this);\n        if (model.isFree) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"w-3 h-3 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 14\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"w-3 h-3 text-green-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 14\n            }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"in_progress\":\n                return \"default\";\n            case \"completed\":\n                return \"secondary\";\n            case \"resigned\":\n                return \"destructive\";\n            case \"draw\":\n                return \"outline\";\n            default:\n                return \"default\";\n        }\n    };\n    const toggleAutoPlay = ()=>{\n        if (!currentGame || currentGame.status !== \"in_progress\") return;\n        setAutoPlay(!autoPlay);\n        // If starting auto-play and it's not currently processing, trigger next move\n        if (!autoPlay && !isProcessingMove) {\n            triggerNextMove(currentGame.id);\n        }\n    };\n    const resetGame = async ()=>{\n        if (!currentGame) return;\n        setAutoPlay(false) // Stop auto-play during reset\n        ;\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"games\").update({\n            fen: \"rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1\",\n            status: \"in_progress\",\n            current_turn: \"white\",\n            winner: null,\n            white_illegal_attempts: 0,\n            black_illegal_attempts: 0\n        }).eq(\"id\", currentGame.id);\n        if (error) {\n            console.error(\"Error resetting game:\", error);\n            return;\n        }\n        // Delete all moves for this game\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"moves\").delete().eq(\"game_id\", currentGame.id);\n        chess.reset();\n        setMoves([]);\n        setCurrentThinking(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-amber-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-8 h-8 text-amber-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                \"AI Chess Battle Arena\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-4 text-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-100 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-blue-800\",\n                                            children: getModelDisplayName(selectedWhiteModel)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl\",\n                                    children: \"⚔️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-purple-100 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-5 h-5 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-purple-800\",\n                                            children: getModelDisplayName(selectedBlackModel)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                missingTables && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 rounded-lg border border-red-300 bg-red-50 p-4 text-red-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold\",\n                            children: \"⚠️ Setup Required\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 space-y-2 text-sm\",\n                            children:  false ? /*#__PURE__*/ 0 : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"2. Create database tables:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"ml-4 list-disc space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"Run the SQL in \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                        className: \"font-mono\",\n                                                        children: \"scripts/create-tables.sql\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 40\n                                                    }, this),\n                                                    \" in your Supabase SQL editor\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Refresh this page after creating the tables\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Battle History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                                                    open: showModelSelector,\n                                                    onOpenChange: setShowModelSelector,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                onClick: ()=>setShowModelSelector(true),\n                                                                disabled: isCreatingGame,\n                                                                size: \"sm\",\n                                                                className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600\",\n                                                                children: [\n                                                                    isCreatingGame ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-4 h-4 animate-spin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 43\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 90\n                                                                    }, this),\n                                                                    !isCreatingGame && \"New Battle\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                                                            className: \"sm:max-w-[600px]\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"w-5 h-5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Configure AI Battle\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid gap-4 py-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-center gap-4 mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: modelFilter === \"all\" ? \"default\" : \"outline\",\n                                                                                    onClick: ()=>setModelFilter(\"all\"),\n                                                                                    children: \"All Models\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 394,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: modelFilter === \"free\" ? \"default\" : \"outline\",\n                                                                                    onClick: ()=>setModelFilter(\"free\"),\n                                                                                    className: \"flex items-center gap-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-yellow-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 407,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Free\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 401,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: modelFilter === \"paid\" ? \"default\" : \"outline\",\n                                                                                    onClick: ()=>setModelFilter(\"paid\"),\n                                                                                    className: \"flex items-center gap-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-500\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 416,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        \"Premium\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 410,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: modelFilter === \"recommended\" ? \"default\" : \"outline\",\n                                                                                    onClick: ()=>setModelFilter(\"recommended\"),\n                                                                                    children: \"Recommended\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"grid grid-cols-2 gap-6\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-4 h-4 bg-white rounded-full border-2 border-gray-300\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 431,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                \"White Player\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 430,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                                            value: selectedWhiteModel,\n                                                                                            onValueChange: setSelectedWhiteModel,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                                        placeholder: \"Select White Player\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 436,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 435,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                                    className: \"max-h-[300px]\",\n                                                                                                    children: getFilteredModels().map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                                            value: model.id,\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex items-center gap-2\",\n                                                                                                                children: [\n                                                                                                                    model.isFree ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                                                        className: \"w-3 h-3 text-yellow-500\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                                        lineNumber: 443,\n                                                                                                                        columnNumber: 41\n                                                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                                        className: \"w-3 h-3 text-green-500\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                                        lineNumber: 445,\n                                                                                                                        columnNumber: 41\n                                                                                                                    }, this),\n                                                                                                                    model.name\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                                lineNumber: 441,\n                                                                                                                columnNumber: 37\n                                                                                                            }, this)\n                                                                                                        }, \"white-\".concat(model.id), false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                            lineNumber: 440,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 438,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 434,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"space-y-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"w-4 h-4 bg-black rounded-full border-2 border-gray-300\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 457,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                \"Black Player\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 456,\n                                                                                            columnNumber: 29\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                                            value: selectedBlackModel,\n                                                                                            onValueChange: setSelectedBlackModel,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                                        placeholder: \"Select Black Player\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                        lineNumber: 462,\n                                                                                                        columnNumber: 33\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 461,\n                                                                                                    columnNumber: 31\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                                    className: \"max-h-[300px]\",\n                                                                                                    children: getFilteredModels().map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                                            value: model.id,\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"flex items-center gap-2\",\n                                                                                                                children: [\n                                                                                                                    model.isFree ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                                                        className: \"w-3 h-3 text-yellow-500\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                                        lineNumber: 469,\n                                                                                                                        columnNumber: 41\n                                                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                                        className: \"w-3 h-3 text-green-500\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                                        lineNumber: 471,\n                                                                                                                        columnNumber: 41\n                                                                                                                    }, this),\n                                                                                                                    model.name\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                                lineNumber: 467,\n                                                                                                                columnNumber: 37\n                                                                                                            }, this)\n                                                                                                        }, \"black-\".concat(model.id), false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                            lineNumber: 466,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this))\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                                    lineNumber: 464,\n                                                                                                    columnNumber: 31\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 460,\n                                                                                            columnNumber: 29\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-end mt-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                onClick: createNewGame,\n                                                                                disabled: isCreatingGame,\n                                                                                children: [\n                                                                                    isCreatingGame ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                        className: \"w-4 h-4 animate-spin mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 484,\n                                                                                        columnNumber: 47\n                                                                                    }, this) : null,\n                                                                                    \"Start Battle\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-2 max-h-96 overflow-y-auto\",\n                                        children: games.map((game)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md \".concat((currentGame === null || currentGame === void 0 ? void 0 : currentGame.id) === game.id ? \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-300\" : \"hover:bg-gray-50\"),\n                                                onClick: ()=>selectGame(game),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: getStatusColor(game.status),\n                                                                children: game.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: new Date(game.created_at).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-3 h-3 bg-white border border-gray-400 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 510,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            getModelIcon(game.white_player_llm),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-medium\",\n                                                                                children: getModelDisplayName(game.white_player_llm)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-3 h-3 bg-gray-800 rounded-sm\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1\",\n                                                                        children: [\n                                                                            getModelIcon(game.black_player_llm),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-medium\",\n                                                                                children: getModelDisplayName(game.black_player_llm)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    game.winner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-xs font-medium text-green-600 flex items-center gap-1\",\n                                                        children: [\n                                                            \"\\uD83C\\uDFC6 Winner:\",\n                                                            \" \",\n                                                            getModelDisplayName(game.winner === \"white\" ? game.white_player_llm : game.black_player_llm)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, game.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: currentGame ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"p-6 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: currentGame.current_turn === \"white\" ? \"default\" : \"secondary\",\n                                                        className: \"px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                getModelIcon(currentGame.white_player_llm),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"White: \",\n                                                                        getModelDisplayName(currentGame.white_player_llm)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: currentGame.current_turn === \"black\" ? \"default\" : \"secondary\",\n                                                        className: \"px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                getModelIcon(currentGame.black_player_llm),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Black: \",\n                                                                        getModelDisplayName(currentGame.black_player_llm)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    currentGame.status === \"in_progress\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: toggleAutoPlay,\n                                                        variant: autoPlay ? \"destructive\" : \"default\",\n                                                        size: \"sm\",\n                                                        disabled: isProcessingMove,\n                                                        className: autoPlay ? \"bg-red-500 hover:bg-red-600\" : \"bg-green-500 hover:bg-green-600\",\n                                                        children: [\n                                                            isProcessingMove ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 27\n                                                            }, this) : autoPlay ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            autoPlay ? \"Stop Auto\" : \"Start Auto\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: ()=>triggerNextMove(currentGame.id),\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        disabled: currentGame.status !== \"in_progress\" || isProcessingMove,\n                                                        className: \"border-blue-300 hover:bg-blue-50\",\n                                                        children: [\n                                                            isProcessingMove ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-4 h-4 animate-spin mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            isProcessingMove ? \"Processing...\" : \"Next Move\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: resetGame,\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"border-red-300 hover:bg-red-50 bg-transparent\",\n                                                        disabled: isProcessingMove,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Reset\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full max-w-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chessboard__WEBPACK_IMPORTED_MODULE_8__.Chessboard, {\n                                                position: currentGame.fen,\n                                                arePiecesDraggable: false,\n                                                boardWidth: 400\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: getStatusColor(currentGame.status),\n                                                className: \"text-lg px-4 py-2\",\n                                                children: currentGame.status === \"in_progress\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        isProcessingMove && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 46\n                                                        }, this),\n                                                        getModelIcon(currentGame.current_turn === \"white\" ? currentGame.white_player_llm : currentGame.black_player_llm),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                getModelDisplayName(currentGame.current_turn === \"white\" ? currentGame.white_player_llm : currentGame.black_player_llm),\n                                                                isProcessingMove ? \" is thinking...\" : \"'s turn\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        autoPlay && !isProcessingMove && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500 ml-2\",\n                                                            children: \"\\uD83D\\uDD04 AUTO\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 59\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 23\n                                                }, this) : currentGame.status\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentGame.winner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 text-lg font-bold text-green-600 flex items-center justify-center gap-2\",\n                                                children: [\n                                                    \"\\uD83C\\uDFC6 Winner:\",\n                                                    getModelIcon(currentGame.winner === \"white\" ? currentGame.white_player_llm : currentGame.black_player_llm),\n                                                    getModelDisplayName(currentGame.winner === \"white\" ? currentGame.white_player_llm : currentGame.black_player_llm)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"p-6 shadow-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-16 h-16 mx-auto mb-4 opacity-50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"No Battle Selected\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Create a new battle or select an existing one to watch the AI showdown\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1 space-y-4\",\n                            children: currentGame && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Battle Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Current Turn:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        getModelIcon(currentGame.current_turn === \"white\" ? currentGame.white_player_llm : currentGame.black_player_llm),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: currentGame.current_turn === \"white\" ? \"default\" : \"secondary\",\n                                                                            children: currentGame.current_turn\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 671,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Total Moves:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 685,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: moves.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: getStatusColor(currentGame.status),\n                                                                    children: currentGame.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Auto Play:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    variant: autoPlay ? \"default\" : \"secondary\",\n                                                                    children: autoPlay ? \"ON\" : \"OFF\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Gemini Fails:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600 font-medium\",\n                                                                    children: currentGame.white_illegal_attempts\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"DeepSeek Fails:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600 font-medium\",\n                                                                    children: currentGame.black_illegal_attempts\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 17\n                                    }, this),\n                                    currentThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Crown_DollarSign_Loader2_Pause_Play_RotateCcw_Settings_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"AI Reasoning\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground whitespace-pre-wrap max-h-40 overflow-y-auto bg-gray-50 p-3 rounded\",\n                                                    children: currentThinking\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Move History\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 725,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1 max-h-60 overflow-y-auto\",\n                                                    children: moves.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"Battle hasn't started yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                        lineNumber: 731,\n                                                        columnNumber: 25\n                                                    }, this) : moves.map((move, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm flex justify-between items-center py-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-mono text-gray-500\",\n                                                                    children: [\n                                                                        Math.floor(index / 2) + 1,\n                                                                        \".\",\n                                                                        move.player_color === \"white\" ? \"\" : \"..\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-mono font-medium\",\n                                                                    children: move.move_san\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1\",\n                                                                    children: [\n                                                                        getModelIcon(move.player_color === \"white\" ? currentGame.white_player_llm : currentGame.black_player_llm),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                            variant: \"outline\",\n                                                                            className: \"text-xs\",\n                                                                            children: move.player_color\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, move.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n            lineNumber: 322,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chessllm\\\\app\\\\page.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n}\n_s(ChessGame, \"vjZmP28KlcXbhFDKmhWe8Zj3UCQ=\");\n_c = ChessGame;\nvar _c;\n$RefreshReg$(_c, \"ChessGame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/supabase.ts":
/*!*************************!*\
  !*** ./lib/supabase.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://gkhscuawgjvqmjxadmdg.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdraHNjdWF3Z2p2cW1qeGFkbWRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMzM0MjMsImV4cCI6MjA2NzcwOTQyM30.7URrs9NvA1SOWDFnRKL8Yf1_76cYuv6MMoMgMvJMh2w\" || 0;\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/supabase.ts\n"));

/***/ })

});
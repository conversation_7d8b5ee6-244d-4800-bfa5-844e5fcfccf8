import { google } from "@ai-sdk/google"
import { generateObject } from "ai"
import { z } from "zod"
import { NextResponse } from "next/server"

const GOOGLE_AI_KEY = process.env.GOOGLE_GENERATIVE_AI_API_KEY

const moveSchema = z.object({
  move: z.string().describe("The chess move in algebraic notation (e.g., e2e4)"),
  thinking: z.string().describe("Your thought process for choosing this move"),
  evaluation: z.number().describe("Position evaluation from -10 to +10 (positive favors white)"),
})

export async function POST(req: Request) {
  try {
    const { fen, moveHistory, difficulty = "intermediate" } = await req.json()

    if (!GOOGLE_AI_KEY) {
      return NextResponse.json(
        { error: "Server mis-configuration: GOOGLE_GENERATIVE_AI_API_KEY is not set." },
        { status: 500 },
      )
    }

    const difficultyPrompts = {
      beginner: "Play at a beginner level, make some suboptimal moves occasionally.",
      intermediate: "Play at an intermediate level with good tactical awareness.",
      advanced: "Play at an advanced level with deep strategic understanding.",
    }

    const result = await generateObject({
      model: google("gemini-2.0-flash-thinking-exp", {
        apiKey: GOOGLE_AI_KEY,
      }),
      schema: moveSchema,
      prompt: `You are playing chess as Black. 
      
Current position (FEN): ${fen}
Move history: ${moveHistory || "Game just started"}
Difficulty: ${difficulty}

${difficultyPrompts[difficulty as keyof typeof difficultyPrompts]}

Analyze the position carefully and choose your next move. Consider:
1. Immediate tactical threats
2. Piece development and activity  
3. King safety
4. Pawn structure
5. Long-term strategic goals

Think through your decision process step by step, then provide your move in algebraic notation (like e7e5 for pawn from e7 to e5).

IMPORTANT: 
- Only provide legal moves
- Use lowercase letters for files (a-h) and numbers for ranks (1-8)
- Format: [from_square][to_square] (e.g., "e7e5", "g8f6", "e8g8" for castling)
- Consider the current board position carefully`,
    })

    return NextResponse.json({
      move: result.object.move,
      thinking: result.object.thinking,
      evaluation: result.object.evaluation,
    })
  } catch (error) {
    console.error("Error generating chess move:", error)
    return NextResponse.json({ error: "Failed to generate move" }, { status: 500 })
  }
}

-- Create games table
CREATE TABLE IF NOT EXISTS games (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  white_player_llm TEXT NOT NULL DEFAULT 'gemini-2.0-flash-thinking-exp',
  black_player_llm TEXT NOT NULL DEFAULT 'gpt-4o',
  fen TEXT NOT NULL DEFAULT 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
  status TEXT NOT NULL DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'resigned', 'draw')),
  winner TEXT,
  current_turn TEXT NOT NULL DEFAULT 'white' CHECK (current_turn IN ('white', 'black')),
  white_illegal_attempts INTEGER DEFAULT 0,
  black_illegal_attempts INTEGER DEFAULT 0
);

-- Create moves table
CREATE TABLE IF NOT EXISTS moves (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  game_id UUID REFERENCES games(id) ON DELETE CASCADE,
  move_number INTEGER NOT NULL,
  player_color TEXT NOT NULL CHECK (player_color IN ('white', 'black')),
  move_pgn TEXT NOT NULL,
  move_san TEXT NOT NULL,
  llm_thinking_process TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE moves ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed)
CREATE POLICY "Allow public read access on games" ON games FOR SELECT USING (true);
CREATE POLICY "Allow public insert access on games" ON games FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow public update access on games" ON games FOR UPDATE USING (true);

CREATE POLICY "Allow public read access on moves" ON moves FOR SELECT USING (true);
CREATE POLICY "Allow public insert access on moves" ON moves FOR INSERT WITH CHECK (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_games_status ON games(status);
CREATE INDEX IF NOT EXISTS idx_moves_game_id ON moves(game_id);
CREATE INDEX IF NOT EXISTS idx_moves_move_number ON moves(game_id, move_number);

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+google@1.2.22_zod@3.25.76";
exports.ids = ["vendor-chunks/@ai-sdk+google@1.2.22_zod@3.25.76"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+google@1.2.22_zod@3.25.76/node_modules/@ai-sdk/google/dist/index.mjs":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+google@1.2.22_zod@3.25.76/node_modules/@ai-sdk/google/dist/index.mjs ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGoogleGenerativeAI: () => (/* binding */ createGoogleGenerativeAI),\n/* harmony export */   google: () => (/* binding */ google)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.25.76/node_modules/zod/v3/types.js\");\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.1.3/node_modules/@ai-sdk/provider/dist/index.mjs\");\n// src/google-provider.ts\n\n\n// src/google-generative-ai-language-model.ts\n\n\n\n// src/convert-json-schema-to-openapi-schema.ts\nfunction convertJSONSchemaToOpenAPISchema(jsonSchema) {\n  if (isEmptyObjectSchema(jsonSchema)) {\n    return void 0;\n  }\n  if (typeof jsonSchema === \"boolean\") {\n    return { type: \"boolean\", properties: {} };\n  }\n  const {\n    type,\n    description,\n    required,\n    properties,\n    items,\n    allOf,\n    anyOf,\n    oneOf,\n    format,\n    const: constValue,\n    minLength,\n    enum: enumValues\n  } = jsonSchema;\n  const result = {};\n  if (description)\n    result.description = description;\n  if (required)\n    result.required = required;\n  if (format)\n    result.format = format;\n  if (constValue !== void 0) {\n    result.enum = [constValue];\n  }\n  if (type) {\n    if (Array.isArray(type)) {\n      if (type.includes(\"null\")) {\n        result.type = type.filter((t) => t !== \"null\")[0];\n        result.nullable = true;\n      } else {\n        result.type = type;\n      }\n    } else if (type === \"null\") {\n      result.type = \"null\";\n    } else {\n      result.type = type;\n    }\n  }\n  if (enumValues !== void 0) {\n    result.enum = enumValues;\n  }\n  if (properties != null) {\n    result.properties = Object.entries(properties).reduce(\n      (acc, [key, value]) => {\n        acc[key] = convertJSONSchemaToOpenAPISchema(value);\n        return acc;\n      },\n      {}\n    );\n  }\n  if (items) {\n    result.items = Array.isArray(items) ? items.map(convertJSONSchemaToOpenAPISchema) : convertJSONSchemaToOpenAPISchema(items);\n  }\n  if (allOf) {\n    result.allOf = allOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (anyOf) {\n    if (anyOf.some(\n      (schema) => typeof schema === \"object\" && (schema == null ? void 0 : schema.type) === \"null\"\n    )) {\n      const nonNullSchemas = anyOf.filter(\n        (schema) => !(typeof schema === \"object\" && (schema == null ? void 0 : schema.type) === \"null\")\n      );\n      if (nonNullSchemas.length === 1) {\n        const converted = convertJSONSchemaToOpenAPISchema(nonNullSchemas[0]);\n        if (typeof converted === \"object\") {\n          result.nullable = true;\n          Object.assign(result, converted);\n        }\n      } else {\n        result.anyOf = nonNullSchemas.map(convertJSONSchemaToOpenAPISchema);\n        result.nullable = true;\n      }\n    } else {\n      result.anyOf = anyOf.map(convertJSONSchemaToOpenAPISchema);\n    }\n  }\n  if (oneOf) {\n    result.oneOf = oneOf.map(convertJSONSchemaToOpenAPISchema);\n  }\n  if (minLength !== void 0) {\n    result.minLength = minLength;\n  }\n  return result;\n}\nfunction isEmptyObjectSchema(jsonSchema) {\n  return jsonSchema != null && typeof jsonSchema === \"object\" && jsonSchema.type === \"object\" && (jsonSchema.properties == null || Object.keys(jsonSchema.properties).length === 0) && !jsonSchema.additionalProperties;\n}\n\n// src/convert-to-google-generative-ai-messages.ts\n\n\nfunction convertToGoogleGenerativeAIMessages(prompt) {\n  var _a, _b;\n  const systemInstructionParts = [];\n  const contents = [];\n  let systemMessagesAllowed = true;\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        if (!systemMessagesAllowed) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n            functionality: \"system messages are only supported at the beginning of the conversation\"\n          });\n        }\n        systemInstructionParts.push({ text: content });\n        break;\n      }\n      case \"user\": {\n        systemMessagesAllowed = false;\n        const parts = [];\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              parts.push({ text: part.text });\n              break;\n            }\n            case \"image\": {\n              parts.push(\n                part.image instanceof URL ? {\n                  fileData: {\n                    mimeType: (_a = part.mimeType) != null ? _a : \"image/jpeg\",\n                    fileUri: part.image.toString()\n                  }\n                } : {\n                  inlineData: {\n                    mimeType: (_b = part.mimeType) != null ? _b : \"image/jpeg\",\n                    data: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.convertUint8ArrayToBase64)(part.image)\n                  }\n                }\n              );\n              break;\n            }\n            case \"file\": {\n              parts.push(\n                part.data instanceof URL ? {\n                  fileData: {\n                    mimeType: part.mimeType,\n                    fileUri: part.data.toString()\n                  }\n                } : {\n                  inlineData: {\n                    mimeType: part.mimeType,\n                    data: part.data\n                  }\n                }\n              );\n              break;\n            }\n          }\n        }\n        contents.push({ role: \"user\", parts });\n        break;\n      }\n      case \"assistant\": {\n        systemMessagesAllowed = false;\n        contents.push({\n          role: \"model\",\n          parts: content.map((part) => {\n            switch (part.type) {\n              case \"text\": {\n                return part.text.length === 0 ? void 0 : { text: part.text };\n              }\n              case \"file\": {\n                if (part.mimeType !== \"image/png\") {\n                  throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n                    functionality: \"Only PNG images are supported in assistant messages\"\n                  });\n                }\n                if (part.data instanceof URL) {\n                  throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n                    functionality: \"File data URLs in assistant messages are not supported\"\n                  });\n                }\n                return {\n                  inlineData: {\n                    mimeType: part.mimeType,\n                    data: part.data\n                  }\n                };\n              }\n              case \"tool-call\": {\n                return {\n                  functionCall: {\n                    name: part.toolName,\n                    args: part.args\n                  }\n                };\n              }\n            }\n          }).filter((part) => part !== void 0)\n        });\n        break;\n      }\n      case \"tool\": {\n        systemMessagesAllowed = false;\n        contents.push({\n          role: \"user\",\n          parts: content.map((part) => ({\n            functionResponse: {\n              name: part.toolName,\n              response: {\n                name: part.toolName,\n                content: part.result\n              }\n            }\n          }))\n        });\n        break;\n      }\n    }\n  }\n  return {\n    systemInstruction: systemInstructionParts.length > 0 ? { parts: systemInstructionParts } : void 0,\n    contents\n  };\n}\n\n// src/get-model-path.ts\nfunction getModelPath(modelId) {\n  return modelId.includes(\"/\") ? modelId : `models/${modelId}`;\n}\n\n// src/google-error.ts\n\n\nvar googleErrorDataSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  error: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    code: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullable(),\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n    status: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n  })\n});\nvar googleFailedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonErrorResponseHandler)({\n  errorSchema: googleErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/google-prepare-tools.ts\n\nfunction prepareTools(mode, useSearchGrounding, dynamicRetrievalConfig, modelId) {\n  var _a, _b;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  const isGemini2 = modelId.includes(\"gemini-2\");\n  const supportsDynamicRetrieval = modelId.includes(\"gemini-1.5-flash\") && !modelId.includes(\"-8b\");\n  if (useSearchGrounding) {\n    return {\n      tools: isGemini2 ? { googleSearch: {} } : {\n        googleSearchRetrieval: !supportsDynamicRetrieval || !dynamicRetrievalConfig ? {} : { dynamicRetrievalConfig }\n      },\n      toolConfig: void 0,\n      toolWarnings\n    };\n  }\n  if (tools == null) {\n    return { tools: void 0, toolConfig: void 0, toolWarnings };\n  }\n  const functionDeclarations = [];\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      functionDeclarations.push({\n        name: tool.name,\n        description: (_b = tool.description) != null ? _b : \"\",\n        parameters: convertJSONSchemaToOpenAPISchema(tool.parameters)\n      });\n    }\n  }\n  const toolChoice = mode.toolChoice;\n  if (toolChoice == null) {\n    return {\n      tools: { functionDeclarations },\n      toolConfig: void 0,\n      toolWarnings\n    };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n      return {\n        tools: { functionDeclarations },\n        toolConfig: { functionCallingConfig: { mode: \"AUTO\" } },\n        toolWarnings\n      };\n    case \"none\":\n      return {\n        tools: { functionDeclarations },\n        toolConfig: { functionCallingConfig: { mode: \"NONE\" } },\n        toolWarnings\n      };\n    case \"required\":\n      return {\n        tools: { functionDeclarations },\n        toolConfig: { functionCallingConfig: { mode: \"ANY\" } },\n        toolWarnings\n      };\n    case \"tool\":\n      return {\n        tools: { functionDeclarations },\n        toolConfig: {\n          functionCallingConfig: {\n            mode: \"ANY\",\n            allowedFunctionNames: [toolChoice.toolName]\n          }\n        },\n        toolWarnings\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/map-google-generative-ai-finish-reason.ts\nfunction mapGoogleGenerativeAIFinishReason({\n  finishReason,\n  hasToolCalls\n}) {\n  switch (finishReason) {\n    case \"STOP\":\n      return hasToolCalls ? \"tool-calls\" : \"stop\";\n    case \"MAX_TOKENS\":\n      return \"length\";\n    case \"IMAGE_SAFETY\":\n    case \"RECITATION\":\n    case \"SAFETY\":\n    case \"BLOCKLIST\":\n    case \"PROHIBITED_CONTENT\":\n    case \"SPII\":\n      return \"content-filter\";\n    case \"FINISH_REASON_UNSPECIFIED\":\n    case \"OTHER\":\n      return \"other\";\n    case \"MALFORMED_FUNCTION_CALL\":\n      return \"error\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/google-generative-ai-language-model.ts\nvar GoogleGenerativeAILanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = \"json\";\n    this.supportsImageUrls = false;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get supportsStructuredOutputs() {\n    var _a;\n    return (_a = this.settings.structuredOutputs) != null ? _a : true;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  async getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata\n  }) {\n    var _a, _b, _c;\n    const type = mode.type;\n    const warnings = [];\n    const googleOptions = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.parseProviderOptions)({\n      provider: \"google\",\n      providerOptions: providerMetadata,\n      schema: googleGenerativeAIProviderOptionsSchema\n    });\n    if (((_a = googleOptions == null ? void 0 : googleOptions.thinkingConfig) == null ? void 0 : _a.includeThoughts) === true && !this.config.provider.startsWith(\"google.vertex.\")) {\n      warnings.push({\n        type: \"other\",\n        message: `The 'includeThoughts' option is only supported with the Google Vertex provider and might not be supported or could behave unexpectedly with the current Google provider (${this.config.provider}).`\n      });\n    }\n    const generationConfig = {\n      // standardized settings:\n      maxOutputTokens: maxTokens,\n      temperature,\n      topK,\n      topP,\n      frequencyPenalty,\n      presencePenalty,\n      stopSequences,\n      seed,\n      // response format:\n      responseMimeType: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? \"application/json\" : void 0,\n      responseSchema: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" && responseFormat.schema != null && // Google GenAI does not support all OpenAPI Schema features,\n      // so this is needed as an escape hatch:\n      this.supportsStructuredOutputs ? convertJSONSchemaToOpenAPISchema(responseFormat.schema) : void 0,\n      ...this.settings.audioTimestamp && {\n        audioTimestamp: this.settings.audioTimestamp\n      },\n      // provider options:\n      responseModalities: googleOptions == null ? void 0 : googleOptions.responseModalities,\n      thinkingConfig: googleOptions == null ? void 0 : googleOptions.thinkingConfig\n    };\n    const { contents, systemInstruction } = convertToGoogleGenerativeAIMessages(prompt);\n    switch (type) {\n      case \"regular\": {\n        const { tools, toolConfig, toolWarnings } = prepareTools(\n          mode,\n          (_b = this.settings.useSearchGrounding) != null ? _b : false,\n          this.settings.dynamicRetrievalConfig,\n          this.modelId\n        );\n        return {\n          args: {\n            generationConfig,\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            tools,\n            toolConfig,\n            cachedContent: this.settings.cachedContent\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            generationConfig: {\n              ...generationConfig,\n              responseMimeType: \"application/json\",\n              responseSchema: mode.schema != null && // Google GenAI does not support all OpenAPI Schema features,\n              // so this is needed as an escape hatch:\n              this.supportsStructuredOutputs ? convertJSONSchemaToOpenAPISchema(mode.schema) : void 0\n            },\n            contents,\n            systemInstruction,\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: {\n            generationConfig,\n            contents,\n            systemInstruction,\n            tools: {\n              functionDeclarations: [\n                {\n                  name: mode.tool.name,\n                  description: (_c = mode.tool.description) != null ? _c : \"\",\n                  parameters: convertJSONSchemaToOpenAPISchema(\n                    mode.tool.parameters\n                  )\n                }\n              ]\n            },\n            toolConfig: { functionCallingConfig: { mode: \"ANY\" } },\n            safetySettings: this.settings.safetySettings,\n            cachedContent: this.settings.cachedContent\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  supportsUrl(url) {\n    return this.config.isSupportedUrl(url);\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e;\n    const { args, warnings } = await this.getArgs(options);\n    const body = JSON.stringify(args);\n    const mergedHeaders = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(\n      await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.resolve)(this.config.headers),\n      options.headers\n    );\n    const {\n      responseHeaders,\n      value: response,\n      rawValue: rawResponse\n    } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId\n      )}:generateContent`,\n      headers: mergedHeaders,\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonResponseHandler)(responseSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { contents: rawPrompt, ...rawSettings } = args;\n    const candidate = response.candidates[0];\n    const parts = candidate.content == null || typeof candidate.content !== \"object\" || !(\"parts\" in candidate.content) ? [] : candidate.content.parts;\n    const toolCalls = getToolCallsFromParts({\n      parts,\n      // Use candidateParts\n      generateId: this.config.generateId\n    });\n    const usageMetadata = response.usageMetadata;\n    return {\n      text: getTextFromParts(parts),\n      reasoning: getReasoningDetailsFromParts(parts),\n      files: (_a = getInlineDataParts(parts)) == null ? void 0 : _a.map((part) => ({\n        data: part.inlineData.data,\n        mimeType: part.inlineData.mimeType\n      })),\n      toolCalls,\n      finishReason: mapGoogleGenerativeAIFinishReason({\n        finishReason: candidate.finishReason,\n        hasToolCalls: toolCalls != null && toolCalls.length > 0\n      }),\n      usage: {\n        promptTokens: (_b = usageMetadata == null ? void 0 : usageMetadata.promptTokenCount) != null ? _b : NaN,\n        completionTokens: (_c = usageMetadata == null ? void 0 : usageMetadata.candidatesTokenCount) != null ? _c : NaN\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders, body: rawResponse },\n      warnings,\n      providerMetadata: {\n        google: {\n          groundingMetadata: (_d = candidate.groundingMetadata) != null ? _d : null,\n          safetyRatings: (_e = candidate.safetyRatings) != null ? _e : null\n        }\n      },\n      sources: extractSources({\n        groundingMetadata: candidate.groundingMetadata,\n        generateId: this.config.generateId\n      }),\n      request: { body }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = await this.getArgs(options);\n    const body = JSON.stringify(args);\n    const headers = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(\n      await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.resolve)(this.config.headers),\n      options.headers\n    );\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: `${this.config.baseURL}/${getModelPath(\n        this.modelId\n      )}:streamGenerateContent?alt=sse`,\n      headers,\n      body: args,\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createEventSourceResponseHandler)(chunkSchema),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { contents: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let providerMetadata = void 0;\n    const generateId2 = this.config.generateId;\n    let hasToolCalls = false;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a, _b, _c, _d, _e, _f;\n            if (!chunk.success) {\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            const usageMetadata = value.usageMetadata;\n            if (usageMetadata != null) {\n              usage = {\n                promptTokens: (_a = usageMetadata.promptTokenCount) != null ? _a : NaN,\n                completionTokens: (_b = usageMetadata.candidatesTokenCount) != null ? _b : NaN\n              };\n            }\n            const candidate = (_c = value.candidates) == null ? void 0 : _c[0];\n            if (candidate == null) {\n              return;\n            }\n            const content = candidate.content;\n            if (content != null) {\n              const deltaText = getTextFromParts(content.parts);\n              if (deltaText != null) {\n                controller.enqueue({\n                  type: \"text-delta\",\n                  textDelta: deltaText\n                });\n              }\n              const reasoningDeltaText = getReasoningDetailsFromParts(\n                content.parts\n              );\n              if (reasoningDeltaText != null) {\n                for (const part of reasoningDeltaText) {\n                  controller.enqueue({\n                    type: \"reasoning\",\n                    textDelta: part.text\n                  });\n                }\n              }\n              const inlineDataParts = getInlineDataParts(content.parts);\n              if (inlineDataParts != null) {\n                for (const part of inlineDataParts) {\n                  controller.enqueue({\n                    type: \"file\",\n                    mimeType: part.inlineData.mimeType,\n                    data: part.inlineData.data\n                  });\n                }\n              }\n              const toolCallDeltas = getToolCallsFromParts({\n                parts: content.parts,\n                generateId: generateId2\n              });\n              if (toolCallDeltas != null) {\n                for (const toolCall of toolCallDeltas) {\n                  controller.enqueue({\n                    type: \"tool-call-delta\",\n                    toolCallType: \"function\",\n                    toolCallId: toolCall.toolCallId,\n                    toolName: toolCall.toolName,\n                    argsTextDelta: toolCall.args\n                  });\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: toolCall.toolCallId,\n                    toolName: toolCall.toolName,\n                    args: toolCall.args\n                  });\n                  hasToolCalls = true;\n                }\n              }\n            }\n            if (candidate.finishReason != null) {\n              finishReason = mapGoogleGenerativeAIFinishReason({\n                finishReason: candidate.finishReason,\n                hasToolCalls\n              });\n              const sources = (_d = extractSources({\n                groundingMetadata: candidate.groundingMetadata,\n                generateId: generateId2\n              })) != null ? _d : [];\n              for (const source of sources) {\n                controller.enqueue({ type: \"source\", source });\n              }\n              providerMetadata = {\n                google: {\n                  groundingMetadata: (_e = candidate.groundingMetadata) != null ? _e : null,\n                  safetyRatings: (_f = candidate.safetyRatings) != null ? _f : null\n                }\n              };\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              usage,\n              providerMetadata\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body }\n    };\n  }\n};\nfunction getToolCallsFromParts({\n  parts,\n  generateId: generateId2\n}) {\n  const functionCallParts = parts == null ? void 0 : parts.filter(\n    (part) => \"functionCall\" in part\n  );\n  return functionCallParts == null || functionCallParts.length === 0 ? void 0 : functionCallParts.map((part) => ({\n    toolCallType: \"function\",\n    toolCallId: generateId2(),\n    toolName: part.functionCall.name,\n    args: JSON.stringify(part.functionCall.args)\n  }));\n}\nfunction getTextFromParts(parts) {\n  const textParts = parts == null ? void 0 : parts.filter(\n    (part) => \"text\" in part && part.thought !== true\n  );\n  return textParts == null || textParts.length === 0 ? void 0 : textParts.map((part) => part.text).join(\"\");\n}\nfunction getReasoningDetailsFromParts(parts) {\n  const reasoningParts = parts == null ? void 0 : parts.filter(\n    (part) => \"text\" in part && part.thought === true && part.text != null\n  );\n  return reasoningParts == null || reasoningParts.length === 0 ? void 0 : reasoningParts.map((part) => ({ type: \"text\", text: part.text }));\n}\nfunction getInlineDataParts(parts) {\n  return parts == null ? void 0 : parts.filter(\n    (part) => \"inlineData\" in part\n  );\n}\nfunction extractSources({\n  groundingMetadata,\n  generateId: generateId2\n}) {\n  var _a;\n  return (_a = groundingMetadata == null ? void 0 : groundingMetadata.groundingChunks) == null ? void 0 : _a.filter(\n    (chunk) => chunk.web != null\n  ).map((chunk) => ({\n    sourceType: \"url\",\n    id: generateId2(),\n    url: chunk.web.uri,\n    title: chunk.web.title\n  }));\n}\nvar contentSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  parts: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.union([\n      // note: order matters since text can be fully empty\n      zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        functionCall: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          name: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n          args: zod__WEBPACK_IMPORTED_MODULE_2__.unknown()\n        })\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        inlineData: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n          mimeType: zod__WEBPACK_IMPORTED_MODULE_2__.string(),\n          data: zod__WEBPACK_IMPORTED_MODULE_2__.string()\n        })\n      }),\n      zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n        thought: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().nullish()\n      })\n    ])\n  ).nullish()\n});\nvar groundingChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  web: zod__WEBPACK_IMPORTED_MODULE_2__.object({ uri: zod__WEBPACK_IMPORTED_MODULE_2__.string(), title: zod__WEBPACK_IMPORTED_MODULE_2__.string() }).nullish(),\n  retrievedContext: zod__WEBPACK_IMPORTED_MODULE_2__.object({ uri: zod__WEBPACK_IMPORTED_MODULE_2__.string(), title: zod__WEBPACK_IMPORTED_MODULE_2__.string() }).nullish()\n});\nvar groundingMetadataSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  webSearchQueries: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()).nullish(),\n  retrievalQueries: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.string()).nullish(),\n  searchEntryPoint: zod__WEBPACK_IMPORTED_MODULE_2__.object({ renderedContent: zod__WEBPACK_IMPORTED_MODULE_2__.string() }).nullish(),\n  groundingChunks: zod__WEBPACK_IMPORTED_MODULE_2__.array(groundingChunkSchema).nullish(),\n  groundingSupports: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      segment: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n        startIndex: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n        endIndex: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish()\n      }),\n      segment_text: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n      groundingChunkIndices: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()).nullish(),\n      supportChunkIndices: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()).nullish(),\n      confidenceScores: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()).nullish(),\n      confidenceScore: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()).nullish()\n    })\n  ).nullish(),\n  retrievalMetadata: zod__WEBPACK_IMPORTED_MODULE_2__.union([\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      webDynamicRetrievalScore: zod__WEBPACK_IMPORTED_MODULE_2__.number()\n    }),\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({})\n  ]).nullish()\n});\nvar safetyRatingSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  category: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  probability: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  probabilityScore: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  severity: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n  severityScore: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n  blocked: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().nullish()\n});\nvar responseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  candidates: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      content: contentSchema.nullish().or(zod__WEBPACK_IMPORTED_MODULE_2__.object({}).strict()),\n      finishReason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n      safetyRatings: zod__WEBPACK_IMPORTED_MODULE_2__.array(safetyRatingSchema).nullish(),\n      groundingMetadata: groundingMetadataSchema.nullish()\n    })\n  ),\n  usageMetadata: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    promptTokenCount: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    candidatesTokenCount: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    totalTokenCount: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish()\n  }).nullish()\n});\nvar chunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  candidates: zod__WEBPACK_IMPORTED_MODULE_2__.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.object({\n      content: contentSchema.nullish(),\n      finishReason: zod__WEBPACK_IMPORTED_MODULE_2__.string().nullish(),\n      safetyRatings: zod__WEBPACK_IMPORTED_MODULE_2__.array(safetyRatingSchema).nullish(),\n      groundingMetadata: groundingMetadataSchema.nullish()\n    })\n  ).nullish(),\n  usageMetadata: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    promptTokenCount: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    candidatesTokenCount: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    totalTokenCount: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish()\n  }).nullish()\n});\nvar googleGenerativeAIProviderOptionsSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  responseModalities: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__[\"enum\"]([\"TEXT\", \"IMAGE\"])).nullish(),\n  thinkingConfig: zod__WEBPACK_IMPORTED_MODULE_2__.object({\n    thinkingBudget: zod__WEBPACK_IMPORTED_MODULE_2__.number().nullish(),\n    includeThoughts: zod__WEBPACK_IMPORTED_MODULE_2__.boolean().nullish()\n  }).nullish()\n});\n\n// src/google-generative-ai-embedding-model.ts\n\n\n\nvar GoogleGenerativeAIEmbeddingModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get maxEmbeddingsPerCall() {\n    return 2048;\n  }\n  get supportsParallelCalls() {\n    return true;\n  }\n  async doEmbed({\n    values,\n    headers,\n    abortSignal\n  }) {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_0__.TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values\n      });\n    }\n    const mergedHeaders = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.combineHeaders)(\n      await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.resolve)(this.config.headers),\n      headers\n    );\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.postJsonToApi)({\n      url: `${this.config.baseURL}/models/${this.modelId}:batchEmbedContents`,\n      headers: mergedHeaders,\n      body: {\n        requests: values.map((value) => ({\n          model: `models/${this.modelId}`,\n          content: { role: \"user\", parts: [{ text: value }] },\n          outputDimensionality: this.settings.outputDimensionality,\n          taskType: this.settings.taskType\n        }))\n      },\n      failedResponseHandler: googleFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.createJsonResponseHandler)(\n        googleGenerativeAITextEmbeddingResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      embeddings: response.embeddings.map((item) => item.values),\n      usage: void 0,\n      rawResponse: { headers: responseHeaders }\n    };\n  }\n};\nvar googleGenerativeAITextEmbeddingResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.object({\n  embeddings: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.object({ values: zod__WEBPACK_IMPORTED_MODULE_2__.array(zod__WEBPACK_IMPORTED_MODULE_2__.number()) }))\n});\n\n// src/google-supported-file-url.ts\nfunction isSupportedFileUrl(url) {\n  return url.toString().startsWith(\"https://generativelanguage.googleapis.com/v1beta/files/\");\n}\n\n// src/google-provider.ts\nfunction createGoogleGenerativeAI(options = {}) {\n  var _a;\n  const baseURL = (_a = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.withoutTrailingSlash)(options.baseURL)) != null ? _a : \"https://generativelanguage.googleapis.com/v1beta\";\n  const getHeaders = () => ({\n    \"x-goog-api-key\": (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"GOOGLE_GENERATIVE_AI_API_KEY\",\n      description: \"Google Generative AI\"\n    }),\n    ...options.headers\n  });\n  const createChatModel = (modelId, settings = {}) => {\n    var _a2;\n    return new GoogleGenerativeAILanguageModel(modelId, settings, {\n      provider: \"google.generative-ai\",\n      baseURL,\n      headers: getHeaders,\n      generateId: (_a2 = options.generateId) != null ? _a2 : _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n      isSupportedUrl: isSupportedFileUrl,\n      fetch: options.fetch\n    });\n  };\n  const createEmbeddingModel = (modelId, settings = {}) => new GoogleGenerativeAIEmbeddingModel(modelId, settings, {\n    provider: \"google.generative-ai\",\n    baseURL,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const provider = function(modelId, settings) {\n    if (new.target) {\n      throw new Error(\n        \"The Google Generative AI model function cannot be called with the new keyword.\"\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  provider.languageModel = createChatModel;\n  provider.chat = createChatModel;\n  provider.generativeAI = createChatModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  return provider;\n}\nvar google = createGoogleGenerativeAI();\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+google@1.2.22_zod@3.25.76/node_modules/@ai-sdk/google/dist/index.mjs\n");

/***/ })

};
;
{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/runtime/api.ts"], "sourcesContent": ["/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\n// css base code, injected by the css-loader\n// eslint-disable-next-line func-names\nmodule.exports = function (useSourceMap: any) {\n  var list: any[] = [] // return the list of modules as css string\n\n  list.toString = function toString() {\n    return this.map(function (item) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      var content = cssWithMappingToString(item, useSourceMap)\n\n      if (item[2]) {\n        return '@media '.concat(item[2], ' {').concat(content, '}')\n      }\n\n      return content\n    }).join('')\n  } // import a list of modules into the list\n  // eslint-disable-next-line func-names\n\n  // @ts-expect-error TODO: fix type\n  list.i = function (modules: any, mediaQuery: any, dedupe: any) {\n    if (typeof modules === 'string') {\n      // eslint-disable-next-line no-param-reassign\n      modules = [[null, modules, '']]\n    }\n\n    var alreadyImportedModules: any = {}\n\n    if (dedupe) {\n      for (var i = 0; i < this.length; i++) {\n        // eslint-disable-next-line prefer-destructuring\n        var id = this[i][0]\n\n        if (id != null) {\n          alreadyImportedModules[id] = true\n        }\n      }\n    }\n\n    for (var _i = 0; _i < modules.length; _i++) {\n      var item: any = [].concat(modules[_i])\n\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        // eslint-disable-next-line no-continue\n        continue\n      }\n\n      if (mediaQuery) {\n        if (!item[2]) {\n          item[2] = mediaQuery\n        } else {\n          item[2] = ''.concat(mediaQuery, ' and ').concat(item[2])\n        }\n      }\n\n      list.push(item)\n    }\n  }\n\n  return list\n}\n\nfunction cssWithMappingToString(item: any, useSourceMap: any) {\n  var content = item[1] || '' // eslint-disable-next-line prefer-destructuring\n\n  var cssMapping = item[3]\n\n  if (!cssMapping) {\n    return content\n  }\n\n  if (useSourceMap && typeof btoa === 'function') {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    var sourceMapping = toComment(cssMapping)\n    var sourceURLs = cssMapping.sources.map(function (source: string) {\n      return '/*# sourceURL='\n        .concat(cssMapping.sourceRoot || '')\n        .concat(source, ' */')\n    })\n    return [content].concat(sourceURLs).concat([sourceMapping]).join('\\n')\n  }\n\n  return [content].join('\\n')\n} // Adapted from convert-source-map (MIT)\n\nfunction toComment(sourceMap: any) {\n  // eslint-disable-next-line no-undef\n  var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))))\n  var data =\n    'sourceMappingURL=data:application/json;charset=utf-8;base64,'.concat(\n      base64\n    )\n  return '/*# '.concat(data, ' */')\n}\n"], "names": ["module", "exports", "useSourceMap", "list", "toString", "map", "item", "content", "cssWithMappingToString", "concat", "join", "i", "modules", "mediaQuery", "dedupe", "alreadyImportedModules", "length", "id", "_i", "push", "cssMapping", "btoa", "sourceMapping", "toComment", "sourceURLs", "sources", "source", "sourceRoot", "sourceMap", "base64", "unescape", "encodeURIComponent", "JSON", "stringify", "data"], "mappings": "AAAA;;;AAGA,GACA,4CAA4C;AAC5C,sCAAsC;;AACtCA,OAAOC,OAAO,GAAG,SAAUC,YAAiB;IAC1C,IAAIC,OAAc,EAAE,CAAC,2CAA2C;;IAEhEA,KAAKC,QAAQ,GAAG,SAASA;QACvB,OAAO,IAAI,CAACC,GAAG,CAAC,SAAUC,IAAI;YAC5B,mEAAmE;YACnE,IAAIC,UAAUC,uBAAuBF,MAAMJ;YAE3C,IAAII,IAAI,CAAC,EAAE,EAAE;gBACX,OAAO,UAAUG,MAAM,CAACH,IAAI,CAAC,EAAE,EAAE,MAAMG,MAAM,CAACF,SAAS;YACzD;YAEA,OAAOA;QACT,GAAGG,IAAI,CAAC;IACV,EAAE,yCAAyC;;IAC3C,sCAAsC;IAEtC,kCAAkC;IAClCP,KAAKQ,CAAC,GAAG,SAAUC,OAAY,EAAEC,UAAe,EAAEC,MAAW;QAC3D,IAAI,OAAOF,YAAY,UAAU;YAC/B,6CAA6C;YAC7CA,UAAU;gBAAC;oBAAC;oBAAMA;oBAAS;iBAAG;aAAC;QACjC;QAEA,IAAIG,yBAA8B,CAAC;QAEnC,IAAID,QAAQ;YACV,IAAK,IAAIH,IAAI,GAAGA,IAAI,IAAI,CAACK,MAAM,EAAEL,IAAK;gBACpC,gDAAgD;gBAChD,IAAIM,KAAK,IAAI,CAACN,EAAE,CAAC,EAAE;gBAEnB,IAAIM,MAAM,MAAM;oBACdF,sBAAsB,CAACE,GAAG,GAAG;gBAC/B;YACF;QACF;QAEA,IAAK,IAAIC,KAAK,GAAGA,KAAKN,QAAQI,MAAM,EAAEE,KAAM;YAC1C,IAAIZ,OAAY,EAAE,CAACG,MAAM,CAACG,OAAO,CAACM,GAAG;YAErC,IAAIJ,UAAUC,sBAAsB,CAACT,IAAI,CAAC,EAAE,CAAC,EAAE;gBAE7C;YACF;YAEA,IAAIO,YAAY;gBACd,IAAI,CAACP,IAAI,CAAC,EAAE,EAAE;oBACZA,IAAI,CAAC,EAAE,GAAGO;gBACZ,OAAO;oBACLP,IAAI,CAAC,EAAE,GAAG,GAAGG,MAAM,CAACI,YAAY,SAASJ,MAAM,CAACH,IAAI,CAAC,EAAE;gBACzD;YACF;YAEAH,KAAKgB,IAAI,CAACb;QACZ;IACF;IAEA,OAAOH;AACT;AAEA,SAASK,uBAAuBF,IAAS,EAAEJ,YAAiB;IAC1D,IAAIK,UAAUD,IAAI,CAAC,EAAE,IAAI,GAAG,gDAAgD;;IAE5E,IAAIc,aAAad,IAAI,CAAC,EAAE;IAExB,IAAI,CAACc,YAAY;QACf,OAAOb;IACT;IAEA,IAAIL,gBAAgB,OAAOmB,SAAS,YAAY;QAC9C,mEAAmE;QACnE,IAAIC,gBAAgBC,UAAUH;QAC9B,IAAII,aAAaJ,WAAWK,OAAO,CAACpB,GAAG,CAAC,SAAUqB,MAAc;YAC9D,OAAO,iBACJjB,MAAM,CAACW,WAAWO,UAAU,IAAI,IAChClB,MAAM,CAACiB,QAAQ;QACpB;QACA,OAAO;YAACnB;SAAQ,CAACE,MAAM,CAACe,YAAYf,MAAM,CAAC;YAACa;SAAc,EAAEZ,IAAI,CAAC;IACnE;IAEA,OAAO;QAACH;KAAQ,CAACG,IAAI,CAAC;AACxB,EAAE,wCAAwC;AAE1C,SAASa,UAAUK,SAAc;IAC/B,oCAAoC;IACpC,IAAIC,SAASR,KAAKS,SAASC,mBAAmBC,KAAKC,SAAS,CAACL;IAC7D,IAAIM,OACF,+DAA+DzB,MAAM,CACnEoB;IAEJ,OAAO,OAAOpB,MAAM,CAACyB,MAAM;AAC7B"}
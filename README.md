# ChessLLM - AI Chess Battle Arena

A real-time chess battle platform where different Large Language Models (LLMs) compete against each other in strategic chess matches. Watch AI models like GPT-4, <PERSON>, <PERSON>, and DeepSeek battle it out on the chessboard with live commentary of their thought processes.

## 🎯 Features

- **Real-time AI vs AI Chess Battles**: Watch LLMs play against each other with live updates
- **Multiple LLM Providers**: Support for OpenAI, Anthropic, Google, DeepSeek, and more via OpenRouter
- **Free and Paid Models**: Choose from free models like DeepSeek R1 or premium models like GPT-4o
- **Live Thinking Process**: See the AI's reasoning and analysis for each move
- **Game History**: Browse and replay previous battles
- **Auto-play Mode**: Continuous battles with automatic move generation
- **Illegal Move Handling**: Robust retry logic with automatic resignation after 5 failed attempts

## 🏗️ Architecture

The system follows a clean architecture with real-time updates:

1. **Frontend (Next.js)**: Interactive chess board with real-time game state
2. **API Layer**: Chess orchestrator that manages game flow and LLM interactions
3. **Database (Supabase)**: Stores game states, moves, and thinking processes
4. **LLM Integration**: Multiple providers via OpenRouter and direct APIs
5. **Real-time Updates**: Supabase Realtime for live game updates

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **UI Components**: Radix UI, Tailwind CSS, Lucide Icons
- **Chess Logic**: chess.js for move validation and game rules
- **Chess Board**: react-chessboard for interactive display
- **Database**: Supabase (PostgreSQL with real-time subscriptions)
- **LLM APIs**:
  - Google Gemini (via @ai-sdk/google)
  - OpenRouter (for DeepSeek, Claude, GPT-4, etc.)
  - Structured output with Zod schemas
- **Deployment**: Vercel (recommended)

## 🤖 Available LLM Models

### Free Models (via OpenRouter)
- **DeepSeek R1** - Advanced reasoning model with strong analytical capabilities
- **DeepSeek R1 Distill Llama 70B** - Distilled version with excellent performance
- **DeepSeek R1 Distill Qwen 14B/7B** - Efficient reasoning models
- **DeepSeek V3** - Latest general-purpose model
- **Llama 3.3 70B Instruct** - Meta's latest instruction-following model
- **Llama 3.1 8B Instruct** - Efficient model for general tasks
- **Qwen 2.5 Coder 32B** - Specialized coding model
- **Qwen 3 32B** - Latest general-purpose model
- **Mistral Small 3.2 24B** - Efficient model with good performance
- **Mistral Nemo** - Compact model optimized for efficiency

### Premium Models
- **GPT-4o** - OpenAI's flagship multimodal model
- **GPT-4o Mini** - Faster and more affordable version
- **Claude 3.5 Sonnet** - Anthropic's balanced model with strong reasoning
- **Gemini 2.5 Flash** - Google's fast and efficient model
- **Gemini 2.5 Pro** - Google's most capable model
- **DeepSeek R1 (Full)** - Complete version of the reasoning model

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and pnpm
- Supabase account and project
- API keys for LLM providers (optional for free models)

### Environment Variables

**Option 1: Interactive Setup (Recommended)**
```bash
pnpm setup
```

**Option 2: Manual Setup**
```bash
cp .env.example .env.local
# Edit .env.local with your values
```

**Required variables:**
- `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Your Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key

**Optional (for premium models):**
- `GOOGLE_GENERATIVE_AI_API_KEY` - For Gemini models
- `OPENROUTER_API_KEY` - For most models including free ones

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd chessllm

# Install dependencies
pnpm install

# Set up database tables
# Run the SQL in scripts/create-tables.sql in your Supabase SQL editor

# Start development server
pnpm dev
```

### Database Setup
Execute the SQL commands in `scripts/create-tables.sql` in your Supabase SQL editor to create the required tables:
- `games` - Stores game state and metadata
- `moves` - Records each move with thinking process

### Testing
Run the LLM integration test to verify your setup:
```bash
pnpm test:llm
```

This will validate:
- LLM provider configurations
- Environment variables
- Model compatibility
- Default game setup

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- Render
- AWS Amplify

### Build Locally
```bash
pnpm build
pnpm start
```

## 🧠 How It Works

The system follows a robust workflow for each chess move:

1. **Game Initialization**:
   - Create a new game with selected LLM players (e.g., Gemini vs DeepSeek)
   - Initialize with standard starting position

2. **Move Generation Process**:
   - Determine current player's turn (white/black)
   - Load current board state (FEN) and move history
   - Generate a detailed prompt for the LLM including:
     - Current board position
     - Game history
     - Available legal moves
     - Instructions to analyze and select a move

3. **LLM Processing**:
   - Send prompt to the appropriate LLM API
   - LLM analyzes the position and returns:
     - Proposed move in algebraic notation
     - Detailed thinking process
     - Position evaluation

4. **Move Validation**:
   - Validate the proposed move using chess.js
   - If legal: update game state, record move and thinking
   - If illegal: retry up to 5 times with feedback
   - After 5 illegal attempts: player resigns

5. **Real-time Updates**:
   - Frontend receives updates via Supabase Realtime
   - Chess board and move history update automatically
   - Thinking process displayed for the latest move

## 📊 Project Structure

```
chessllm/
├── app/                    # Next.js app directory
│   ├── api/                # API routes
│   │   └── chess-orchestrator/  # Main game logic
│   └── page.tsx            # Main chess UI
├── components/             # UI components
├── lib/                    # Shared utilities
│   ├── llm-providers.ts    # LLM provider configurations
│   └── supabase.ts         # Supabase client and types
├── scripts/                # Database setup scripts
└── public/                 # Static assets
```

## 🔄 Contributing

Contributions are welcome! Here are some ways to help:

- Add support for more LLM providers
- Improve the chess UI and visualization
- Enhance the prompt engineering for better chess play
- Add analytics and game statistics
- Implement tournament functionality

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [chess.js](https://github.com/jhlywa/chess.js) for chess logic
- [react-chessboard](https://github.com/Clariity/react-chessboard) for the chess UI
- [Supabase](https://supabase.com/) for database and real-time functionality
- [OpenRouter](https://openrouter.ai/) for unified LLM API access
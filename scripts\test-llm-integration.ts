#!/usr/bin/env tsx

/**
 * Test script to validate LLM integrations
 * Run with: npx tsx scripts/test-llm-integration.ts
 */

import { LLM_PROVIDERS, getFreeModels, getPaidModels, getRecommendedChessModels } from '../lib/llm-providers'

function testLLMProviders() {
  console.log('🧪 Testing LLM Provider Configuration...\n')

  // Test 1: Check if all providers are properly configured
  console.log('📋 Total LLM Providers:', LLM_PROVIDERS.length)
  
  const freeModels = getFreeModels()
  const paidModels = getPaidModels()
  const recommendedModels = getRecommendedChessModels()
  
  console.log('🆓 Free Models:', freeModels.length)
  console.log('💰 Paid Models:', paidModels.length)
  console.log('⭐ Recommended Models:', recommendedModels.length)
  
  // Test 2: Validate free models
  console.log('\n🆓 Free Models:')
  freeModels.forEach(model => {
    console.log(`  - ${model.name} (${model.provider})`)
    if (!model.isFree) {
      console.error(`    ❌ ERROR: Model marked as free but isFree is false`)
    }
  })
  
  // Test 3: Validate paid models
  console.log('\n💰 Paid Models:')
  paidModels.forEach(model => {
    console.log(`  - ${model.name} (${model.provider})`)
    if (model.isFree) {
      console.error(`    ❌ ERROR: Model marked as paid but isFree is true`)
    }
    if (!model.pricing) {
      console.error(`    ❌ ERROR: Paid model missing pricing information`)
    }
  })
  
  // Test 4: Check for duplicate IDs
  console.log('\n🔍 Checking for duplicate model IDs...')
  const ids = LLM_PROVIDERS.map(m => m.id)
  const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index)
  if (duplicates.length > 0) {
    console.error(`❌ ERROR: Duplicate model IDs found:`, duplicates)
  } else {
    console.log('✅ No duplicate model IDs found')
  }
  
  // Test 5: Validate model categories
  console.log('\n📊 Models by Category:')
  const categories = ['reasoning', 'coding', 'general', 'creative', 'fast'] as const
  categories.forEach(category => {
    const modelsInCategory = LLM_PROVIDERS.filter(m => m.category === category)
    console.log(`  ${category}: ${modelsInCategory.length} models`)
  })
  
  // Test 6: Check recommended models for chess
  console.log('\n♟️ Recommended Chess Models:')
  recommendedModels.forEach(model => {
    console.log(`  - ${model.name} (${model.isFree ? 'Free' : 'Paid'})`)
    if (!model.capabilities.includes('reasoning')) {
      console.warn(`    ⚠️ WARNING: Chess model without reasoning capability`)
    }
  })
  
  console.log('\n✅ LLM Provider configuration test completed!')
}

function testEnvironmentVariables() {
  console.log('\n🔧 Testing Environment Variables...\n')
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  const optionalVars = [
    'GOOGLE_GENERATIVE_AI_API_KEY',
    'OPENROUTER_API_KEY'
  ]
  
  console.log('📋 Required Environment Variables:')
  requiredVars.forEach(varName => {
    const value = process.env[varName]
    if (value) {
      console.log(`  ✅ ${varName}: Set`)
    } else {
      console.error(`  ❌ ${varName}: Missing`)
    }
  })
  
  console.log('\n📋 Optional Environment Variables:')
  optionalVars.forEach(varName => {
    const value = process.env[varName]
    if (value) {
      console.log(`  ✅ ${varName}: Set`)
    } else {
      console.log(`  ⚠️ ${varName}: Not set (some models may not work)`)
    }
  })
}

function testModelCompatibility() {
  console.log('\n🔗 Testing Model Compatibility...\n')
  
  // Test default models used in the app
  const defaultWhite = 'google/gemini-2.5-flash'
  const defaultBlack = 'deepseek/deepseek-r1:free'
  
  const whiteModel = LLM_PROVIDERS.find(m => m.id === defaultWhite)
  const blackModel = LLM_PROVIDERS.find(m => m.id === defaultBlack)
  
  console.log('🏁 Default Game Configuration:')
  if (whiteModel) {
    console.log(`  ✅ White: ${whiteModel.name} (${whiteModel.provider})`)
  } else {
    console.error(`  ❌ White model not found: ${defaultWhite}`)
  }
  
  if (blackModel) {
    console.log(`  ✅ Black: ${blackModel.name} (${blackModel.provider})`)
  } else {
    console.error(`  ❌ Black model not found: ${defaultBlack}`)
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 ChessLLM Integration Tests\n')
  console.log('=' .repeat(50))
  
  try {
    testLLMProviders()
    testEnvironmentVariables()
    testModelCompatibility()
    
    console.log('\n' + '=' .repeat(50))
    console.log('🎉 All tests completed!')
    console.log('\n💡 Next steps:')
    console.log('  1. Set up your environment variables')
    console.log('  2. Run the development server: pnpm dev')
    console.log('  3. Create a new chess battle and test the LLM integrations')
    
  } catch (error) {
    console.error('\n❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the tests
runTests()

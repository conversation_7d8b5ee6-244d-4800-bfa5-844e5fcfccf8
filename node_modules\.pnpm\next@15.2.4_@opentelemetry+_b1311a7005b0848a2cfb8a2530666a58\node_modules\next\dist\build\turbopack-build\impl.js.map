{"version": 3, "sources": ["../../../src/build/turbopack-build/impl.ts"], "sourcesContent": ["import path from 'path'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isRelevantWarning,\n  type EntryIssuesMap,\n} from '../../shared/lib/turbopack/utils'\nimport { NextBuildContext } from '../build-context'\nimport { createDefineEnv, loadBindings } from '../swc'\nimport { Sema } from 'next/dist/compiled/async-sema'\nimport {\n  handleEntrypoints,\n  handlePagesErrorRoute,\n  handleRouteType,\n} from '../handle-entrypoints'\nimport type { Entrypoints } from '../swc/types'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { createProgress } from '../progress'\nimport * as Log from '../output/log'\nimport { promises as fs } from 'fs'\nimport { PHASE_PRODUCTION_BUILD } from '../../shared/lib/constants'\nimport loadConfig from '../../server/config'\nimport { hasCustomExportOutput } from '../../export/utils'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../trace'\n\nconst IS_TURBOPACK_BUILD = process.env.TURBOPACK && process.env.TURBOPACK_BUILD\n\nexport async function turbopackBuild(): Promise<{\n  duration: number\n  buildTraceContext: undefined\n  shutdownPromise: Promise<void>\n}> {\n  if (!IS_TURBOPACK_BUILD) {\n    throw new Error(\"next build doesn't support turbopack yet\")\n  }\n\n  await validateTurboNextConfig({\n    dir: NextBuildContext.dir!,\n    isDev: false,\n  })\n\n  const config = NextBuildContext.config!\n  const dir = NextBuildContext.dir!\n  const distDir = NextBuildContext.distDir!\n  const buildId = NextBuildContext.buildId!\n  const encryptionKey = NextBuildContext.encryptionKey!\n  const previewProps = NextBuildContext.previewProps!\n  const hasRewrites = NextBuildContext.hasRewrites!\n  const rewrites = NextBuildContext.rewrites!\n  const appDirOnly = NextBuildContext.appDirOnly!\n  const noMangling = NextBuildContext.noMangling!\n\n  const startTime = process.hrtime()\n  const bindings = await loadBindings(config?.experimental?.useWasmBinary)\n  const dev = false\n\n  // const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  const supportedBrowsers = [\n    'last 1 Chrome versions, last 1 Firefox versions, last 1 Safari versions, last 1 Edge versions',\n  ]\n\n  const persistentCaching = isPersistentCachingEnabled(config)\n  const project = await bindings.turbo.createProject(\n    {\n      projectPath: dir,\n      rootPath:\n        config.experimental?.turbo?.root || config.outputFileTracingRoot || dir,\n      distDir,\n      nextConfig: config,\n      jsConfig: await getTurbopackJsConfig(dir, config),\n      watch: {\n        enable: false,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters: NextBuildContext.clientRouterFilters!,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix: config.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // Implemented separately in Turbopack, doesn't have to be passed here.\n        middlewareMatchers: undefined,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling,\n    },\n    {\n      persistentCaching,\n      memoryLimit: config.experimental.turbo?.memoryLimit,\n      dependencyTracking: persistentCaching,\n    }\n  )\n\n  await fs.mkdir(path.join(distDir, 'server'), { recursive: true })\n  await fs.mkdir(path.join(distDir, 'static', buildId), {\n    recursive: true,\n  })\n  await fs.writeFile(\n    path.join(distDir, 'package.json'),\n    JSON.stringify(\n      {\n        type: 'commonjs',\n      },\n      null,\n      2\n    )\n  )\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const entrypointsSubscription = project.entrypointsSubscribe()\n  const currentEntrypoints: Entrypoints = {\n    global: {\n      app: undefined,\n      document: undefined,\n      error: undefined,\n\n      middleware: undefined,\n      instrumentation: undefined,\n    },\n\n    app: new Map(),\n    page: new Map(),\n  }\n\n  const currentEntryIssues: EntryIssuesMap = new Map()\n\n  const manifestLoader = new TurbopackManifestLoader({\n    buildId,\n    distDir,\n    encryptionKey,\n  })\n\n  const entrypointsResult = await entrypointsSubscription.next()\n  if (entrypointsResult.done) {\n    throw new Error('Turbopack did not return any entrypoints')\n  }\n  entrypointsSubscription.return?.().catch(() => {})\n\n  const entrypoints = entrypointsResult.value\n\n  const topLevelErrors: {\n    message: string\n  }[] = []\n  for (const issue of entrypoints.issues) {\n    topLevelErrors.push({\n      message: formatIssue(issue),\n    })\n  }\n\n  if (topLevelErrors.length > 0) {\n    throw new Error(\n      `Turbopack build failed with ${\n        topLevelErrors.length\n      } issues:\\n${topLevelErrors.map((e) => e.message).join('\\n')}`\n    )\n  }\n\n  await handleEntrypoints({\n    entrypoints,\n    currentEntrypoints,\n    currentEntryIssues,\n    manifestLoader,\n    productionRewrites: rewrites,\n    logErrors: false,\n  })\n\n  const progress = createProgress(\n    currentEntrypoints.page.size + currentEntrypoints.app.size + 1,\n    'Building'\n  )\n  const promises: Promise<any>[] = []\n\n  // Concurrency will start at INITIAL_CONCURRENCY and\n  // slowly ramp up to CONCURRENCY by increasing the\n  // concurrency by 1 every time a task is completed.\n  const INITIAL_CONCURRENCY = 5\n  const CONCURRENCY = 10\n\n  const sema = new Sema(INITIAL_CONCURRENCY)\n  let remainingRampup = CONCURRENCY - INITIAL_CONCURRENCY\n  const enqueue = (fn: () => Promise<void>) => {\n    promises.push(\n      (async () => {\n        await sema.acquire()\n        try {\n          await fn()\n        } finally {\n          sema.release()\n          if (remainingRampup > 0) {\n            remainingRampup--\n            sema.release()\n          }\n          progress.run()\n        }\n      })()\n    )\n  }\n\n  if (!appDirOnly) {\n    for (const [page, route] of currentEntrypoints.page) {\n      enqueue(() =>\n        handleRouteType({\n          page,\n          route,\n          currentEntryIssues,\n          entrypoints: currentEntrypoints,\n          manifestLoader,\n          productionRewrites: rewrites,\n          logErrors: false,\n        })\n      )\n    }\n  }\n\n  for (const [page, route] of currentEntrypoints.app) {\n    enqueue(() =>\n      handleRouteType({\n        page,\n        route,\n        currentEntryIssues,\n        entrypoints: currentEntrypoints,\n        manifestLoader,\n        productionRewrites: rewrites,\n        logErrors: false,\n      })\n    )\n  }\n\n  enqueue(() =>\n    handlePagesErrorRoute({\n      currentEntryIssues,\n      entrypoints: currentEntrypoints,\n      manifestLoader,\n      productionRewrites: rewrites,\n      logErrors: false,\n    })\n  )\n  await Promise.all(promises)\n\n  await manifestLoader.writeManifests({\n    devRewrites: undefined,\n    productionRewrites: rewrites,\n    entrypoints: currentEntrypoints,\n  })\n\n  const errors: {\n    page: string\n    message: string\n  }[] = []\n  const warnings: {\n    page: string\n    message: string\n  }[] = []\n  for (const [page, entryIssues] of currentEntryIssues) {\n    for (const issue of entryIssues.values()) {\n      if (issue.severity !== 'warning') {\n        errors.push({\n          page,\n          message: formatIssue(issue),\n        })\n      } else {\n        if (isRelevantWarning(issue)) {\n          warnings.push({\n            page,\n            message: formatIssue(issue),\n          })\n        }\n      }\n    }\n  }\n\n  const shutdownPromise = project.shutdown()\n\n  if (warnings.length > 0) {\n    Log.warn(\n      `Turbopack build collected ${warnings.length} warnings:\\n${warnings\n        .map((e) => {\n          return 'Page: ' + e.page + '\\n' + e.message\n        })\n        .join('\\n')}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Turbopack build failed with ${errors.length} errors:\\n${errors\n        .map((e) => {\n          return 'Page: ' + e.page + '\\n' + e.message\n        })\n        .join('\\n')}`\n    )\n  }\n\n  const time = process.hrtime(startTime)\n  return {\n    duration: time[0] + time[1] / 1e9,\n    buildTraceContext: undefined,\n    shutdownPromise,\n  }\n}\n\nlet shutdownPromise: Promise<void> | undefined\nexport async function workerMain(workerData: {\n  buildContext: typeof NextBuildContext\n}): Promise<Awaited<ReturnType<typeof turbopackBuild>>> {\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!\n  )\n\n  // Matches handling in build/index.ts\n  // https://github.com/vercel/next.js/blob/84f347fc86f4efc4ec9f13615c215e4b9fb6f8f0/packages/next/src/build/index.ts#L815-L818\n  // Ensures the `config.distDir` option is matched.\n  if (hasCustomExportOutput(NextBuildContext.config)) {\n    NextBuildContext.config.distDir = '.next'\n  }\n\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: NextBuildContext.config.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n\n  const result = await turbopackBuild()\n  shutdownPromise = result.shutdownPromise\n  return result\n}\n\nexport async function waitForShutdown(): Promise<void> {\n  if (shutdownPromise) {\n    await shutdownPromise\n  }\n}\n"], "names": ["turbopackBuild", "waitForShutdown", "worker<PERSON>ain", "IS_TURBOPACK_BUILD", "process", "env", "TURBOPACK", "TURBOPACK_BUILD", "config", "Error", "validateTurboNextConfig", "dir", "NextBuildContext", "isDev", "distDir", "buildId", "<PERSON><PERSON><PERSON>", "previewProps", "hasRewrites", "rewrites", "appDirOnly", "noMangling", "startTime", "hrtime", "bindings", "loadBindings", "experimental", "useWasmBinary", "dev", "supportedBrowsers", "persistentCaching", "isPersistentCachingEnabled", "project", "turbo", "createProject", "projectPath", "rootPath", "root", "outputFileTracingRoot", "nextConfig", "jsConfig", "getTurbopackJsConfig", "watch", "enable", "defineEnv", "createDefineEnv", "isTurbopack", "clientRouterFilters", "fetchCacheKeyPrefix", "middlewareMatchers", "undefined", "browserslistQuery", "join", "memoryLimit", "dependencyTracking", "fs", "mkdir", "path", "recursive", "writeFile", "JSON", "stringify", "type", "entrypointsSubscription", "entrypointsSubscribe", "currentEntrypoints", "global", "app", "document", "error", "middleware", "instrumentation", "Map", "page", "currentEntryIssues", "manifest<PERSON><PERSON>der", "TurbopackManifestLoader", "entrypointsResult", "next", "done", "return", "catch", "entrypoints", "value", "topLevelErrors", "issue", "issues", "push", "message", "formatIssue", "length", "map", "e", "handleEntrypoints", "productionRewrites", "logErrors", "progress", "createProgress", "size", "promises", "INITIAL_CONCURRENCY", "CONCURRENCY", "sema", "<PERSON><PERSON>", "remainingRampup", "enqueue", "fn", "acquire", "release", "run", "route", "handleRouteType", "handlePagesErrorRoute", "Promise", "all", "writeManifests", "devRewrites", "errors", "warnings", "entryIssues", "values", "severity", "isRelevantWarning", "shutdownPromise", "shutdown", "Log", "warn", "time", "duration", "buildTraceContext", "workerData", "Object", "assign", "buildContext", "loadConfig", "PHASE_PRODUCTION_BUILD", "hasCustomExportOutput", "telemetry", "Telemetry", "setGlobal", "result"], "mappings": ";;;;;;;;;;;;;;;;IA8BsBA,cAAc;eAAdA;;IAuTAC,eAAe;eAAfA;;IA9BAC,UAAU;eAAVA;;;6DAvTL;kCACuB;uBAOjC;8BAC0B;qBACa;2BACzB;mCAKd;gCAEiC;0BACT;6DACV;oBACU;2BACQ;+DAChB;wBACe;yBACZ;uBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMC,qBAAqBC,QAAQC,GAAG,CAACC,SAAS,IAAIF,QAAQC,GAAG,CAACE,eAAe;AAExE,eAAeP;QA0BgBQ,sBAa9BA,4BAAAA,uBA4BWA;IA9DjB,IAAI,CAACL,oBAAoB;QACvB,MAAM,qBAAqD,CAArD,IAAIM,MAAM,6CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IAEA,MAAMC,IAAAA,yCAAuB,EAAC;QAC5BC,KAAKC,8BAAgB,CAACD,GAAG;QACzBE,OAAO;IACT;IAEA,MAAML,SAASI,8BAAgB,CAACJ,MAAM;IACtC,MAAMG,MAAMC,8BAAgB,CAACD,GAAG;IAChC,MAAMG,UAAUF,8BAAgB,CAACE,OAAO;IACxC,MAAMC,UAAUH,8BAAgB,CAACG,OAAO;IACxC,MAAMC,gBAAgBJ,8BAAgB,CAACI,aAAa;IACpD,MAAMC,eAAeL,8BAAgB,CAACK,YAAY;IAClD,MAAMC,cAAcN,8BAAgB,CAACM,WAAW;IAChD,MAAMC,WAAWP,8BAAgB,CAACO,QAAQ;IAC1C,MAAMC,aAAaR,8BAAgB,CAACQ,UAAU;IAC9C,MAAMC,aAAaT,8BAAgB,CAACS,UAAU;IAE9C,MAAMC,YAAYlB,QAAQmB,MAAM;IAChC,MAAMC,WAAW,MAAMC,IAAAA,iBAAY,EAACjB,2BAAAA,uBAAAA,OAAQkB,YAAY,qBAApBlB,qBAAsBmB,aAAa;IACvE,MAAMC,MAAM;IAEZ,iEAAiE;IACjE,MAAMC,oBAAoB;QACxB;KACD;IAED,MAAMC,oBAAoBC,IAAAA,iCAA0B,EAACvB;IACrD,MAAMwB,UAAU,MAAMR,SAASS,KAAK,CAACC,aAAa,CAChD;QACEC,aAAaxB;QACbyB,UACE5B,EAAAA,wBAAAA,OAAOkB,YAAY,sBAAnBlB,6BAAAA,sBAAqByB,KAAK,qBAA1BzB,2BAA4B6B,IAAI,KAAI7B,OAAO8B,qBAAqB,IAAI3B;QACtEG;QACAyB,YAAY/B;QACZgC,UAAU,MAAMC,IAAAA,2BAAoB,EAAC9B,KAAKH;QAC1CkC,OAAO;YACLC,QAAQ;QACV;QACAf;QACAvB,KAAKD,QAAQC,GAAG;QAChBuC,WAAWC,IAAAA,oBAAe,EAAC;YACzBC,aAAa;YACbC,qBAAqBnC,8BAAgB,CAACmC,mBAAmB;YACzDvC;YACAoB;YACAd;YACAkC,qBAAqBxC,OAAOkB,YAAY,CAACsB,mBAAmB;YAC5D9B;YACA,uEAAuE;YACvE+B,oBAAoBC;QACtB;QACAnC;QACAC;QACAC;QACAkC,mBAAmBtB,kBAAkBuB,IAAI,CAAC;QAC1C/B;IACF,GACA;QACES;QACAuB,WAAW,GAAE7C,8BAAAA,OAAOkB,YAAY,CAACO,KAAK,qBAAzBzB,4BAA2B6C,WAAW;QACnDC,oBAAoBxB;IACtB;IAGF,MAAMyB,YAAE,CAACC,KAAK,CAACC,aAAI,CAACL,IAAI,CAACtC,SAAS,WAAW;QAAE4C,WAAW;IAAK;IAC/D,MAAMH,YAAE,CAACC,KAAK,CAACC,aAAI,CAACL,IAAI,CAACtC,SAAS,UAAUC,UAAU;QACpD2C,WAAW;IACb;IACA,MAAMH,YAAE,CAACI,SAAS,CAChBF,aAAI,CAACL,IAAI,CAACtC,SAAS,iBACnB8C,KAAKC,SAAS,CACZ;QACEC,MAAM;IACR,GACA,MACA;IAIJ,6DAA6D;IAC7D,MAAMC,0BAA0B/B,QAAQgC,oBAAoB;IAC5D,MAAMC,qBAAkC;QACtCC,QAAQ;YACNC,KAAKjB;YACLkB,UAAUlB;YACVmB,OAAOnB;YAEPoB,YAAYpB;YACZqB,iBAAiBrB;QACnB;QAEAiB,KAAK,IAAIK;QACTC,MAAM,IAAID;IACZ;IAEA,MAAME,qBAAqC,IAAIF;IAE/C,MAAMG,iBAAiB,IAAIC,uCAAuB,CAAC;QACjD7D;QACAD;QACAE;IACF;IAEA,MAAM6D,oBAAoB,MAAMd,wBAAwBe,IAAI;IAC5D,IAAID,kBAAkBE,IAAI,EAAE;QAC1B,MAAM,qBAAqD,CAArD,IAAItE,MAAM,6CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAoD;IAC5D;IACAsD,wBAAwBiB,MAAM,oBAA9BjB,wBAAwBiB,MAAM,MAA9BjB,yBAAmCkB,KAAK,CAAC,KAAO;IAEhD,MAAMC,cAAcL,kBAAkBM,KAAK;IAE3C,MAAMC,iBAEA,EAAE;IACR,KAAK,MAAMC,SAASH,YAAYI,MAAM,CAAE;QACtCF,eAAeG,IAAI,CAAC;YAClBC,SAASC,IAAAA,kBAAW,EAACJ;QACvB;IACF;IAEA,IAAID,eAAeM,MAAM,GAAG,GAAG;QAC7B,MAAM,qBAIL,CAJK,IAAIjF,MACR,CAAC,4BAA4B,EAC3B2E,eAAeM,MAAM,CACtB,UAAU,EAAEN,eAAeO,GAAG,CAAC,CAACC,IAAMA,EAAEJ,OAAO,EAAEpC,IAAI,CAAC,OAAO,GAH1D,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,MAAMyC,IAAAA,oCAAiB,EAAC;QACtBX;QACAjB;QACAS;QACAC;QACAmB,oBAAoB3E;QACpB4E,WAAW;IACb;IAEA,MAAMC,WAAWC,IAAAA,wBAAc,EAC7BhC,mBAAmBQ,IAAI,CAACyB,IAAI,GAAGjC,mBAAmBE,GAAG,CAAC+B,IAAI,GAAG,GAC7D;IAEF,MAAMC,WAA2B,EAAE;IAEnC,oDAAoD;IACpD,kDAAkD;IAClD,mDAAmD;IACnD,MAAMC,sBAAsB;IAC5B,MAAMC,cAAc;IAEpB,MAAMC,OAAO,IAAIC,eAAI,CAACH;IACtB,IAAII,kBAAkBH,cAAcD;IACpC,MAAMK,UAAU,CAACC;QACfP,SAASZ,IAAI,CACX,AAAC,CAAA;YACC,MAAMe,KAAKK,OAAO;YAClB,IAAI;gBACF,MAAMD;YACR,SAAU;gBACRJ,KAAKM,OAAO;gBACZ,IAAIJ,kBAAkB,GAAG;oBACvBA;oBACAF,KAAKM,OAAO;gBACd;gBACAZ,SAASa,GAAG;YACd;QACF,CAAA;IAEJ;IAEA,IAAI,CAACzF,YAAY;QACf,KAAK,MAAM,CAACqD,MAAMqC,MAAM,IAAI7C,mBAAmBQ,IAAI,CAAE;YACnDgC,QAAQ,IACNM,IAAAA,kCAAe,EAAC;oBACdtC;oBACAqC;oBACApC;oBACAQ,aAAajB;oBACbU;oBACAmB,oBAAoB3E;oBACpB4E,WAAW;gBACb;QAEJ;IACF;IAEA,KAAK,MAAM,CAACtB,MAAMqC,MAAM,IAAI7C,mBAAmBE,GAAG,CAAE;QAClDsC,QAAQ,IACNM,IAAAA,kCAAe,EAAC;gBACdtC;gBACAqC;gBACApC;gBACAQ,aAAajB;gBACbU;gBACAmB,oBAAoB3E;gBACpB4E,WAAW;YACb;IAEJ;IAEAU,QAAQ,IACNO,IAAAA,wCAAqB,EAAC;YACpBtC;YACAQ,aAAajB;YACbU;YACAmB,oBAAoB3E;YACpB4E,WAAW;QACb;IAEF,MAAMkB,QAAQC,GAAG,CAACf;IAElB,MAAMxB,eAAewC,cAAc,CAAC;QAClCC,aAAalE;QACb4C,oBAAoB3E;QACpB+D,aAAajB;IACf;IAEA,MAAMoD,SAGA,EAAE;IACR,MAAMC,WAGA,EAAE;IACR,KAAK,MAAM,CAAC7C,MAAM8C,YAAY,IAAI7C,mBAAoB;QACpD,KAAK,MAAMW,SAASkC,YAAYC,MAAM,GAAI;YACxC,IAAInC,MAAMoC,QAAQ,KAAK,WAAW;gBAChCJ,OAAO9B,IAAI,CAAC;oBACVd;oBACAe,SAASC,IAAAA,kBAAW,EAACJ;gBACvB;YACF,OAAO;gBACL,IAAIqC,IAAAA,wBAAiB,EAACrC,QAAQ;oBAC5BiC,SAAS/B,IAAI,CAAC;wBACZd;wBACAe,SAASC,IAAAA,kBAAW,EAACJ;oBACvB;gBACF;YACF;QACF;IACF;IAEA,MAAMsC,kBAAkB3F,QAAQ4F,QAAQ;IAExC,IAAIN,SAAS5B,MAAM,GAAG,GAAG;QACvBmC,KAAIC,IAAI,CACN,CAAC,0BAA0B,EAAER,SAAS5B,MAAM,CAAC,YAAY,EAAE4B,SACxD3B,GAAG,CAAC,CAACC;YACJ,OAAO,WAAWA,EAAEnB,IAAI,GAAG,OAAOmB,EAAEJ,OAAO;QAC7C,GACCpC,IAAI,CAAC,OAAO;IAEnB;IAEA,IAAIiE,OAAO3B,MAAM,GAAG,GAAG;QACrB,MAAM,qBAML,CANK,IAAIjF,MACR,CAAC,4BAA4B,EAAE4G,OAAO3B,MAAM,CAAC,UAAU,EAAE2B,OACtD1B,GAAG,CAAC,CAACC;YACJ,OAAO,WAAWA,EAAEnB,IAAI,GAAG,OAAOmB,EAAEJ,OAAO;QAC7C,GACCpC,IAAI,CAAC,OAAO,GALX,qBAAA;mBAAA;wBAAA;0BAAA;QAMN;IACF;IAEA,MAAM2E,OAAO3H,QAAQmB,MAAM,CAACD;IAC5B,OAAO;QACL0G,UAAUD,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,GAAG;QAC9BE,mBAAmB/E;QACnByE;IACF;AACF;AAEA,IAAIA;AACG,eAAezH,WAAWgI,UAEhC;IACC,0EAA0E;IAC1EC,OAAOC,MAAM,CAACxH,8BAAgB,EAAEsH,WAAWG,YAAY;IAEvD,iDAAiD;IACjDzH,8BAAgB,CAACJ,MAAM,GAAG,MAAM8H,IAAAA,eAAU,EACxCC,iCAAsB,EACtB3H,8BAAgB,CAACD,GAAG;IAGtB,qCAAqC;IACrC,6HAA6H;IAC7H,kDAAkD;IAClD,IAAI6H,IAAAA,6BAAqB,EAAC5H,8BAAgB,CAACJ,MAAM,GAAG;QAClDI,8BAAgB,CAACJ,MAAM,CAACM,OAAO,GAAG;IACpC;IAEA,iCAAiC;IACjC,MAAM2H,YAAY,IAAIC,kBAAS,CAAC;QAC9B5H,SAASF,8BAAgB,CAACJ,MAAM,CAACM,OAAO;IAC1C;IACA6H,IAAAA,gBAAS,EAAC,aAAaF;IAEvB,MAAMG,SAAS,MAAM5I;IACrB2H,kBAAkBiB,OAAOjB,eAAe;IACxC,OAAOiB;AACT;AAEO,eAAe3I;IACpB,IAAI0H,iBAAiB;QACnB,MAAMA;IACR;AACF"}
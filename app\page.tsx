"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Loader2, RotateCcw, Crown, Play, Users, Zap, Brain, Pause, Settings, Star, DollarSign } from "lucide-react"
import { supabase, type Database } from "@/lib/supabase"
import { Chessboard } from "react-chessboard"
import { Chess } from "chess.js"
import { LLM_PROVIDERS, getFreeModels, getPaidModels, getRecommendedChessModels, type LLMProvider } from "@/lib/llm-providers"

type Game = Database["public"]["Tables"]["games"]["Row"]
type Move = Database["public"]["Tables"]["moves"]["Row"]

export default function ChessGame() {
  const [games, setGames] = useState<Game[]>([])
  const [currentGame, setCurrentGame] = useState<Game | null>(null)
  const [moves, setMoves] = useState<Move[]>([])
  const [missingTables, setMissingTables] = useState(false)
  const [chess] = useState(new Chess())
  const [isCreatingGame, setIsCreatingGame] = useState(false)
  const [isProcessingMove, setIsProcessingMove] = useState(false)
  const [autoPlay, setAutoPlay] = useState(false)
  const [currentThinking, setCurrentThinking] = useState<string>("")
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [selectedWhiteModel, setSelectedWhiteModel] = useState<string>("google/gemini-2.5-flash")
  const [selectedBlackModel, setSelectedBlackModel] = useState<string>("deepseek/deepseek-r1:free")
  const [modelFilter, setModelFilter] = useState<"all" | "free" | "paid" | "recommended">("recommended")

  // Load games on component mount
  useEffect(() => {
    loadGames()
  }, [])

  // Auto-play effect
  useEffect(() => {
    if (autoPlay && currentGame && currentGame.status === "in_progress" && !isProcessingMove) {
      const timer = setTimeout(() => {
        triggerNextMove(currentGame.id)
      }, 2000) // 2 second delay between moves

      return () => clearTimeout(timer)
    }
  }, [autoPlay, currentGame, moves, isProcessingMove])

  // Subscribe to real-time updates for current game
  useEffect(() => {
    if (!currentGame) return

    const gameChannel = supabase
      .channel(`game-${currentGame.id}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "games",
          filter: `id=eq.${currentGame.id}`,
        },
        (payload) => {
          if (payload.eventType === "UPDATE") {
            setCurrentGame(payload.new as Game)
            chess.load((payload.new as Game).fen)
          }
        },
      )
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "moves",
          filter: `game_id=eq.${currentGame.id}`,
        },
        (payload) => {
          const newMove = payload.new as Move
          setMoves((prev) => [...prev, newMove])
          if (newMove.llm_thinking_process) {
            setCurrentThinking(newMove.llm_thinking_process)
          }
        },
      )
      .subscribe()

    return () => {
      supabase.removeChannel(gameChannel)
    }
  }, [currentGame, chess])

  const loadGames = async () => {
    try {
      // Check if Supabase is properly configured
      if (!process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL === 'https://placeholder.supabase.co') {
        console.warn("Supabase not configured. Please set up your environment variables.")
        setMissingTables(true)
        return
      }

      const { data, error } = await supabase.from("games").select("*").order("created_at", { ascending: false }).limit(10)

      if (error) {
        if (error.code === "42P01") {
          console.warn("Table 'games' not found. Please run the DB migration.")
          setMissingTables(true)
        } else {
          console.error("Error loading games:", error.message || error)
        }
        return
      }

      setGames(data || [])
      setMissingTables(false)
    } catch (err) {
      console.error("Failed to load games:", err)
      setMissingTables(true)
    }
  }

  const createNewGame = async () => {
    setIsCreatingGame(true)

    try {
      // Check if Supabase is properly configured
      if (!process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL === 'https://placeholder.supabase.co') {
        alert("Please configure your Supabase environment variables first!")
        setIsCreatingGame(false)
        return
      }

      const { data, error } = await supabase
        .from("games")
        .insert({
          white_player_llm: selectedWhiteModel,
          black_player_llm: selectedBlackModel,
        })
        .select()
        .single()

      if (error) {
        console.error("Error creating game:", error.message || error)
        alert(`Failed to create game: ${error.message || 'Unknown error'}`)
        setIsCreatingGame(false)
        return
      }

      if (!data) {
        console.error("No data returned from game creation")
        alert("Failed to create game: No data returned")
        setIsCreatingGame(false)
        return
      }

      setCurrentGame(data)
      chess.reset()
      setMoves([])
      setCurrentThinking("")
      setIsCreatingGame(false)
      setAutoPlay(true) // Auto-start the battle
      setShowModelSelector(false) // Close the model selector

      // Start the game by triggering the first move
      await triggerNextMove(data.id)
    } catch (err) {
      console.error("Failed to create game:", err)
      alert("Failed to create game. Please check your configuration.")
      setIsCreatingGame(false)
    }
  }

  const selectGame = async (game: Game) => {
    setCurrentGame(game)
    chess.load(game.fen)
    setAutoPlay(false) // Stop auto-play when selecting different game

    // Load moves for this game
    const { data: movesData, error } = await supabase
      .from("moves")
      .select("*")
      .eq("game_id", game.id)
      .order("move_number", { ascending: true })

    if (error) {
      console.error("Error loading moves:", error)
      return
    }

    setMoves(movesData || [])

    // Get the latest thinking process
    const latestMove = movesData?.[movesData.length - 1]
    if (latestMove?.llm_thinking_process) {
      setCurrentThinking(latestMove.llm_thinking_process)
    }
  }

  const triggerNextMove = async (gameId: string) => {
    if (isProcessingMove) return // Prevent multiple simultaneous requests

    setIsProcessingMove(true)
    try {
      const response = await fetch("/api/chess-orchestrator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ gameId }),
      })

      const result = await response.json()

      if (!response.ok) {
        console.error("Failed to trigger next move:", result.error)
        throw new Error(result.error || "Failed to trigger next move")
      }

      console.log("Move result:", result)

      // If game ended, stop auto-play
      if (result.gameStatus && result.gameStatus !== "in_progress") {
        setAutoPlay(false)
      }
    } catch (error) {
      console.error("Error triggering next move:", error)
      setAutoPlay(false) // Stop auto-play on error
    } finally {
      setIsProcessingMove(false)
    }
  }

  const getFilteredModels = (): LLMProvider[] => {
    switch (modelFilter) {
      case "free":
        return getFreeModels()
      case "paid":
        return getPaidModels()
      case "recommended":
        return getRecommendedChessModels()
      default:
        return LLM_PROVIDERS
    }
  }

  const getModelDisplayName = (modelId: string): string => {
    const model = LLM_PROVIDERS.find(m => m.id === modelId)
    return model ? model.name : modelId
  }

  const getModelIcon = (modelId: string) => {
    const model = LLM_PROVIDERS.find(m => m.id === modelId)
    if (!model) return <Brain className="w-3 h-3" />

    if (model.isFree) {
      return <Star className="w-3 h-3 text-yellow-500" />
    } else {
      return <DollarSign className="w-3 h-3 text-green-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "in_progress":
        return "default"
      case "completed":
        return "secondary"
      case "resigned":
        return "destructive"
      case "draw":
        return "outline"
      default:
        return "default"
    }
  }

  const toggleAutoPlay = () => {
    if (!currentGame || currentGame.status !== "in_progress") return

    setAutoPlay(!autoPlay)

    // If starting auto-play and it's not currently processing, trigger next move
    if (!autoPlay && !isProcessingMove) {
      triggerNextMove(currentGame.id)
    }
  }

  const resetGame = async () => {
    if (!currentGame) return

    setAutoPlay(false) // Stop auto-play during reset

    const { error } = await supabase
      .from("games")
      .update({
        fen: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
        status: "in_progress",
        current_turn: "white",
        winner: null,
        white_illegal_attempts: 0,
        black_illegal_attempts: 0,
      })
      .eq("id", currentGame.id)

    if (error) {
      console.error("Error resetting game:", error)
      return
    }

    // Delete all moves for this game
    await supabase.from("moves").delete().eq("game_id", currentGame.id)

    chess.reset()
    setMoves([])
    setCurrentThinking("")
  }



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-amber-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-2">
            <Crown className="w-8 h-8 text-amber-600" />
            AI Chess Battle Arena
          </h1>
          <div className="flex items-center justify-center gap-4 text-lg">
            <div className="flex items-center gap-2 px-4 py-2 bg-blue-100 rounded-full">
              <Zap className="w-5 h-5 text-blue-600" />
              <span className="font-semibold text-blue-800">{getModelDisplayName(selectedWhiteModel)}</span>
            </div>
            <span className="text-2xl">⚔️</span>
            <div className="flex items-center gap-2 px-4 py-2 bg-purple-100 rounded-full">
              <Brain className="w-5 h-5 text-purple-600" />
              <span className="font-semibold text-purple-800">{getModelDisplayName(selectedBlackModel)}</span>
            </div>
          </div>
        </div>

        {missingTables && (
          <div className="mb-4 rounded-lg border border-red-300 bg-red-50 p-4 text-red-700">
            <p className="font-semibold">⚠️ Setup Required</p>
            <div className="mt-2 space-y-2 text-sm">
              {(!process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL === 'https://placeholder.supabase.co') ? (
                <>
                  <p>1. Configure your Supabase environment variables:</p>
                  <ul className="ml-4 list-disc space-y-1">
                    <li>Copy <code className="font-mono">.env.example</code> to <code className="font-mono">.env.local</code></li>
                    <li>Fill in your Supabase project URL and keys</li>
                  </ul>
                </>
              ) : (
                <>
                  <p>2. Create database tables:</p>
                  <ul className="ml-4 list-disc space-y-1">
                    <li>Run the SQL in <code className="font-mono">scripts/create-tables.sql</code> in your Supabase SQL editor</li>
                    <li>Refresh this page after creating the tables</li>
                  </ul>
                </>
              )}
            </div>
          </div>
        )}

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Game List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Battle History</span>
                  <Dialog open={showModelSelector} onOpenChange={setShowModelSelector}>
                    <DialogTrigger asChild>
                      <Button
                        onClick={() => setShowModelSelector(true)}
                        disabled={isCreatingGame}
                        size="sm"
                        className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                      >
                        {isCreatingGame ? <Loader2 className="w-4 h-4 animate-spin" /> : <Play className="w-4 h-4" />}
                        {!isCreatingGame && "New Battle"}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[600px]">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <Settings className="w-5 h-5" />
                          Configure AI Battle
                        </DialogTitle>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="flex items-center justify-center gap-4 mb-4">
                          <Button
                            size="sm"
                            variant={modelFilter === "all" ? "default" : "outline"}
                            onClick={() => setModelFilter("all")}
                          >
                            All Models
                          </Button>
                          <Button
                            size="sm"
                            variant={modelFilter === "free" ? "default" : "outline"}
                            onClick={() => setModelFilter("free")}
                            className="flex items-center gap-1"
                          >
                            <Star className="w-4 h-4 text-yellow-500" />
                            Free
                          </Button>
                          <Button
                            size="sm"
                            variant={modelFilter === "paid" ? "default" : "outline"}
                            onClick={() => setModelFilter("paid")}
                            className="flex items-center gap-1"
                          >
                            <DollarSign className="w-4 h-4 text-green-500" />
                            Premium
                          </Button>
                          <Button
                            size="sm"
                            variant={modelFilter === "recommended" ? "default" : "outline"}
                            onClick={() => setModelFilter("recommended")}
                          >
                            Recommended
                          </Button>
                        </div>

                        <div className="grid grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <label className="text-sm font-medium flex items-center gap-2">
                              <div className="w-4 h-4 bg-white rounded-full border-2 border-gray-300"></div>
                              White Player
                            </label>
                            <Select value={selectedWhiteModel} onValueChange={setSelectedWhiteModel}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select White Player" />
                              </SelectTrigger>
                              <SelectContent className="max-h-[300px]">
                                {getFilteredModels().map((model) => (
                                  <SelectItem key={`white-${model.id}`} value={model.id}>
                                    <div className="flex items-center gap-2">
                                      {model.isFree ? (
                                        <Star className="w-3 h-3 text-yellow-500" />
                                      ) : (
                                        <DollarSign className="w-3 h-3 text-green-500" />
                                      )}
                                      {model.name}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium flex items-center gap-2">
                              <div className="w-4 h-4 bg-black rounded-full border-2 border-gray-300"></div>
                              Black Player
                            </label>
                            <Select value={selectedBlackModel} onValueChange={setSelectedBlackModel}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select Black Player" />
                              </SelectTrigger>
                              <SelectContent className="max-h-[300px]">
                                {getFilteredModels().map((model) => (
                                  <SelectItem key={`black-${model.id}`} value={model.id}>
                                    <div className="flex items-center gap-2">
                                      {model.isFree ? (
                                        <Star className="w-3 h-3 text-yellow-500" />
                                      ) : (
                                        <DollarSign className="w-3 h-3 text-green-500" />
                                      )}
                                      {model.name}
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="flex justify-end mt-4">
                          <Button onClick={createNewGame} disabled={isCreatingGame}>
                            {isCreatingGame ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
                            Start Battle
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 max-h-96 overflow-y-auto">
                {games.map((game) => (
                  <div
                    key={game.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      currentGame?.id === game.id
                        ? "bg-gradient-to-r from-blue-50 to-purple-50 border-blue-300"
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() => selectGame(game)}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <Badge variant={getStatusColor(game.status)}>{game.status}</Badge>
                      <span className="text-xs text-gray-500">{new Date(game.created_at).toLocaleDateString()}</span>
                    </div>
                    <div className="text-sm space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="w-3 h-3 bg-white border border-gray-400 rounded-sm"></span>
                        <div className="flex items-center gap-1">
                          {getModelIcon(game.white_player_llm)}
                          <span className="text-xs font-medium">{getModelDisplayName(game.white_player_llm)}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-3 h-3 bg-gray-800 rounded-sm"></span>
                        <div className="flex items-center gap-1">
                          {getModelIcon(game.black_player_llm)}
                          <span className="text-xs font-medium">{getModelDisplayName(game.black_player_llm)}</span>
                        </div>
                      </div>
                    </div>
                    {game.winner && (
                      <div className="mt-2 text-xs font-medium text-green-600 flex items-center gap-1">
                        🏆 Winner:{" "}
                        {getModelDisplayName(game.winner === "white" ? game.white_player_llm : game.black_player_llm)}
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Chess Board */}
          <div className="lg:col-span-2">
            {currentGame ? (
              <Card className="p-6 shadow-lg">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-4">
                    <Badge
                      variant={currentGame.current_turn === "white" ? "default" : "secondary"}
                      className="px-3 py-1"
                    >
                      <div className="flex items-center gap-2">
                        {getModelIcon(currentGame.white_player_llm)}
                        <span>White: {getModelDisplayName(currentGame.white_player_llm)}</span>
                      </div>
                    </Badge>
                    <Badge
                      variant={currentGame.current_turn === "black" ? "default" : "secondary"}
                      className="px-3 py-1"
                    >
                      <div className="flex items-center gap-2">
                        {getModelIcon(currentGame.black_player_llm)}
                        <span>Black: {getModelDisplayName(currentGame.black_player_llm)}</span>
                      </div>
                    </Badge>
                  </div>
                  <div className="flex gap-2">
                    {currentGame.status === "in_progress" && (
                      <Button
                        onClick={toggleAutoPlay}
                        variant={autoPlay ? "destructive" : "default"}
                        size="sm"
                        disabled={isProcessingMove}
                        className={autoPlay ? "bg-red-500 hover:bg-red-600" : "bg-green-500 hover:bg-green-600"}
                      >
                        {isProcessingMove ? (
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        ) : autoPlay ? (
                          <Pause className="w-4 h-4 mr-2" />
                        ) : (
                          <Play className="w-4 h-4 mr-2" />
                        )}
                        {autoPlay ? "Stop Auto" : "Start Auto"}
                      </Button>
                    )}
                    <Button
                      onClick={() => triggerNextMove(currentGame.id)}
                      variant="outline"
                      size="sm"
                      disabled={currentGame.status !== "in_progress" || isProcessingMove}
                      className="border-blue-300 hover:bg-blue-50"
                    >
                      {isProcessingMove ? (
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      ) : (
                        <Users className="w-4 h-4 mr-2" />
                      )}
                      {isProcessingMove ? "Processing..." : "Next Move"}
                    </Button>
                    <Button
                      onClick={resetGame}
                      variant="outline"
                      size="sm"
                      className="border-red-300 hover:bg-red-50 bg-transparent"
                      disabled={isProcessingMove}
                    >
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Reset
                    </Button>
                  </div>
                </div>

                <div className="flex justify-center">
                  <div className="w-full max-w-lg">
                    <Chessboard position={currentGame.fen} arePiecesDraggable={false} boardWidth={400} />
                  </div>
                </div>

                <div className="mt-4 text-center">
                  <Badge variant={getStatusColor(currentGame.status)} className="text-lg px-4 py-2">
                    {currentGame.status === "in_progress" ? (
                      <div className="flex items-center gap-2">
                        {isProcessingMove && <Loader2 className="w-4 h-4 animate-spin" />}
                        {getModelIcon(
                          currentGame.current_turn === "white"
                            ? currentGame.white_player_llm
                            : currentGame.black_player_llm,
                        )}
                        <span>
                          {getModelDisplayName(
                            currentGame.current_turn === "white"
                              ? currentGame.white_player_llm
                              : currentGame.black_player_llm,
                          )}
                          {isProcessingMove ? " is thinking..." : "'s turn"}
                        </span>
                        {autoPlay && !isProcessingMove && <span className="text-green-500 ml-2">🔄 AUTO</span>}
                      </div>
                    ) : (
                      currentGame.status
                    )}
                  </Badge>
                  {currentGame.winner && (
                    <div className="mt-2 text-lg font-bold text-green-600 flex items-center justify-center gap-2">
                      🏆 Winner:
                      {getModelIcon(
                        currentGame.winner === "white" ? currentGame.white_player_llm : currentGame.black_player_llm,
                      )}
                      {getModelDisplayName(
                        currentGame.winner === "white" ? currentGame.white_player_llm : currentGame.black_player_llm,
                      )}
                    </div>
                  )}
                </div>
              </Card>
            ) : (
              <Card className="p-6 shadow-lg">
                <div className="text-center text-gray-500">
                  <Crown className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-xl font-semibold mb-2">No Battle Selected</h3>
                  <p>Create a new battle or select an existing one to watch the AI showdown</p>
                </div>
              </Card>
            )}
          </div>

          {/* Game Info */}
          <div className="lg:col-span-1 space-y-4">
            {currentGame && (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Battle Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Current Turn:</span>
                        <div className="flex items-center gap-1">
                          {getModelIcon(
                            currentGame.current_turn === "white"
                              ? currentGame.white_player_llm
                              : currentGame.black_player_llm,
                          )}
                          <Badge variant={currentGame.current_turn === "white" ? "default" : "secondary"}>
                            {currentGame.current_turn}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <span>Total Moves:</span>
                        <span>{moves.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <Badge variant={getStatusColor(currentGame.status)}>{currentGame.status}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Auto Play:</span>
                        <Badge variant={autoPlay ? "default" : "secondary"}>{autoPlay ? "ON" : "OFF"}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Gemini Fails:</span>
                        <span className="text-red-600 font-medium">{currentGame.white_illegal_attempts}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>DeepSeek Fails:</span>
                        <span className="text-red-600 font-medium">{currentGame.black_illegal_attempts}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {currentThinking && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Brain className="w-5 h-5" />
                        AI Reasoning
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm text-muted-foreground whitespace-pre-wrap max-h-40 overflow-y-auto bg-gray-50 p-3 rounded">
                        {currentThinking}
                      </div>
                    </CardContent>
                  </Card>
                )}

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Move History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1 max-h-60 overflow-y-auto">
                      {moves.length === 0 ? (
                        <p className="text-sm text-muted-foreground">Battle hasn't started yet</p>
                      ) : (
                        moves.map((move, index) => (
                          <div key={move.id} className="text-sm flex justify-between items-center py-1">
                            <span className="font-mono text-gray-500">
                              {Math.floor(index / 2) + 1}.{move.player_color === "white" ? "" : ".."}
                            </span>
                            <span className="font-mono font-medium">{move.move_san}</span>
                            <div className="flex items-center gap-1">
                              {getModelIcon(
                                move.player_color === "white"
                                  ? currentGame.white_player_llm
                                  : currentGame.black_player_llm,
                              )}
                              <Badge variant="outline" className="text-xs">
                                {move.player_color}
                              </Badge>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

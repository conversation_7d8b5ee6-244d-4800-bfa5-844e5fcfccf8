OPENROUTER_API_KEY="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"
POSTGRES_URL="postgres://postgres.gkhscuawgjvqmjxadmdg:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.x"
POSTGRES_USER="postgres"
POSTGRES_HOST="db.gkhscuawgjvqmjxadmdg.supabase.co"
SUPABASE_JWT_SECRET="osD2nJnPjlw9pyfA1nFxFTmnYQOTfZLn6Jm014kJoC9KinHAeEz57Yyy3PnVm1T7oZrbmCR47xhwdMfPlElGwg=="
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdraHNjdWF3Z2p2cW1qeGFkbWRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMzM0MjMsImV4cCI6MjA2NzcwOTQyM30.7URrs9NvA1SOWDFnRKL8Yf1_76cYuv6MMoMgMvJMh2w"
POSTGRES_PRISMA_URL="postgres://postgres.gkhscuawgjvqmjxadmdg:<EMAIL>:6543/postgres?sslmode=require&pgbouncer=true"
POSTGRES_PASSWORD="WpTqjYCxFAj6QQig"
POSTGRES_DATABASE="postgres"
SUPABASE_URL="https://gkhscuawgjvqmjxadmdg.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdraHNjdWF3Z2p2cW1qeGFkbWRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMzM0MjMsImV4cCI6MjA2NzcwOTQyM30.7URrs9NvA1SOWDFnRKL8Yf1_76cYuv6MMoMgMvJMh2w"
NEXT_PUBLIC_SUPABASE_URL="https://gkhscuawgjvqmjxadmdg.supabase.co"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdraHNjdWF3Z2p2cW1qeGFkbWRnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjEzMzQyMywiZXhwIjoyMDY3NzA5NDIzfQ.TN83nT81_FQrSHrO40bInam268OFhTZjokEL16eGF8k"
POSTGRES_URL_NON_POOLING="postgres://postgres.gkhscuawgjvqmjxadmdg:<EMAIL>:5432/postgres?sslmode=require"
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+supabase-js@2.50.4";
exports.ids = ["vendor-chunks/@supabase+supabase-js@2.50.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(rsc)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(rsc)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(rsc)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\nclass SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */\n    constructor(supabaseUrl, supabaseKey, options) {\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl)\n            throw new Error('supabaseUrl is required.');\n        if (!supabaseKey)\n            throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), { storageKey: defaultStorageKey }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS,\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        }\n        else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop) => {\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                },\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({ headers: this.headers, accessToken: this._getAccessToken.bind(this) }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch,\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */\n    get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch,\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */\n    get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */\n    channel(name, opts = { config: {} }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */\n    getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */\n    removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */\n    removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug, }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`,\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), { params: Object.assign({ apikey: this.supabaseKey }, options === null || options === void 0 ? void 0 : options.params) }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session) => {\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n            this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        }\n        else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE')\n                this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n}\n//# sourceMappingURL=SupabaseClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(rsc)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(rsc)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(rsc)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(rsc)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */\nconst createClient = (supabaseUrl, supabaseKey, options) => {\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNaO0FBQ3VCO0FBQzhFO0FBQ2pHO0FBQ3VCO0FBQzdEO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsZUFBZSx1REFBYztBQUM3QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlbmRvYXJzYW5kaVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGVzc2xsbVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTdXBhYmFzZUNsaWVudCBmcm9tICcuL1N1cGFiYXNlQ2xpZW50JztcbmV4cG9ydCAqIGZyb20gJ0BzdXBhYmFzZS9hdXRoLWpzJztcbmV4cG9ydCB7IFBvc3RncmVzdEVycm9yLCB9IGZyb20gJ0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMnO1xuZXhwb3J0IHsgRnVuY3Rpb25zSHR0cEVycm9yLCBGdW5jdGlvbnNGZXRjaEVycm9yLCBGdW5jdGlvbnNSZWxheUVycm9yLCBGdW5jdGlvbnNFcnJvciwgRnVuY3Rpb25SZWdpb24sIH0gZnJvbSAnQHN1cGFiYXNlL2Z1bmN0aW9ucy1qcyc7XG5leHBvcnQgKiBmcm9tICdAc3VwYWJhc2UvcmVhbHRpbWUtanMnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdXBhYmFzZUNsaWVudCB9IGZyb20gJy4vU3VwYWJhc2VDbGllbnQnO1xuLyoqXG4gKiBDcmVhdGVzIGEgbmV3IFN1cGFiYXNlIENsaWVudC5cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUNsaWVudCA9IChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIG9wdGlvbnMpID0+IHtcbiAgICByZXR1cm4gbmV3IFN1cGFiYXNlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgb3B0aW9ucyk7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(rsc)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options) {\n        super(options);\n    }\n}\n//# sourceMappingURL=SupabaseAuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9TdXBhYmFzZUF1dGhDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDeEMsaUNBQWlDLHlEQUFVO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVuZG9hcnNhbmRpXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoZXNzbGxtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3VwYWJhc2UtanNAMi41MC40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3VwYWJhc2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxTdXBhYmFzZUF1dGhDbGllbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXV0aENsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9hdXRoLWpzJztcbmV4cG9ydCBjbGFzcyBTdXBhYmFzZUF1dGhDbGllbnQgZXh0ZW5kcyBBdXRoQ2xpZW50IHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVN1cGFiYXNlQXV0aENsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n}\nelse if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n}\nelse if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n}\nelse {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS,\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public',\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit',\n};\nconst DEFAULT_REALTIME_OPTIONS = {};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywwQkFBMEIsZ0NBQWdDLE9BQU8sR0FBRyw2Q0FBTyxDQUFDO0FBQzVFO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVuZG9hcnNhbmRpXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoZXNzbGxtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3VwYWJhc2UtanNAMi41MC40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3VwYWJhc2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbic7XG5sZXQgSlNfRU5WID0gJyc7XG4vLyBAdHMtaWdub3JlXG5pZiAodHlwZW9mIERlbm8gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgSlNfRU5WID0gJ2Rlbm8nO1xufVxuZWxzZSBpZiAodHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJykge1xuICAgIEpTX0VOViA9ICd3ZWInO1xufVxuZWxzZSBpZiAodHlwZW9mIG5hdmlnYXRvciAhPT0gJ3VuZGVmaW5lZCcgJiYgbmF2aWdhdG9yLnByb2R1Y3QgPT09ICdSZWFjdE5hdGl2ZScpIHtcbiAgICBKU19FTlYgPSAncmVhY3QtbmF0aXZlJztcbn1cbmVsc2Uge1xuICAgIEpTX0VOViA9ICdub2RlJztcbn1cbmV4cG9ydCBjb25zdCBERUZBVUxUX0hFQURFUlMgPSB7ICdYLUNsaWVudC1JbmZvJzogYHN1cGFiYXNlLWpzLSR7SlNfRU5WfS8ke3ZlcnNpb259YCB9O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfR0xPQkFMX09QVElPTlMgPSB7XG4gICAgaGVhZGVyczogREVGQVVMVF9IRUFERVJTLFxufTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX0RCX09QVElPTlMgPSB7XG4gICAgc2NoZW1hOiAncHVibGljJyxcbn07XG5leHBvcnQgY29uc3QgREVGQVVMVF9BVVRIX09QVElPTlMgPSB7XG4gICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gICAgZmxvd1R5cGU6ICdpbXBsaWNpdCcsXG59O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfUkVBTFRJTUVfT1BUSU9OUyA9IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(rsc)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveHeadersConstructor = () => {\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n    const fetch = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n        var _a;\n        const accessToken = (_a = (yield getAccessToken())) !== null && _a !== void 0 ? _a : supabaseKey;\n        let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n        if (!headers.has('apikey')) {\n            headers.set('apikey', supabaseKey);\n        }\n        if (!headers.has('Authorization')) {\n            headers.set('Authorization', `Bearer ${accessToken}`);\n        }\n        return fetch(input, Object.assign(Object.assign({}, init), { headers }));\n    });\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = () => typeof window !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions, } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS, } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), { headers: Object.assign(Object.assign({}, ((_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {})), ((_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})) }),\n        accessToken: () => __awaiter(this, void 0, void 0, function* () { return ''; }),\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    }\n    else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.50.4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlbmRvYXJzYW5kaVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGVzc2xsbVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjUwLjQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\nclass SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */\n    constructor(supabaseUrl, supabaseKey, options) {\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl)\n            throw new Error('supabaseUrl is required.');\n        if (!supabaseKey)\n            throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), { storageKey: defaultStorageKey }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS,\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        }\n        else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop) => {\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                },\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({ headers: this.headers, accessToken: this._getAccessToken.bind(this) }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch,\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */\n    get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch,\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */\n    get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */\n    channel(name, opts = { config: {} }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */\n    getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */\n    removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */\n    removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug, }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`,\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), { params: Object.assign({ apikey: this.supabaseKey }, options === null || options === void 0 ? void 0 : options.params) }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session) => {\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n            this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        }\n        else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE')\n                this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n}\n//# sourceMappingURL=SupabaseClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.21.0/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */\nconst createClient = (supabaseUrl, supabaseKey, options) => {\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNaO0FBQ3VCO0FBQzhFO0FBQ2pHO0FBQ3VCO0FBQzdEO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsZUFBZSx1REFBYztBQUM3QjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlbmRvYXJzYW5kaVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGVzc2xsbVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTdXBhYmFzZUNsaWVudCBmcm9tICcuL1N1cGFiYXNlQ2xpZW50JztcbmV4cG9ydCAqIGZyb20gJ0BzdXBhYmFzZS9hdXRoLWpzJztcbmV4cG9ydCB7IFBvc3RncmVzdEVycm9yLCB9IGZyb20gJ0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMnO1xuZXhwb3J0IHsgRnVuY3Rpb25zSHR0cEVycm9yLCBGdW5jdGlvbnNGZXRjaEVycm9yLCBGdW5jdGlvbnNSZWxheUVycm9yLCBGdW5jdGlvbnNFcnJvciwgRnVuY3Rpb25SZWdpb24sIH0gZnJvbSAnQHN1cGFiYXNlL2Z1bmN0aW9ucy1qcyc7XG5leHBvcnQgKiBmcm9tICdAc3VwYWJhc2UvcmVhbHRpbWUtanMnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdXBhYmFzZUNsaWVudCB9IGZyb20gJy4vU3VwYWJhc2VDbGllbnQnO1xuLyoqXG4gKiBDcmVhdGVzIGEgbmV3IFN1cGFiYXNlIENsaWVudC5cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUNsaWVudCA9IChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIG9wdGlvbnMpID0+IHtcbiAgICByZXR1cm4gbmV3IFN1cGFiYXNlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgb3B0aW9ucyk7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options) {\n        super(options);\n    }\n}\n//# sourceMappingURL=SupabaseAuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9TdXBhYmFzZUF1dGhDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDeEMsaUNBQWlDLHlEQUFVO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVuZG9hcnNhbmRpXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoZXNzbGxtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3VwYWJhc2UtanNAMi41MC40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3VwYWJhc2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxTdXBhYmFzZUF1dGhDbGllbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXV0aENsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9hdXRoLWpzJztcbmV4cG9ydCBjbGFzcyBTdXBhYmFzZUF1dGhDbGllbnQgZXh0ZW5kcyBBdXRoQ2xpZW50IHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVN1cGFiYXNlQXV0aENsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n}\nelse if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n}\nelse if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n}\nelse {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS,\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public',\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit',\n};\nconst DEFAULT_REALTIME_OPTIONS = {};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywwQkFBMEIsZ0NBQWdDLE9BQU8sR0FBRyw2Q0FBTyxDQUFDO0FBQzVFO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVuZG9hcnNhbmRpXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGNoZXNzbGxtXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3VwYWJhc2UtanNAMi41MC40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3VwYWJhc2UtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbic7XG5sZXQgSlNfRU5WID0gJyc7XG4vLyBAdHMtaWdub3JlXG5pZiAodHlwZW9mIERlbm8gIT09ICd1bmRlZmluZWQnKSB7XG4gICAgSlNfRU5WID0gJ2Rlbm8nO1xufVxuZWxzZSBpZiAodHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJykge1xuICAgIEpTX0VOViA9ICd3ZWInO1xufVxuZWxzZSBpZiAodHlwZW9mIG5hdmlnYXRvciAhPT0gJ3VuZGVmaW5lZCcgJiYgbmF2aWdhdG9yLnByb2R1Y3QgPT09ICdSZWFjdE5hdGl2ZScpIHtcbiAgICBKU19FTlYgPSAncmVhY3QtbmF0aXZlJztcbn1cbmVsc2Uge1xuICAgIEpTX0VOViA9ICdub2RlJztcbn1cbmV4cG9ydCBjb25zdCBERUZBVUxUX0hFQURFUlMgPSB7ICdYLUNsaWVudC1JbmZvJzogYHN1cGFiYXNlLWpzLSR7SlNfRU5WfS8ke3ZlcnNpb259YCB9O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfR0xPQkFMX09QVElPTlMgPSB7XG4gICAgaGVhZGVyczogREVGQVVMVF9IRUFERVJTLFxufTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX0RCX09QVElPTlMgPSB7XG4gICAgc2NoZW1hOiAncHVibGljJyxcbn07XG5leHBvcnQgY29uc3QgREVGQVVMVF9BVVRIX09QVElPTlMgPSB7XG4gICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSxcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUsXG4gICAgZmxvd1R5cGU6ICdpbXBsaWNpdCcsXG59O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfUkVBTFRJTUVfT1BUSU9OUyA9IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveHeadersConstructor = () => {\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n    const fetch = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n        var _a;\n        const accessToken = (_a = (yield getAccessToken())) !== null && _a !== void 0 ? _a : supabaseKey;\n        let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n        if (!headers.has('apikey')) {\n            headers.set('apikey', supabaseKey);\n        }\n        if (!headers.has('Authorization')) {\n            headers.set('Authorization', `Bearer ${accessToken}`);\n        }\n        return fetch(input, Object.assign(Object.assign({}, init), { headers }));\n    });\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = () => typeof window !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions, } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS, } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), { headers: Object.assign(Object.assign({}, ((_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {})), ((_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})) }),\n        accessToken: () => __awaiter(this, void 0, void 0, function* () { return ''; }),\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    }\n    else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.50.4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlbmRvYXJzYW5kaVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxjaGVzc2xsbVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjUwLjQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.4/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ })

};
;
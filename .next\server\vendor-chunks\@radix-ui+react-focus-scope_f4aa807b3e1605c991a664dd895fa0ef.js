"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef/node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef/node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/FocusScope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"FocusScope.useComposedRefs[composedRefs]\": (node)=>setContainer(node)\n    }[\"FocusScope.useComposedRefs[composedRefs]\"]);\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (trapped) {\n                let handleFocusIn2 = {\n                    \"FocusScope.useEffect.handleFocusIn2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const target = event.target;\n                        if (container.contains(target)) {\n                            lastFocusedElementRef.current = target;\n                        } else {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusIn2\"], handleFocusOut2 = {\n                    \"FocusScope.useEffect.handleFocusOut2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const relatedTarget = event.relatedTarget;\n                        if (relatedTarget === null) return;\n                        if (!container.contains(relatedTarget)) {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusOut2\"], handleMutations2 = {\n                    \"FocusScope.useEffect.handleMutations2\": function(mutations) {\n                        const focusedElement = document.activeElement;\n                        if (focusedElement !== document.body) return;\n                        for (const mutation of mutations){\n                            if (mutation.removedNodes.length > 0) focus(container);\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleMutations2\"];\n                var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n                document.addEventListener(\"focusin\", handleFocusIn2);\n                document.addEventListener(\"focusout\", handleFocusOut2);\n                const mutationObserver = new MutationObserver(handleMutations2);\n                if (container) mutationObserver.observe(container, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        document.removeEventListener(\"focusin\", handleFocusIn2);\n                        document.removeEventListener(\"focusout\", handleFocusOut2);\n                        mutationObserver.disconnect();\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (container) {\n                focusScopesStack.add(focusScope);\n                const previouslyFocusedElement = document.activeElement;\n                const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n                if (!hasFocusedCandidate) {\n                    const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                    container.dispatchEvent(mountEvent);\n                    if (!mountEvent.defaultPrevented) {\n                        focusFirst(removeLinks(getTabbableCandidates(container)), {\n                            select: true\n                        });\n                        if (document.activeElement === previouslyFocusedElement) {\n                            focus(container);\n                        }\n                    }\n                }\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                        setTimeout({\n                            \"FocusScope.useEffect\": ()=>{\n                                const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                                container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                container.dispatchEvent(unmountEvent);\n                                if (!unmountEvent.defaultPrevented) {\n                                    focus(previouslyFocusedElement ?? document.body, {\n                                        select: true\n                                    });\n                                }\n                                container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                focusScopesStack.remove(focusScope);\n                            }\n                        }[\"FocusScope.useEffect\"], 0);\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"FocusScope.useCallback[handleKeyDown]\": (event)=>{\n            if (!loop && !trapped) return;\n            if (focusScope.paused) return;\n            const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n            const focusedElement = document.activeElement;\n            if (isTabKey && focusedElement) {\n                const container2 = event.currentTarget;\n                const [first, last] = getTabbableEdges(container2);\n                const hasTabbableElementsInside = first && last;\n                if (!hasTabbableElementsInside) {\n                    if (focusedElement === container2) event.preventDefault();\n                } else {\n                    if (!event.shiftKey && focusedElement === last) {\n                        event.preventDefault();\n                        if (loop) focus(first, {\n                            select: true\n                        });\n                    } else if (event.shiftKey && focusedElement === first) {\n                        event.preventDefault();\n                        if (loop) focus(last, {\n                            select: true\n                        });\n                    }\n                }\n            }\n        }\n    }[\"FocusScope.useCallback[handleKeyDown]\"], [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f4aa807b3e1605c991a664dd895fa0ef/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ })

};
;
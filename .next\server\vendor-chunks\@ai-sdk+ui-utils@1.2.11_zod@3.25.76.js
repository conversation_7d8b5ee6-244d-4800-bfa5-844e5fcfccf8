"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.25.76";
exports.ids = ["vendor-chunks/@ai-sdk+ui-utils@1.2.11_zod@3.25.76"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.25.76/node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.25.76/node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.8_zod@3.25.76/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(rsc)/./node_modules/.pnpm/zod-to-json-schema@3.24.6_zod@3.25.76/node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n      message.parts.push({ type: \"step-start\" });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage,\n  requestType = \"generate\"\n}) {\n  var _a, _b, _c;\n  const request = requestType === \"resume\" ? fetch2(`${api}?chatId=${body.id}`, {\n    method: \"GET\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }) : fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_b = abortController == null ? void 0 : abortController()) == null ? void 0 : _b.signal,\n    credentials\n  });\n  const response = await request.catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_c = await response.text()) != null ? _c : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (globalThis.FileList && attachmentsFromOptions instanceof globalThis.FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  if (message.role !== \"assistant\") {\n    return false;\n  }\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === \"step-start\" ? index : lastIndex;\n  }, -1);\n  const lastStepToolInvocations = message.parts.slice(lastStepStartIndex + 1).filter((part) => part.type === \"tool-invocation\");\n  return lastStepToolInvocations.length > 0 && lastStepToolInvocations.every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFpLXNkayt1aS11dGlsc0AxLjIuMTFfem9kQDMuMjUuNzYvbm9kZV9tb2R1bGVzL0BhaS1zZGsvdWktdXRpbHMvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ29EOztBQUVwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRUFBbUUsT0FBTztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsS0FBSztBQUN0RDtBQUNBLFlBQVksZ0JBQWdCLEdBQUc7QUFDL0I7QUFDQTs7QUFFQTtBQUMwRTs7QUFFMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ3VEOztBQUV2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixrQkFBa0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLFFBQVE7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxlQUFlLHFFQUFhLEdBQUcsZ0JBQWdCO0FBQy9DO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsV0FBVyxxRUFBYSxHQUFHLHlCQUF5QjtBQUNwRDtBQUNBLGFBQWE7QUFDYjtBQUNBLFdBQVc7QUFDWDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLHFDQUFxQztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRUFBbUUsT0FBTztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsS0FBSztBQUN0RDtBQUNBLFlBQVksZ0JBQWdCLEdBQUc7QUFDL0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsY0FBYztBQUMzRSxpQkFBaUIsc0JBQXNCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxnQkFBZ0I7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsOERBQWtCO0FBQzlDO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsY0FBYyxxQkFBcUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGlCQUFpQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsb0JBQW9CO0FBQy9DO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHlDQUF5Qyw4QkFBOEI7QUFDdkU7O0FBRUE7QUFDMkU7O0FBRTNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxZQUFZLGNBQWM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0Qiw4REFBbUI7QUFDL0MsQ0FBQztBQUNELHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrQkFBa0I7QUFDckM7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSDtBQUNBLGFBQWEsNERBQTREO0FBQ3pFO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLHVEQUF1RCxJQUFJLFVBQVUsUUFBUTtBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOEJBQThCO0FBQ2pEO0FBQ0EsZ0NBQWdDLHFCQUFxQjtBQUNyRDtBQUNBLFNBQVM7QUFDVDtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxnQkFBZ0I7QUFDbEU7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9EQUFvRCxnQkFBZ0I7QUFDcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHVDQUF1QztBQUMzRDtBQUNBO0FBQ0EsNEJBQTRCLHFDQUFxQztBQUNqRTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaUJBQWlCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxjQUFjO0FBQzNFLGlCQUFpQixzQkFBc0I7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELGdCQUFnQjtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ3lEOztBQUV6RDtBQUNpRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksOERBQWU7QUFDbkI7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxvQ0FBb0MsSUFBSTtBQUMxRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSyxtRUFBZTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUF5QkU7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZW5kb2Fyc2FuZGlcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcY2hlc3NsbG1cXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBhaS1zZGsrdWktdXRpbHNAMS4yLjExX3pvZEAzLjI1Ljc2XFxub2RlX21vZHVsZXNcXEBhaS1zZGtcXHVpLXV0aWxzXFxkaXN0XFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2luZGV4LnRzXG5pbXBvcnQgeyBnZW5lcmF0ZUlkIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXItdXRpbHNcIjtcblxuLy8gc3JjL2Fzc2lzdGFudC1zdHJlYW0tcGFydHMudHNcbnZhciB0ZXh0U3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCIwXCIsXG4gIG5hbWU6IFwidGV4dFwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdcInRleHRcIiBwYXJ0cyBleHBlY3QgYSBzdHJpbmcgdmFsdWUuJyk7XG4gICAgfVxuICAgIHJldHVybiB7IHR5cGU6IFwidGV4dFwiLCB2YWx1ZSB9O1xuICB9XG59O1xudmFyIGVycm9yU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCIzXCIsXG4gIG5hbWU6IFwiZXJyb3JcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignXCJlcnJvclwiIHBhcnRzIGV4cGVjdCBhIHN0cmluZyB2YWx1ZS4nKTtcbiAgICB9XG4gICAgcmV0dXJuIHsgdHlwZTogXCJlcnJvclwiLCB2YWx1ZSB9O1xuICB9XG59O1xudmFyIGFzc2lzdGFudE1lc3NhZ2VTdHJlYW1QYXJ0ID0ge1xuICBjb2RlOiBcIjRcIixcbiAgbmFtZTogXCJhc3Npc3RhbnRfbWVzc2FnZVwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlICE9PSBcIm9iamVjdFwiIHx8ICEoXCJpZFwiIGluIHZhbHVlKSB8fCAhKFwicm9sZVwiIGluIHZhbHVlKSB8fCAhKFwiY29udGVudFwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUuaWQgIT09IFwic3RyaW5nXCIgfHwgdHlwZW9mIHZhbHVlLnJvbGUgIT09IFwic3RyaW5nXCIgfHwgdmFsdWUucm9sZSAhPT0gXCJhc3Npc3RhbnRcIiB8fCAhQXJyYXkuaXNBcnJheSh2YWx1ZS5jb250ZW50KSB8fCAhdmFsdWUuY29udGVudC5ldmVyeShcbiAgICAgIChpdGVtKSA9PiBpdGVtICE9IG51bGwgJiYgdHlwZW9mIGl0ZW0gPT09IFwib2JqZWN0XCIgJiYgXCJ0eXBlXCIgaW4gaXRlbSAmJiBpdGVtLnR5cGUgPT09IFwidGV4dFwiICYmIFwidGV4dFwiIGluIGl0ZW0gJiYgaXRlbS50ZXh0ICE9IG51bGwgJiYgdHlwZW9mIGl0ZW0udGV4dCA9PT0gXCJvYmplY3RcIiAmJiBcInZhbHVlXCIgaW4gaXRlbS50ZXh0ICYmIHR5cGVvZiBpdGVtLnRleHQudmFsdWUgPT09IFwic3RyaW5nXCJcbiAgICApKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdcImFzc2lzdGFudF9tZXNzYWdlXCIgcGFydHMgZXhwZWN0IGFuIG9iamVjdCB3aXRoIGFuIFwiaWRcIiwgXCJyb2xlXCIsIGFuZCBcImNvbnRlbnRcIiBwcm9wZXJ0eS4nXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogXCJhc3Npc3RhbnRfbWVzc2FnZVwiLFxuICAgICAgdmFsdWVcbiAgICB9O1xuICB9XG59O1xudmFyIGFzc2lzdGFudENvbnRyb2xEYXRhU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCI1XCIsXG4gIG5hbWU6IFwiYXNzaXN0YW50X2NvbnRyb2xfZGF0YVwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlICE9PSBcIm9iamVjdFwiIHx8ICEoXCJ0aHJlYWRJZFwiIGluIHZhbHVlKSB8fCAhKFwibWVzc2FnZUlkXCIgaW4gdmFsdWUpIHx8IHR5cGVvZiB2YWx1ZS50aHJlYWRJZCAhPT0gXCJzdHJpbmdcIiB8fCB0eXBlb2YgdmFsdWUubWVzc2FnZUlkICE9PSBcInN0cmluZ1wiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdcImFzc2lzdGFudF9jb250cm9sX2RhdGFcIiBwYXJ0cyBleHBlY3QgYW4gb2JqZWN0IHdpdGggYSBcInRocmVhZElkXCIgYW5kIFwibWVzc2FnZUlkXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwiYXNzaXN0YW50X2NvbnRyb2xfZGF0YVwiLFxuICAgICAgdmFsdWU6IHtcbiAgICAgICAgdGhyZWFkSWQ6IHZhbHVlLnRocmVhZElkLFxuICAgICAgICBtZXNzYWdlSWQ6IHZhbHVlLm1lc3NhZ2VJZFxuICAgICAgfVxuICAgIH07XG4gIH1cbn07XG52YXIgZGF0YU1lc3NhZ2VTdHJlYW1QYXJ0ID0ge1xuICBjb2RlOiBcIjZcIixcbiAgbmFtZTogXCJkYXRhX21lc3NhZ2VcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsIHx8IHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIiB8fCAhKFwicm9sZVwiIGluIHZhbHVlKSB8fCAhKFwiZGF0YVwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUucm9sZSAhPT0gXCJzdHJpbmdcIiB8fCB2YWx1ZS5yb2xlICE9PSBcImRhdGFcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnXCJkYXRhX21lc3NhZ2VcIiBwYXJ0cyBleHBlY3QgYW4gb2JqZWN0IHdpdGggYSBcInJvbGVcIiBhbmQgXCJkYXRhXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwiZGF0YV9tZXNzYWdlXCIsXG4gICAgICB2YWx1ZVxuICAgIH07XG4gIH1cbn07XG52YXIgYXNzaXN0YW50U3RyZWFtUGFydHMgPSBbXG4gIHRleHRTdHJlYW1QYXJ0LFxuICBlcnJvclN0cmVhbVBhcnQsXG4gIGFzc2lzdGFudE1lc3NhZ2VTdHJlYW1QYXJ0LFxuICBhc3Npc3RhbnRDb250cm9sRGF0YVN0cmVhbVBhcnQsXG4gIGRhdGFNZXNzYWdlU3RyZWFtUGFydFxuXTtcbnZhciBhc3Npc3RhbnRTdHJlYW1QYXJ0c0J5Q29kZSA9IHtcbiAgW3RleHRTdHJlYW1QYXJ0LmNvZGVdOiB0ZXh0U3RyZWFtUGFydCxcbiAgW2Vycm9yU3RyZWFtUGFydC5jb2RlXTogZXJyb3JTdHJlYW1QYXJ0LFxuICBbYXNzaXN0YW50TWVzc2FnZVN0cmVhbVBhcnQuY29kZV06IGFzc2lzdGFudE1lc3NhZ2VTdHJlYW1QYXJ0LFxuICBbYXNzaXN0YW50Q29udHJvbERhdGFTdHJlYW1QYXJ0LmNvZGVdOiBhc3Npc3RhbnRDb250cm9sRGF0YVN0cmVhbVBhcnQsXG4gIFtkYXRhTWVzc2FnZVN0cmVhbVBhcnQuY29kZV06IGRhdGFNZXNzYWdlU3RyZWFtUGFydFxufTtcbnZhciBTdHJlYW1TdHJpbmdQcmVmaXhlcyA9IHtcbiAgW3RleHRTdHJlYW1QYXJ0Lm5hbWVdOiB0ZXh0U3RyZWFtUGFydC5jb2RlLFxuICBbZXJyb3JTdHJlYW1QYXJ0Lm5hbWVdOiBlcnJvclN0cmVhbVBhcnQuY29kZSxcbiAgW2Fzc2lzdGFudE1lc3NhZ2VTdHJlYW1QYXJ0Lm5hbWVdOiBhc3Npc3RhbnRNZXNzYWdlU3RyZWFtUGFydC5jb2RlLFxuICBbYXNzaXN0YW50Q29udHJvbERhdGFTdHJlYW1QYXJ0Lm5hbWVdOiBhc3Npc3RhbnRDb250cm9sRGF0YVN0cmVhbVBhcnQuY29kZSxcbiAgW2RhdGFNZXNzYWdlU3RyZWFtUGFydC5uYW1lXTogZGF0YU1lc3NhZ2VTdHJlYW1QYXJ0LmNvZGVcbn07XG52YXIgdmFsaWRDb2RlcyA9IGFzc2lzdGFudFN0cmVhbVBhcnRzLm1hcCgocGFydCkgPT4gcGFydC5jb2RlKTtcbnZhciBwYXJzZUFzc2lzdGFudFN0cmVhbVBhcnQgPSAobGluZSkgPT4ge1xuICBjb25zdCBmaXJzdFNlcGFyYXRvckluZGV4ID0gbGluZS5pbmRleE9mKFwiOlwiKTtcbiAgaWYgKGZpcnN0U2VwYXJhdG9ySW5kZXggPT09IC0xKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIHBhcnNlIHN0cmVhbSBzdHJpbmcuIE5vIHNlcGFyYXRvciBmb3VuZC5cIik7XG4gIH1cbiAgY29uc3QgcHJlZml4ID0gbGluZS5zbGljZSgwLCBmaXJzdFNlcGFyYXRvckluZGV4KTtcbiAgaWYgKCF2YWxpZENvZGVzLmluY2x1ZGVzKHByZWZpeCkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBwYXJzZSBzdHJlYW0gc3RyaW5nLiBJbnZhbGlkIGNvZGUgJHtwcmVmaXh9LmApO1xuICB9XG4gIGNvbnN0IGNvZGUgPSBwcmVmaXg7XG4gIGNvbnN0IHRleHRWYWx1ZSA9IGxpbmUuc2xpY2UoZmlyc3RTZXBhcmF0b3JJbmRleCArIDEpO1xuICBjb25zdCBqc29uVmFsdWUgPSBKU09OLnBhcnNlKHRleHRWYWx1ZSk7XG4gIHJldHVybiBhc3Npc3RhbnRTdHJlYW1QYXJ0c0J5Q29kZVtjb2RlXS5wYXJzZShqc29uVmFsdWUpO1xufTtcbmZ1bmN0aW9uIGZvcm1hdEFzc2lzdGFudFN0cmVhbVBhcnQodHlwZSwgdmFsdWUpIHtcbiAgY29uc3Qgc3RyZWFtUGFydCA9IGFzc2lzdGFudFN0cmVhbVBhcnRzLmZpbmQoKHBhcnQpID0+IHBhcnQubmFtZSA9PT0gdHlwZSk7XG4gIGlmICghc3RyZWFtUGFydCkge1xuICAgIHRocm93IG5ldyBFcnJvcihgSW52YWxpZCBzdHJlYW0gcGFydCB0eXBlOiAke3R5cGV9YCk7XG4gIH1cbiAgcmV0dXJuIGAke3N0cmVhbVBhcnQuY29kZX06JHtKU09OLnN0cmluZ2lmeSh2YWx1ZSl9XG5gO1xufVxuXG4vLyBzcmMvcHJvY2Vzcy1jaGF0LXJlc3BvbnNlLnRzXG5pbXBvcnQgeyBnZW5lcmF0ZUlkIGFzIGdlbmVyYXRlSWRGdW5jdGlvbiB9IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyLXV0aWxzXCI7XG5cbi8vIHNyYy9kdXBsaWNhdGVkL3VzYWdlLnRzXG5mdW5jdGlvbiBjYWxjdWxhdGVMYW5ndWFnZU1vZGVsVXNhZ2Uoe1xuICBwcm9tcHRUb2tlbnMsXG4gIGNvbXBsZXRpb25Ub2tlbnNcbn0pIHtcbiAgcmV0dXJuIHtcbiAgICBwcm9tcHRUb2tlbnMsXG4gICAgY29tcGxldGlvblRva2VucyxcbiAgICB0b3RhbFRva2VuczogcHJvbXB0VG9rZW5zICsgY29tcGxldGlvblRva2Vuc1xuICB9O1xufVxuXG4vLyBzcmMvcGFyc2UtcGFydGlhbC1qc29uLnRzXG5pbXBvcnQgeyBzYWZlUGFyc2VKU09OIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXItdXRpbHNcIjtcblxuLy8gc3JjL2ZpeC1qc29uLnRzXG5mdW5jdGlvbiBmaXhKc29uKGlucHV0KSB7XG4gIGNvbnN0IHN0YWNrID0gW1wiUk9PVFwiXTtcbiAgbGV0IGxhc3RWYWxpZEluZGV4ID0gLTE7XG4gIGxldCBsaXRlcmFsU3RhcnQgPSBudWxsO1xuICBmdW5jdGlvbiBwcm9jZXNzVmFsdWVTdGFydChjaGFyLCBpLCBzd2FwU3RhdGUpIHtcbiAgICB7XG4gICAgICBzd2l0Y2ggKGNoYXIpIHtcbiAgICAgICAgY2FzZSAnXCInOiB7XG4gICAgICAgICAgbGFzdFZhbGlkSW5kZXggPSBpO1xuICAgICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICAgIHN0YWNrLnB1c2goc3dhcFN0YXRlKTtcbiAgICAgICAgICBzdGFjay5wdXNoKFwiSU5TSURFX1NUUklOR1wiKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlIFwiZlwiOlxuICAgICAgICBjYXNlIFwidFwiOlxuICAgICAgICBjYXNlIFwiblwiOiB7XG4gICAgICAgICAgbGFzdFZhbGlkSW5kZXggPSBpO1xuICAgICAgICAgIGxpdGVyYWxTdGFydCA9IGk7XG4gICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgc3RhY2sucHVzaChzd2FwU3RhdGUpO1xuICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfTElURVJBTFwiKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlIFwiLVwiOiB7XG4gICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgc3RhY2sucHVzaChzd2FwU3RhdGUpO1xuICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfTlVNQkVSXCIpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgXCIwXCI6XG4gICAgICAgIGNhc2UgXCIxXCI6XG4gICAgICAgIGNhc2UgXCIyXCI6XG4gICAgICAgIGNhc2UgXCIzXCI6XG4gICAgICAgIGNhc2UgXCI0XCI6XG4gICAgICAgIGNhc2UgXCI1XCI6XG4gICAgICAgIGNhc2UgXCI2XCI6XG4gICAgICAgIGNhc2UgXCI3XCI6XG4gICAgICAgIGNhc2UgXCI4XCI6XG4gICAgICAgIGNhc2UgXCI5XCI6IHtcbiAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgc3RhY2sucHVzaChzd2FwU3RhdGUpO1xuICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfTlVNQkVSXCIpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgXCJ7XCI6IHtcbiAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgc3RhY2sucHVzaChzd2FwU3RhdGUpO1xuICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfT0JKRUNUX1NUQVJUXCIpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgXCJbXCI6IHtcbiAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgc3RhY2sucHVzaChzd2FwU3RhdGUpO1xuICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfQVJSQVlfU1RBUlRcIik7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gcHJvY2Vzc0FmdGVyT2JqZWN0VmFsdWUoY2hhciwgaSkge1xuICAgIHN3aXRjaCAoY2hhcikge1xuICAgICAgY2FzZSBcIixcIjoge1xuICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgc3RhY2sucHVzaChcIklOU0lERV9PQkpFQ1RfQUZURVJfQ09NTUFcIik7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSBcIn1cIjoge1xuICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gcHJvY2Vzc0FmdGVyQXJyYXlWYWx1ZShjaGFyLCBpKSB7XG4gICAgc3dpdGNoIChjaGFyKSB7XG4gICAgICBjYXNlIFwiLFwiOiB7XG4gICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICBzdGFjay5wdXNoKFwiSU5TSURFX0FSUkFZX0FGVEVSX0NPTU1BXCIpO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJdXCI6IHtcbiAgICAgICAgbGFzdFZhbGlkSW5kZXggPSBpO1xuICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgaW5wdXQubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBjaGFyID0gaW5wdXRbaV07XG4gICAgY29uc3QgY3VycmVudFN0YXRlID0gc3RhY2tbc3RhY2subGVuZ3RoIC0gMV07XG4gICAgc3dpdGNoIChjdXJyZW50U3RhdGUpIHtcbiAgICAgIGNhc2UgXCJST09UXCI6XG4gICAgICAgIHByb2Nlc3NWYWx1ZVN0YXJ0KGNoYXIsIGksIFwiRklOSVNIXCIpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgXCJJTlNJREVfT0JKRUNUX1NUQVJUXCI6IHtcbiAgICAgICAgc3dpdGNoIChjaGFyKSB7XG4gICAgICAgICAgY2FzZSAnXCInOiB7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfT0JKRUNUX0tFWVwiKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjYXNlIFwifVwiOiB7XG4gICAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfT0JKRUNUX0FGVEVSX0NPTU1BXCI6IHtcbiAgICAgICAgc3dpdGNoIChjaGFyKSB7XG4gICAgICAgICAgY2FzZSAnXCInOiB7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfT0JKRUNUX0tFWVwiKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfT0JKRUNUX0tFWVwiOiB7XG4gICAgICAgIHN3aXRjaCAoY2hhcikge1xuICAgICAgICAgIGNhc2UgJ1wiJzoge1xuICAgICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgICBzdGFjay5wdXNoKFwiSU5TSURFX09CSkVDVF9BRlRFUl9LRVlcIik7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBjYXNlIFwiSU5TSURFX09CSkVDVF9BRlRFUl9LRVlcIjoge1xuICAgICAgICBzd2l0Y2ggKGNoYXIpIHtcbiAgICAgICAgICBjYXNlIFwiOlwiOiB7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfT0JKRUNUX0JFRk9SRV9WQUxVRVwiKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfT0JKRUNUX0JFRk9SRV9WQUxVRVwiOiB7XG4gICAgICAgIHByb2Nlc3NWYWx1ZVN0YXJ0KGNoYXIsIGksIFwiSU5TSURFX09CSkVDVF9BRlRFUl9WQUxVRVwiKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBjYXNlIFwiSU5TSURFX09CSkVDVF9BRlRFUl9WQUxVRVwiOiB7XG4gICAgICAgIHByb2Nlc3NBZnRlck9iamVjdFZhbHVlKGNoYXIsIGkpO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfU1RSSU5HXCI6IHtcbiAgICAgICAgc3dpdGNoIChjaGFyKSB7XG4gICAgICAgICAgY2FzZSAnXCInOiB7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIGxhc3RWYWxpZEluZGV4ID0gaTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjYXNlIFwiXFxcXFwiOiB7XG4gICAgICAgICAgICBzdGFjay5wdXNoKFwiSU5TSURFX1NUUklOR19FU0NBUEVcIik7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgbGFzdFZhbGlkSW5kZXggPSBpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfQVJSQVlfU1RBUlRcIjoge1xuICAgICAgICBzd2l0Y2ggKGNoYXIpIHtcbiAgICAgICAgICBjYXNlIFwiXVwiOiB7XG4gICAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgICBwcm9jZXNzVmFsdWVTdGFydChjaGFyLCBpLCBcIklOU0lERV9BUlJBWV9BRlRFUl9WQUxVRVwiKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfQVJSQVlfQUZURVJfVkFMVUVcIjoge1xuICAgICAgICBzd2l0Y2ggKGNoYXIpIHtcbiAgICAgICAgICBjYXNlIFwiLFwiOiB7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIHN0YWNrLnB1c2goXCJJTlNJREVfQVJSQVlfQUZURVJfQ09NTUFcIik7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgICAgY2FzZSBcIl1cIjoge1xuICAgICAgICAgICAgbGFzdFZhbGlkSW5kZXggPSBpO1xuICAgICAgICAgICAgc3RhY2sucG9wKCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgbGFzdFZhbGlkSW5kZXggPSBpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSBcIklOU0lERV9BUlJBWV9BRlRFUl9DT01NQVwiOiB7XG4gICAgICAgIHByb2Nlc3NWYWx1ZVN0YXJ0KGNoYXIsIGksIFwiSU5TSURFX0FSUkFZX0FGVEVSX1ZBTFVFXCIpO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfU1RSSU5HX0VTQ0FQRVwiOiB7XG4gICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSBcIklOU0lERV9OVU1CRVJcIjoge1xuICAgICAgICBzd2l0Y2ggKGNoYXIpIHtcbiAgICAgICAgICBjYXNlIFwiMFwiOlxuICAgICAgICAgIGNhc2UgXCIxXCI6XG4gICAgICAgICAgY2FzZSBcIjJcIjpcbiAgICAgICAgICBjYXNlIFwiM1wiOlxuICAgICAgICAgIGNhc2UgXCI0XCI6XG4gICAgICAgICAgY2FzZSBcIjVcIjpcbiAgICAgICAgICBjYXNlIFwiNlwiOlxuICAgICAgICAgIGNhc2UgXCI3XCI6XG4gICAgICAgICAgY2FzZSBcIjhcIjpcbiAgICAgICAgICBjYXNlIFwiOVwiOiB7XG4gICAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICAgICAgY2FzZSBcImVcIjpcbiAgICAgICAgICBjYXNlIFwiRVwiOlxuICAgICAgICAgIGNhc2UgXCItXCI6XG4gICAgICAgICAgY2FzZSBcIi5cIjoge1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIGNhc2UgXCIsXCI6IHtcbiAgICAgICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICAgICAgaWYgKHN0YWNrW3N0YWNrLmxlbmd0aCAtIDFdID09PSBcIklOU0lERV9BUlJBWV9BRlRFUl9WQUxVRVwiKSB7XG4gICAgICAgICAgICAgIHByb2Nlc3NBZnRlckFycmF5VmFsdWUoY2hhciwgaSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoc3RhY2tbc3RhY2subGVuZ3RoIC0gMV0gPT09IFwiSU5TSURFX09CSkVDVF9BRlRFUl9WQUxVRVwiKSB7XG4gICAgICAgICAgICAgIHByb2Nlc3NBZnRlck9iamVjdFZhbHVlKGNoYXIsIGkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIGNhc2UgXCJ9XCI6IHtcbiAgICAgICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICAgICAgaWYgKHN0YWNrW3N0YWNrLmxlbmd0aCAtIDFdID09PSBcIklOU0lERV9PQkpFQ1RfQUZURVJfVkFMVUVcIikge1xuICAgICAgICAgICAgICBwcm9jZXNzQWZ0ZXJPYmplY3RWYWx1ZShjaGFyLCBpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjYXNlIFwiXVwiOiB7XG4gICAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICAgIGlmIChzdGFja1tzdGFjay5sZW5ndGggLSAxXSA9PT0gXCJJTlNJREVfQVJSQVlfQUZURVJfVkFMVUVcIikge1xuICAgICAgICAgICAgICBwcm9jZXNzQWZ0ZXJBcnJheVZhbHVlKGNoYXIsIGkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAgIHN0YWNrLnBvcCgpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSBcIklOU0lERV9MSVRFUkFMXCI6IHtcbiAgICAgICAgY29uc3QgcGFydGlhbExpdGVyYWwgPSBpbnB1dC5zdWJzdHJpbmcobGl0ZXJhbFN0YXJ0LCBpICsgMSk7XG4gICAgICAgIGlmICghXCJmYWxzZVwiLnN0YXJ0c1dpdGgocGFydGlhbExpdGVyYWwpICYmICFcInRydWVcIi5zdGFydHNXaXRoKHBhcnRpYWxMaXRlcmFsKSAmJiAhXCJudWxsXCIuc3RhcnRzV2l0aChwYXJ0aWFsTGl0ZXJhbCkpIHtcbiAgICAgICAgICBzdGFjay5wb3AoKTtcbiAgICAgICAgICBpZiAoc3RhY2tbc3RhY2subGVuZ3RoIC0gMV0gPT09IFwiSU5TSURFX09CSkVDVF9BRlRFUl9WQUxVRVwiKSB7XG4gICAgICAgICAgICBwcm9jZXNzQWZ0ZXJPYmplY3RWYWx1ZShjaGFyLCBpKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKHN0YWNrW3N0YWNrLmxlbmd0aCAtIDFdID09PSBcIklOU0lERV9BUlJBWV9BRlRFUl9WQUxVRVwiKSB7XG4gICAgICAgICAgICBwcm9jZXNzQWZ0ZXJBcnJheVZhbHVlKGNoYXIsIGkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBsYXN0VmFsaWRJbmRleCA9IGk7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGxldCByZXN1bHQgPSBpbnB1dC5zbGljZSgwLCBsYXN0VmFsaWRJbmRleCArIDEpO1xuICBmb3IgKGxldCBpID0gc3RhY2subGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHtcbiAgICBjb25zdCBzdGF0ZSA9IHN0YWNrW2ldO1xuICAgIHN3aXRjaCAoc3RhdGUpIHtcbiAgICAgIGNhc2UgXCJJTlNJREVfU1RSSU5HXCI6IHtcbiAgICAgICAgcmVzdWx0ICs9ICdcIic7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgY2FzZSBcIklOU0lERV9PQkpFQ1RfS0VZXCI6XG4gICAgICBjYXNlIFwiSU5TSURFX09CSkVDVF9BRlRFUl9LRVlcIjpcbiAgICAgIGNhc2UgXCJJTlNJREVfT0JKRUNUX0FGVEVSX0NPTU1BXCI6XG4gICAgICBjYXNlIFwiSU5TSURFX09CSkVDVF9TVEFSVFwiOlxuICAgICAgY2FzZSBcIklOU0lERV9PQkpFQ1RfQkVGT1JFX1ZBTFVFXCI6XG4gICAgICBjYXNlIFwiSU5TSURFX09CSkVDVF9BRlRFUl9WQUxVRVwiOiB7XG4gICAgICAgIHJlc3VsdCArPSBcIn1cIjtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBjYXNlIFwiSU5TSURFX0FSUkFZX1NUQVJUXCI6XG4gICAgICBjYXNlIFwiSU5TSURFX0FSUkFZX0FGVEVSX0NPTU1BXCI6XG4gICAgICBjYXNlIFwiSU5TSURFX0FSUkFZX0FGVEVSX1ZBTFVFXCI6IHtcbiAgICAgICAgcmVzdWx0ICs9IFwiXVwiO1xuICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICAgIGNhc2UgXCJJTlNJREVfTElURVJBTFwiOiB7XG4gICAgICAgIGNvbnN0IHBhcnRpYWxMaXRlcmFsID0gaW5wdXQuc3Vic3RyaW5nKGxpdGVyYWxTdGFydCwgaW5wdXQubGVuZ3RoKTtcbiAgICAgICAgaWYgKFwidHJ1ZVwiLnN0YXJ0c1dpdGgocGFydGlhbExpdGVyYWwpKSB7XG4gICAgICAgICAgcmVzdWx0ICs9IFwidHJ1ZVwiLnNsaWNlKHBhcnRpYWxMaXRlcmFsLmxlbmd0aCk7XG4gICAgICAgIH0gZWxzZSBpZiAoXCJmYWxzZVwiLnN0YXJ0c1dpdGgocGFydGlhbExpdGVyYWwpKSB7XG4gICAgICAgICAgcmVzdWx0ICs9IFwiZmFsc2VcIi5zbGljZShwYXJ0aWFsTGl0ZXJhbC5sZW5ndGgpO1xuICAgICAgICB9IGVsc2UgaWYgKFwibnVsbFwiLnN0YXJ0c1dpdGgocGFydGlhbExpdGVyYWwpKSB7XG4gICAgICAgICAgcmVzdWx0ICs9IFwibnVsbFwiLnNsaWNlKHBhcnRpYWxMaXRlcmFsLmxlbmd0aCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHJlc3VsdDtcbn1cblxuLy8gc3JjL3BhcnNlLXBhcnRpYWwtanNvbi50c1xuZnVuY3Rpb24gcGFyc2VQYXJ0aWFsSnNvbihqc29uVGV4dCkge1xuICBpZiAoanNvblRleHQgPT09IHZvaWQgMCkge1xuICAgIHJldHVybiB7IHZhbHVlOiB2b2lkIDAsIHN0YXRlOiBcInVuZGVmaW5lZC1pbnB1dFwiIH07XG4gIH1cbiAgbGV0IHJlc3VsdCA9IHNhZmVQYXJzZUpTT04oeyB0ZXh0OiBqc29uVGV4dCB9KTtcbiAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgcmV0dXJuIHsgdmFsdWU6IHJlc3VsdC52YWx1ZSwgc3RhdGU6IFwic3VjY2Vzc2Z1bC1wYXJzZVwiIH07XG4gIH1cbiAgcmVzdWx0ID0gc2FmZVBhcnNlSlNPTih7IHRleHQ6IGZpeEpzb24oanNvblRleHQpIH0pO1xuICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICByZXR1cm4geyB2YWx1ZTogcmVzdWx0LnZhbHVlLCBzdGF0ZTogXCJyZXBhaXJlZC1wYXJzZVwiIH07XG4gIH1cbiAgcmV0dXJuIHsgdmFsdWU6IHZvaWQgMCwgc3RhdGU6IFwiZmFpbGVkLXBhcnNlXCIgfTtcbn1cblxuLy8gc3JjL2RhdGEtc3RyZWFtLXBhcnRzLnRzXG52YXIgdGV4dFN0cmVhbVBhcnQyID0ge1xuICBjb2RlOiBcIjBcIixcbiAgbmFtZTogXCJ0ZXh0XCIsXG4gIHBhcnNlOiAodmFsdWUpID0+IHtcbiAgICBpZiAodHlwZW9mIHZhbHVlICE9PSBcInN0cmluZ1wiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1widGV4dFwiIHBhcnRzIGV4cGVjdCBhIHN0cmluZyB2YWx1ZS4nKTtcbiAgICB9XG4gICAgcmV0dXJuIHsgdHlwZTogXCJ0ZXh0XCIsIHZhbHVlIH07XG4gIH1cbn07XG52YXIgZGF0YVN0cmVhbVBhcnQgPSB7XG4gIGNvZGU6IFwiMlwiLFxuICBuYW1lOiBcImRhdGFcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICghQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignXCJkYXRhXCIgcGFydHMgZXhwZWN0IGFuIGFycmF5IHZhbHVlLicpO1xuICAgIH1cbiAgICByZXR1cm4geyB0eXBlOiBcImRhdGFcIiwgdmFsdWUgfTtcbiAgfVxufTtcbnZhciBlcnJvclN0cmVhbVBhcnQyID0ge1xuICBjb2RlOiBcIjNcIixcbiAgbmFtZTogXCJlcnJvclwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdcImVycm9yXCIgcGFydHMgZXhwZWN0IGEgc3RyaW5nIHZhbHVlLicpO1xuICAgIH1cbiAgICByZXR1cm4geyB0eXBlOiBcImVycm9yXCIsIHZhbHVlIH07XG4gIH1cbn07XG52YXIgbWVzc2FnZUFubm90YXRpb25zU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCI4XCIsXG4gIG5hbWU6IFwibWVzc2FnZV9hbm5vdGF0aW9uc1wiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdcIm1lc3NhZ2VfYW5ub3RhdGlvbnNcIiBwYXJ0cyBleHBlY3QgYW4gYXJyYXkgdmFsdWUuJyk7XG4gICAgfVxuICAgIHJldHVybiB7IHR5cGU6IFwibWVzc2FnZV9hbm5vdGF0aW9uc1wiLCB2YWx1ZSB9O1xuICB9XG59O1xudmFyIHRvb2xDYWxsU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCI5XCIsXG4gIG5hbWU6IFwidG9vbF9jYWxsXCIsXG4gIHBhcnNlOiAodmFsdWUpID0+IHtcbiAgICBpZiAodmFsdWUgPT0gbnVsbCB8fCB0eXBlb2YgdmFsdWUgIT09IFwib2JqZWN0XCIgfHwgIShcInRvb2xDYWxsSWRcIiBpbiB2YWx1ZSkgfHwgdHlwZW9mIHZhbHVlLnRvb2xDYWxsSWQgIT09IFwic3RyaW5nXCIgfHwgIShcInRvb2xOYW1lXCIgaW4gdmFsdWUpIHx8IHR5cGVvZiB2YWx1ZS50b29sTmFtZSAhPT0gXCJzdHJpbmdcIiB8fCAhKFwiYXJnc1wiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUuYXJncyAhPT0gXCJvYmplY3RcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnXCJ0b29sX2NhbGxcIiBwYXJ0cyBleHBlY3QgYW4gb2JqZWN0IHdpdGggYSBcInRvb2xDYWxsSWRcIiwgXCJ0b29sTmFtZVwiLCBhbmQgXCJhcmdzXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwidG9vbF9jYWxsXCIsXG4gICAgICB2YWx1ZVxuICAgIH07XG4gIH1cbn07XG52YXIgdG9vbFJlc3VsdFN0cmVhbVBhcnQgPSB7XG4gIGNvZGU6IFwiYVwiLFxuICBuYW1lOiBcInRvb2xfcmVzdWx0XCIsXG4gIHBhcnNlOiAodmFsdWUpID0+IHtcbiAgICBpZiAodmFsdWUgPT0gbnVsbCB8fCB0eXBlb2YgdmFsdWUgIT09IFwib2JqZWN0XCIgfHwgIShcInRvb2xDYWxsSWRcIiBpbiB2YWx1ZSkgfHwgdHlwZW9mIHZhbHVlLnRvb2xDYWxsSWQgIT09IFwic3RyaW5nXCIgfHwgIShcInJlc3VsdFwiIGluIHZhbHVlKSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnXCJ0b29sX3Jlc3VsdFwiIHBhcnRzIGV4cGVjdCBhbiBvYmplY3Qgd2l0aCBhIFwidG9vbENhbGxJZFwiIGFuZCBhIFwicmVzdWx0XCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwidG9vbF9yZXN1bHRcIixcbiAgICAgIHZhbHVlXG4gICAgfTtcbiAgfVxufTtcbnZhciB0b29sQ2FsbFN0cmVhbWluZ1N0YXJ0U3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCJiXCIsXG4gIG5hbWU6IFwidG9vbF9jYWxsX3N0cmVhbWluZ19zdGFydFwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlICE9PSBcIm9iamVjdFwiIHx8ICEoXCJ0b29sQ2FsbElkXCIgaW4gdmFsdWUpIHx8IHR5cGVvZiB2YWx1ZS50b29sQ2FsbElkICE9PSBcInN0cmluZ1wiIHx8ICEoXCJ0b29sTmFtZVwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUudG9vbE5hbWUgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ1widG9vbF9jYWxsX3N0cmVhbWluZ19zdGFydFwiIHBhcnRzIGV4cGVjdCBhbiBvYmplY3Qgd2l0aCBhIFwidG9vbENhbGxJZFwiIGFuZCBcInRvb2xOYW1lXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwidG9vbF9jYWxsX3N0cmVhbWluZ19zdGFydFwiLFxuICAgICAgdmFsdWVcbiAgICB9O1xuICB9XG59O1xudmFyIHRvb2xDYWxsRGVsdGFTdHJlYW1QYXJ0ID0ge1xuICBjb2RlOiBcImNcIixcbiAgbmFtZTogXCJ0b29sX2NhbGxfZGVsdGFcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsIHx8IHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIiB8fCAhKFwidG9vbENhbGxJZFwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUudG9vbENhbGxJZCAhPT0gXCJzdHJpbmdcIiB8fCAhKFwiYXJnc1RleHREZWx0YVwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUuYXJnc1RleHREZWx0YSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnXCJ0b29sX2NhbGxfZGVsdGFcIiBwYXJ0cyBleHBlY3QgYW4gb2JqZWN0IHdpdGggYSBcInRvb2xDYWxsSWRcIiBhbmQgXCJhcmdzVGV4dERlbHRhXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwidG9vbF9jYWxsX2RlbHRhXCIsXG4gICAgICB2YWx1ZVxuICAgIH07XG4gIH1cbn07XG52YXIgZmluaXNoTWVzc2FnZVN0cmVhbVBhcnQgPSB7XG4gIGNvZGU6IFwiZFwiLFxuICBuYW1lOiBcImZpbmlzaF9tZXNzYWdlXCIsXG4gIHBhcnNlOiAodmFsdWUpID0+IHtcbiAgICBpZiAodmFsdWUgPT0gbnVsbCB8fCB0eXBlb2YgdmFsdWUgIT09IFwib2JqZWN0XCIgfHwgIShcImZpbmlzaFJlYXNvblwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUuZmluaXNoUmVhc29uICE9PSBcInN0cmluZ1wiKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdcImZpbmlzaF9tZXNzYWdlXCIgcGFydHMgZXhwZWN0IGFuIG9iamVjdCB3aXRoIGEgXCJmaW5pc2hSZWFzb25cIiBwcm9wZXJ0eS4nXG4gICAgICApO1xuICAgIH1cbiAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICBmaW5pc2hSZWFzb246IHZhbHVlLmZpbmlzaFJlYXNvblxuICAgIH07XG4gICAgaWYgKFwidXNhZ2VcIiBpbiB2YWx1ZSAmJiB2YWx1ZS51c2FnZSAhPSBudWxsICYmIHR5cGVvZiB2YWx1ZS51c2FnZSA9PT0gXCJvYmplY3RcIiAmJiBcInByb21wdFRva2Vuc1wiIGluIHZhbHVlLnVzYWdlICYmIFwiY29tcGxldGlvblRva2Vuc1wiIGluIHZhbHVlLnVzYWdlKSB7XG4gICAgICByZXN1bHQudXNhZ2UgPSB7XG4gICAgICAgIHByb21wdFRva2VuczogdHlwZW9mIHZhbHVlLnVzYWdlLnByb21wdFRva2VucyA9PT0gXCJudW1iZXJcIiA/IHZhbHVlLnVzYWdlLnByb21wdFRva2VucyA6IE51bWJlci5OYU4sXG4gICAgICAgIGNvbXBsZXRpb25Ub2tlbnM6IHR5cGVvZiB2YWx1ZS51c2FnZS5jb21wbGV0aW9uVG9rZW5zID09PSBcIm51bWJlclwiID8gdmFsdWUudXNhZ2UuY29tcGxldGlvblRva2VucyA6IE51bWJlci5OYU5cbiAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICB0eXBlOiBcImZpbmlzaF9tZXNzYWdlXCIsXG4gICAgICB2YWx1ZTogcmVzdWx0XG4gICAgfTtcbiAgfVxufTtcbnZhciBmaW5pc2hTdGVwU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCJlXCIsXG4gIG5hbWU6IFwiZmluaXNoX3N0ZXBcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsIHx8IHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIiB8fCAhKFwiZmluaXNoUmVhc29uXCIgaW4gdmFsdWUpIHx8IHR5cGVvZiB2YWx1ZS5maW5pc2hSZWFzb24gIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ1wiZmluaXNoX3N0ZXBcIiBwYXJ0cyBleHBlY3QgYW4gb2JqZWN0IHdpdGggYSBcImZpbmlzaFJlYXNvblwiIHByb3BlcnR5LidcbiAgICAgICk7XG4gICAgfVxuICAgIGNvbnN0IHJlc3VsdCA9IHtcbiAgICAgIGZpbmlzaFJlYXNvbjogdmFsdWUuZmluaXNoUmVhc29uLFxuICAgICAgaXNDb250aW51ZWQ6IGZhbHNlXG4gICAgfTtcbiAgICBpZiAoXCJ1c2FnZVwiIGluIHZhbHVlICYmIHZhbHVlLnVzYWdlICE9IG51bGwgJiYgdHlwZW9mIHZhbHVlLnVzYWdlID09PSBcIm9iamVjdFwiICYmIFwicHJvbXB0VG9rZW5zXCIgaW4gdmFsdWUudXNhZ2UgJiYgXCJjb21wbGV0aW9uVG9rZW5zXCIgaW4gdmFsdWUudXNhZ2UpIHtcbiAgICAgIHJlc3VsdC51c2FnZSA9IHtcbiAgICAgICAgcHJvbXB0VG9rZW5zOiB0eXBlb2YgdmFsdWUudXNhZ2UucHJvbXB0VG9rZW5zID09PSBcIm51bWJlclwiID8gdmFsdWUudXNhZ2UucHJvbXB0VG9rZW5zIDogTnVtYmVyLk5hTixcbiAgICAgICAgY29tcGxldGlvblRva2VuczogdHlwZW9mIHZhbHVlLnVzYWdlLmNvbXBsZXRpb25Ub2tlbnMgPT09IFwibnVtYmVyXCIgPyB2YWx1ZS51c2FnZS5jb21wbGV0aW9uVG9rZW5zIDogTnVtYmVyLk5hTlxuICAgICAgfTtcbiAgICB9XG4gICAgaWYgKFwiaXNDb250aW51ZWRcIiBpbiB2YWx1ZSAmJiB0eXBlb2YgdmFsdWUuaXNDb250aW51ZWQgPT09IFwiYm9vbGVhblwiKSB7XG4gICAgICByZXN1bHQuaXNDb250aW51ZWQgPSB2YWx1ZS5pc0NvbnRpbnVlZDtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwiZmluaXNoX3N0ZXBcIixcbiAgICAgIHZhbHVlOiByZXN1bHRcbiAgICB9O1xuICB9XG59O1xudmFyIHN0YXJ0U3RlcFN0cmVhbVBhcnQgPSB7XG4gIGNvZGU6IFwiZlwiLFxuICBuYW1lOiBcInN0YXJ0X3N0ZXBcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsIHx8IHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIiB8fCAhKFwibWVzc2FnZUlkXCIgaW4gdmFsdWUpIHx8IHR5cGVvZiB2YWx1ZS5tZXNzYWdlSWQgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ1wic3RhcnRfc3RlcFwiIHBhcnRzIGV4cGVjdCBhbiBvYmplY3Qgd2l0aCBhbiBcImlkXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6IFwic3RhcnRfc3RlcFwiLFxuICAgICAgdmFsdWU6IHtcbiAgICAgICAgbWVzc2FnZUlkOiB2YWx1ZS5tZXNzYWdlSWRcbiAgICAgIH1cbiAgICB9O1xuICB9XG59O1xudmFyIHJlYXNvbmluZ1N0cmVhbVBhcnQgPSB7XG4gIGNvZGU6IFwiZ1wiLFxuICBuYW1lOiBcInJlYXNvbmluZ1wiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdcInJlYXNvbmluZ1wiIHBhcnRzIGV4cGVjdCBhIHN0cmluZyB2YWx1ZS4nKTtcbiAgICB9XG4gICAgcmV0dXJuIHsgdHlwZTogXCJyZWFzb25pbmdcIiwgdmFsdWUgfTtcbiAgfVxufTtcbnZhciBzb3VyY2VQYXJ0ID0ge1xuICBjb2RlOiBcImhcIixcbiAgbmFtZTogXCJzb3VyY2VcIixcbiAgcGFyc2U6ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PSBudWxsIHx8IHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdcInNvdXJjZVwiIHBhcnRzIGV4cGVjdCBhIFNvdXJjZSBvYmplY3QuJyk7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICB0eXBlOiBcInNvdXJjZVwiLFxuICAgICAgdmFsdWVcbiAgICB9O1xuICB9XG59O1xudmFyIHJlZGFjdGVkUmVhc29uaW5nU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCJpXCIsXG4gIG5hbWU6IFwicmVkYWN0ZWRfcmVhc29uaW5nXCIsXG4gIHBhcnNlOiAodmFsdWUpID0+IHtcbiAgICBpZiAodmFsdWUgPT0gbnVsbCB8fCB0eXBlb2YgdmFsdWUgIT09IFwib2JqZWN0XCIgfHwgIShcImRhdGFcIiBpbiB2YWx1ZSkgfHwgdHlwZW9mIHZhbHVlLmRhdGEgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ1wicmVkYWN0ZWRfcmVhc29uaW5nXCIgcGFydHMgZXhwZWN0IGFuIG9iamVjdCB3aXRoIGEgXCJkYXRhXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHsgdHlwZTogXCJyZWRhY3RlZF9yZWFzb25pbmdcIiwgdmFsdWU6IHsgZGF0YTogdmFsdWUuZGF0YSB9IH07XG4gIH1cbn07XG52YXIgcmVhc29uaW5nU2lnbmF0dXJlU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCJqXCIsXG4gIG5hbWU6IFwicmVhc29uaW5nX3NpZ25hdHVyZVwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlICE9PSBcIm9iamVjdFwiIHx8ICEoXCJzaWduYXR1cmVcIiBpbiB2YWx1ZSkgfHwgdHlwZW9mIHZhbHVlLnNpZ25hdHVyZSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnXCJyZWFzb25pbmdfc2lnbmF0dXJlXCIgcGFydHMgZXhwZWN0IGFuIG9iamVjdCB3aXRoIGEgXCJzaWduYXR1cmVcIiBwcm9wZXJ0eS4nXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogXCJyZWFzb25pbmdfc2lnbmF0dXJlXCIsXG4gICAgICB2YWx1ZTogeyBzaWduYXR1cmU6IHZhbHVlLnNpZ25hdHVyZSB9XG4gICAgfTtcbiAgfVxufTtcbnZhciBmaWxlU3RyZWFtUGFydCA9IHtcbiAgY29kZTogXCJrXCIsXG4gIG5hbWU6IFwiZmlsZVwiLFxuICBwYXJzZTogKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09IG51bGwgfHwgdHlwZW9mIHZhbHVlICE9PSBcIm9iamVjdFwiIHx8ICEoXCJkYXRhXCIgaW4gdmFsdWUpIHx8IHR5cGVvZiB2YWx1ZS5kYXRhICE9PSBcInN0cmluZ1wiIHx8ICEoXCJtaW1lVHlwZVwiIGluIHZhbHVlKSB8fCB0eXBlb2YgdmFsdWUubWltZVR5cGUgIT09IFwic3RyaW5nXCIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ1wiZmlsZVwiIHBhcnRzIGV4cGVjdCBhbiBvYmplY3Qgd2l0aCBhIFwiZGF0YVwiIGFuZCBcIm1pbWVUeXBlXCIgcHJvcGVydHkuJ1xuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHsgdHlwZTogXCJmaWxlXCIsIHZhbHVlIH07XG4gIH1cbn07XG52YXIgZGF0YVN0cmVhbVBhcnRzID0gW1xuICB0ZXh0U3RyZWFtUGFydDIsXG4gIGRhdGFTdHJlYW1QYXJ0LFxuICBlcnJvclN0cmVhbVBhcnQyLFxuICBtZXNzYWdlQW5ub3RhdGlvbnNTdHJlYW1QYXJ0LFxuICB0b29sQ2FsbFN0cmVhbVBhcnQsXG4gIHRvb2xSZXN1bHRTdHJlYW1QYXJ0LFxuICB0b29sQ2FsbFN0cmVhbWluZ1N0YXJ0U3RyZWFtUGFydCxcbiAgdG9vbENhbGxEZWx0YVN0cmVhbVBhcnQsXG4gIGZpbmlzaE1lc3NhZ2VTdHJlYW1QYXJ0LFxuICBmaW5pc2hTdGVwU3RyZWFtUGFydCxcbiAgc3RhcnRTdGVwU3RyZWFtUGFydCxcbiAgcmVhc29uaW5nU3RyZWFtUGFydCxcbiAgc291cmNlUGFydCxcbiAgcmVkYWN0ZWRSZWFzb25pbmdTdHJlYW1QYXJ0LFxuICByZWFzb25pbmdTaWduYXR1cmVTdHJlYW1QYXJ0LFxuICBmaWxlU3RyZWFtUGFydFxuXTtcbnZhciBkYXRhU3RyZWFtUGFydHNCeUNvZGUgPSBPYmplY3QuZnJvbUVudHJpZXMoXG4gIGRhdGFTdHJlYW1QYXJ0cy5tYXAoKHBhcnQpID0+IFtwYXJ0LmNvZGUsIHBhcnRdKVxuKTtcbnZhciBEYXRhU3RyZWFtU3RyaW5nUHJlZml4ZXMgPSBPYmplY3QuZnJvbUVudHJpZXMoXG4gIGRhdGFTdHJlYW1QYXJ0cy5tYXAoKHBhcnQpID0+IFtwYXJ0Lm5hbWUsIHBhcnQuY29kZV0pXG4pO1xudmFyIHZhbGlkQ29kZXMyID0gZGF0YVN0cmVhbVBhcnRzLm1hcCgocGFydCkgPT4gcGFydC5jb2RlKTtcbnZhciBwYXJzZURhdGFTdHJlYW1QYXJ0ID0gKGxpbmUpID0+IHtcbiAgY29uc3QgZmlyc3RTZXBhcmF0b3JJbmRleCA9IGxpbmUuaW5kZXhPZihcIjpcIik7XG4gIGlmIChmaXJzdFNlcGFyYXRvckluZGV4ID09PSAtMSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkZhaWxlZCB0byBwYXJzZSBzdHJlYW0gc3RyaW5nLiBObyBzZXBhcmF0b3IgZm91bmQuXCIpO1xuICB9XG4gIGNvbnN0IHByZWZpeCA9IGxpbmUuc2xpY2UoMCwgZmlyc3RTZXBhcmF0b3JJbmRleCk7XG4gIGlmICghdmFsaWRDb2RlczIuaW5jbHVkZXMocHJlZml4KSkge1xuICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHBhcnNlIHN0cmVhbSBzdHJpbmcuIEludmFsaWQgY29kZSAke3ByZWZpeH0uYCk7XG4gIH1cbiAgY29uc3QgY29kZSA9IHByZWZpeDtcbiAgY29uc3QgdGV4dFZhbHVlID0gbGluZS5zbGljZShmaXJzdFNlcGFyYXRvckluZGV4ICsgMSk7XG4gIGNvbnN0IGpzb25WYWx1ZSA9IEpTT04ucGFyc2UodGV4dFZhbHVlKTtcbiAgcmV0dXJuIGRhdGFTdHJlYW1QYXJ0c0J5Q29kZVtjb2RlXS5wYXJzZShqc29uVmFsdWUpO1xufTtcbmZ1bmN0aW9uIGZvcm1hdERhdGFTdHJlYW1QYXJ0KHR5cGUsIHZhbHVlKSB7XG4gIGNvbnN0IHN0cmVhbVBhcnQgPSBkYXRhU3RyZWFtUGFydHMuZmluZCgocGFydCkgPT4gcGFydC5uYW1lID09PSB0eXBlKTtcbiAgaWYgKCFzdHJlYW1QYXJ0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHN0cmVhbSBwYXJ0IHR5cGU6ICR7dHlwZX1gKTtcbiAgfVxuICByZXR1cm4gYCR7c3RyZWFtUGFydC5jb2RlfToke0pTT04uc3RyaW5naWZ5KHZhbHVlKX1cbmA7XG59XG5cbi8vIHNyYy9wcm9jZXNzLWRhdGEtc3RyZWFtLnRzXG52YXIgTkVXTElORSA9IFwiXFxuXCIuY2hhckNvZGVBdCgwKTtcbmZ1bmN0aW9uIGNvbmNhdENodW5rcyhjaHVua3MsIHRvdGFsTGVuZ3RoKSB7XG4gIGNvbnN0IGNvbmNhdGVuYXRlZENodW5rcyA9IG5ldyBVaW50OEFycmF5KHRvdGFsTGVuZ3RoKTtcbiAgbGV0IG9mZnNldCA9IDA7XG4gIGZvciAoY29uc3QgY2h1bmsgb2YgY2h1bmtzKSB7XG4gICAgY29uY2F0ZW5hdGVkQ2h1bmtzLnNldChjaHVuaywgb2Zmc2V0KTtcbiAgICBvZmZzZXQgKz0gY2h1bmsubGVuZ3RoO1xuICB9XG4gIGNodW5rcy5sZW5ndGggPSAwO1xuICByZXR1cm4gY29uY2F0ZW5hdGVkQ2h1bmtzO1xufVxuYXN5bmMgZnVuY3Rpb24gcHJvY2Vzc0RhdGFTdHJlYW0oe1xuICBzdHJlYW0sXG4gIG9uVGV4dFBhcnQsXG4gIG9uUmVhc29uaW5nUGFydCxcbiAgb25SZWFzb25pbmdTaWduYXR1cmVQYXJ0LFxuICBvblJlZGFjdGVkUmVhc29uaW5nUGFydCxcbiAgb25Tb3VyY2VQYXJ0LFxuICBvbkZpbGVQYXJ0LFxuICBvbkRhdGFQYXJ0LFxuICBvbkVycm9yUGFydCxcbiAgb25Ub29sQ2FsbFN0cmVhbWluZ1N0YXJ0UGFydCxcbiAgb25Ub29sQ2FsbERlbHRhUGFydCxcbiAgb25Ub29sQ2FsbFBhcnQsXG4gIG9uVG9vbFJlc3VsdFBhcnQsXG4gIG9uTWVzc2FnZUFubm90YXRpb25zUGFydCxcbiAgb25GaW5pc2hNZXNzYWdlUGFydCxcbiAgb25GaW5pc2hTdGVwUGFydCxcbiAgb25TdGFydFN0ZXBQYXJ0XG59KSB7XG4gIGNvbnN0IHJlYWRlciA9IHN0cmVhbS5nZXRSZWFkZXIoKTtcbiAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpO1xuICBjb25zdCBjaHVua3MgPSBbXTtcbiAgbGV0IHRvdGFsTGVuZ3RoID0gMDtcbiAgd2hpbGUgKHRydWUpIHtcbiAgICBjb25zdCB7IHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuICAgIGlmICh2YWx1ZSkge1xuICAgICAgY2h1bmtzLnB1c2godmFsdWUpO1xuICAgICAgdG90YWxMZW5ndGggKz0gdmFsdWUubGVuZ3RoO1xuICAgICAgaWYgKHZhbHVlW3ZhbHVlLmxlbmd0aCAtIDFdICE9PSBORVdMSU5FKSB7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAoY2h1bmtzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGNvbnN0IGNvbmNhdGVuYXRlZENodW5rcyA9IGNvbmNhdENodW5rcyhjaHVua3MsIHRvdGFsTGVuZ3RoKTtcbiAgICB0b3RhbExlbmd0aCA9IDA7XG4gICAgY29uc3Qgc3RyZWFtUGFydHMgPSBkZWNvZGVyLmRlY29kZShjb25jYXRlbmF0ZWRDaHVua3MsIHsgc3RyZWFtOiB0cnVlIH0pLnNwbGl0KFwiXFxuXCIpLmZpbHRlcigobGluZSkgPT4gbGluZSAhPT0gXCJcIikubWFwKHBhcnNlRGF0YVN0cmVhbVBhcnQpO1xuICAgIGZvciAoY29uc3QgeyB0eXBlLCB2YWx1ZTogdmFsdWUyIH0gb2Ygc3RyZWFtUGFydHMpIHtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFwidGV4dFwiOlxuICAgICAgICAgIGF3YWl0IChvblRleHRQYXJ0ID09IG51bGwgPyB2b2lkIDAgOiBvblRleHRQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwicmVhc29uaW5nXCI6XG4gICAgICAgICAgYXdhaXQgKG9uUmVhc29uaW5nUGFydCA9PSBudWxsID8gdm9pZCAwIDogb25SZWFzb25pbmdQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwicmVhc29uaW5nX3NpZ25hdHVyZVwiOlxuICAgICAgICAgIGF3YWl0IChvblJlYXNvbmluZ1NpZ25hdHVyZVBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uUmVhc29uaW5nU2lnbmF0dXJlUGFydCh2YWx1ZTIpKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBcInJlZGFjdGVkX3JlYXNvbmluZ1wiOlxuICAgICAgICAgIGF3YWl0IChvblJlZGFjdGVkUmVhc29uaW5nUGFydCA9PSBudWxsID8gdm9pZCAwIDogb25SZWRhY3RlZFJlYXNvbmluZ1BhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJmaWxlXCI6XG4gICAgICAgICAgYXdhaXQgKG9uRmlsZVBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uRmlsZVBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJzb3VyY2VcIjpcbiAgICAgICAgICBhd2FpdCAob25Tb3VyY2VQYXJ0ID09IG51bGwgPyB2b2lkIDAgOiBvblNvdXJjZVBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJkYXRhXCI6XG4gICAgICAgICAgYXdhaXQgKG9uRGF0YVBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uRGF0YVBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJlcnJvclwiOlxuICAgICAgICAgIGF3YWl0IChvbkVycm9yUGFydCA9PSBudWxsID8gdm9pZCAwIDogb25FcnJvclBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJtZXNzYWdlX2Fubm90YXRpb25zXCI6XG4gICAgICAgICAgYXdhaXQgKG9uTWVzc2FnZUFubm90YXRpb25zUGFydCA9PSBudWxsID8gdm9pZCAwIDogb25NZXNzYWdlQW5ub3RhdGlvbnNQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwidG9vbF9jYWxsX3N0cmVhbWluZ19zdGFydFwiOlxuICAgICAgICAgIGF3YWl0IChvblRvb2xDYWxsU3RyZWFtaW5nU3RhcnRQYXJ0ID09IG51bGwgPyB2b2lkIDAgOiBvblRvb2xDYWxsU3RyZWFtaW5nU3RhcnRQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwidG9vbF9jYWxsX2RlbHRhXCI6XG4gICAgICAgICAgYXdhaXQgKG9uVG9vbENhbGxEZWx0YVBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uVG9vbENhbGxEZWx0YVBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJ0b29sX2NhbGxcIjpcbiAgICAgICAgICBhd2FpdCAob25Ub29sQ2FsbFBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uVG9vbENhbGxQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwidG9vbF9yZXN1bHRcIjpcbiAgICAgICAgICBhd2FpdCAob25Ub29sUmVzdWx0UGFydCA9PSBudWxsID8gdm9pZCAwIDogb25Ub29sUmVzdWx0UGFydCh2YWx1ZTIpKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSBcImZpbmlzaF9tZXNzYWdlXCI6XG4gICAgICAgICAgYXdhaXQgKG9uRmluaXNoTWVzc2FnZVBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uRmluaXNoTWVzc2FnZVBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJmaW5pc2hfc3RlcFwiOlxuICAgICAgICAgIGF3YWl0IChvbkZpbmlzaFN0ZXBQYXJ0ID09IG51bGwgPyB2b2lkIDAgOiBvbkZpbmlzaFN0ZXBQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwic3RhcnRfc3RlcFwiOlxuICAgICAgICAgIGF3YWl0IChvblN0YXJ0U3RlcFBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uU3RhcnRTdGVwUGFydCh2YWx1ZTIpKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgIGNvbnN0IGV4aGF1c3RpdmVDaGVjayA9IHR5cGU7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbmtub3duIHN0cmVhbSBwYXJ0IHR5cGU6ICR7ZXhoYXVzdGl2ZUNoZWNrfWApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIHNyYy9wcm9jZXNzLWNoYXQtcmVzcG9uc2UudHNcbmFzeW5jIGZ1bmN0aW9uIHByb2Nlc3NDaGF0UmVzcG9uc2Uoe1xuICBzdHJlYW0sXG4gIHVwZGF0ZSxcbiAgb25Ub29sQ2FsbCxcbiAgb25GaW5pc2gsXG4gIGdlbmVyYXRlSWQ6IGdlbmVyYXRlSWQyID0gZ2VuZXJhdGVJZEZ1bmN0aW9uLFxuICBnZXRDdXJyZW50RGF0ZSA9ICgpID0+IC8qIEBfX1BVUkVfXyAqLyBuZXcgRGF0ZSgpLFxuICBsYXN0TWVzc2FnZVxufSkge1xuICB2YXIgX2EsIF9iO1xuICBjb25zdCByZXBsYWNlTGFzdE1lc3NhZ2UgPSAobGFzdE1lc3NhZ2UgPT0gbnVsbCA/IHZvaWQgMCA6IGxhc3RNZXNzYWdlLnJvbGUpID09PSBcImFzc2lzdGFudFwiO1xuICBsZXQgc3RlcCA9IHJlcGxhY2VMYXN0TWVzc2FnZSA/IDEgKyAvLyBmaW5kIG1heCBzdGVwIGluIGV4aXN0aW5nIHRvb2wgaW52b2NhdGlvbnM6XG4gICgoX2IgPSAoX2EgPSBsYXN0TWVzc2FnZS50b29sSW52b2NhdGlvbnMpID09IG51bGwgPyB2b2lkIDAgOiBfYS5yZWR1Y2UoKG1heCwgdG9vbEludm9jYXRpb24pID0+IHtcbiAgICB2YXIgX2EyO1xuICAgIHJldHVybiBNYXRoLm1heChtYXgsIChfYTIgPSB0b29sSW52b2NhdGlvbi5zdGVwKSAhPSBudWxsID8gX2EyIDogMCk7XG4gIH0sIDApKSAhPSBudWxsID8gX2IgOiAwKSA6IDA7XG4gIGNvbnN0IG1lc3NhZ2UgPSByZXBsYWNlTGFzdE1lc3NhZ2UgPyBzdHJ1Y3R1cmVkQ2xvbmUobGFzdE1lc3NhZ2UpIDoge1xuICAgIGlkOiBnZW5lcmF0ZUlkMigpLFxuICAgIGNyZWF0ZWRBdDogZ2V0Q3VycmVudERhdGUoKSxcbiAgICByb2xlOiBcImFzc2lzdGFudFwiLFxuICAgIGNvbnRlbnQ6IFwiXCIsXG4gICAgcGFydHM6IFtdXG4gIH07XG4gIGxldCBjdXJyZW50VGV4dFBhcnQgPSB2b2lkIDA7XG4gIGxldCBjdXJyZW50UmVhc29uaW5nUGFydCA9IHZvaWQgMDtcbiAgbGV0IGN1cnJlbnRSZWFzb25pbmdUZXh0RGV0YWlsID0gdm9pZCAwO1xuICBmdW5jdGlvbiB1cGRhdGVUb29sSW52b2NhdGlvblBhcnQodG9vbENhbGxJZCwgaW52b2NhdGlvbikge1xuICAgIGNvbnN0IHBhcnQgPSBtZXNzYWdlLnBhcnRzLmZpbmQoXG4gICAgICAocGFydDIpID0+IHBhcnQyLnR5cGUgPT09IFwidG9vbC1pbnZvY2F0aW9uXCIgJiYgcGFydDIudG9vbEludm9jYXRpb24udG9vbENhbGxJZCA9PT0gdG9vbENhbGxJZFxuICAgICk7XG4gICAgaWYgKHBhcnQgIT0gbnVsbCkge1xuICAgICAgcGFydC50b29sSW52b2NhdGlvbiA9IGludm9jYXRpb247XG4gICAgfSBlbHNlIHtcbiAgICAgIG1lc3NhZ2UucGFydHMucHVzaCh7XG4gICAgICAgIHR5cGU6IFwidG9vbC1pbnZvY2F0aW9uXCIsXG4gICAgICAgIHRvb2xJbnZvY2F0aW9uOiBpbnZvY2F0aW9uXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgY29uc3QgZGF0YSA9IFtdO1xuICBsZXQgbWVzc2FnZUFubm90YXRpb25zID0gcmVwbGFjZUxhc3RNZXNzYWdlID8gbGFzdE1lc3NhZ2UgPT0gbnVsbCA/IHZvaWQgMCA6IGxhc3RNZXNzYWdlLmFubm90YXRpb25zIDogdm9pZCAwO1xuICBjb25zdCBwYXJ0aWFsVG9vbENhbGxzID0ge307XG4gIGxldCB1c2FnZSA9IHtcbiAgICBjb21wbGV0aW9uVG9rZW5zOiBOYU4sXG4gICAgcHJvbXB0VG9rZW5zOiBOYU4sXG4gICAgdG90YWxUb2tlbnM6IE5hTlxuICB9O1xuICBsZXQgZmluaXNoUmVhc29uID0gXCJ1bmtub3duXCI7XG4gIGZ1bmN0aW9uIGV4ZWNVcGRhdGUoKSB7XG4gICAgY29uc3QgY29waWVkRGF0YSA9IFsuLi5kYXRhXTtcbiAgICBpZiAobWVzc2FnZUFubm90YXRpb25zID09IG51bGwgPyB2b2lkIDAgOiBtZXNzYWdlQW5ub3RhdGlvbnMubGVuZ3RoKSB7XG4gICAgICBtZXNzYWdlLmFubm90YXRpb25zID0gbWVzc2FnZUFubm90YXRpb25zO1xuICAgIH1cbiAgICBjb25zdCBjb3BpZWRNZXNzYWdlID0ge1xuICAgICAgLy8gZGVlcCBjb3B5IHRoZSBtZXNzYWdlIHRvIGVuc3VyZSB0aGF0IGRlZXAgY2hhbmdlcyAobXNnIGF0dGFjaG1lbnRzKSBhcmUgdXBkYXRlZFxuICAgICAgLy8gd2l0aCBTb2xpZEpTLiBTb2xpZEpTIHVzZXMgcmVmZXJlbnRpYWwgaW50ZWdyYXRpb24gb2Ygc3ViLW9iamVjdHMgdG8gZGV0ZWN0IGNoYW5nZXMuXG4gICAgICAuLi5zdHJ1Y3R1cmVkQ2xvbmUobWVzc2FnZSksXG4gICAgICAvLyBhZGQgYSByZXZpc2lvbiBpZCB0byBlbnN1cmUgdGhhdCB0aGUgbWVzc2FnZSBpcyB1cGRhdGVkIHdpdGggU1dSLiBTV1IgdXNlcyBhXG4gICAgICAvLyBoYXNoaW5nIGFwcHJvYWNoIGJ5IGRlZmF1bHQgdG8gZGV0ZWN0IGNoYW5nZXMsIGJ1dCBpdCBvbmx5IHdvcmtzIGZvciBzaGFsbG93XG4gICAgICAvLyBjaGFuZ2VzLiBUaGlzIGlzIHdoeSB3ZSBuZWVkIHRvIGFkZCBhIHJldmlzaW9uIGlkIHRvIGVuc3VyZSB0aGF0IHRoZSBtZXNzYWdlXG4gICAgICAvLyBpcyB1cGRhdGVkIHdpdGggU1dSICh3aXRob3V0IGl0LCB0aGUgY2hhbmdlcyBnZXQgc3R1Y2sgaW4gU1dSIGFuZCBhcmUgbm90XG4gICAgICAvLyBmb3J3YXJkZWQgdG8gcmVuZGVyaW5nKTpcbiAgICAgIHJldmlzaW9uSWQ6IGdlbmVyYXRlSWQyKClcbiAgICB9O1xuICAgIHVwZGF0ZSh7XG4gICAgICBtZXNzYWdlOiBjb3BpZWRNZXNzYWdlLFxuICAgICAgZGF0YTogY29waWVkRGF0YSxcbiAgICAgIHJlcGxhY2VMYXN0TWVzc2FnZVxuICAgIH0pO1xuICB9XG4gIGF3YWl0IHByb2Nlc3NEYXRhU3RyZWFtKHtcbiAgICBzdHJlYW0sXG4gICAgb25UZXh0UGFydCh2YWx1ZSkge1xuICAgICAgaWYgKGN1cnJlbnRUZXh0UGFydCA9PSBudWxsKSB7XG4gICAgICAgIGN1cnJlbnRUZXh0UGFydCA9IHtcbiAgICAgICAgICB0eXBlOiBcInRleHRcIixcbiAgICAgICAgICB0ZXh0OiB2YWx1ZVxuICAgICAgICB9O1xuICAgICAgICBtZXNzYWdlLnBhcnRzLnB1c2goY3VycmVudFRleHRQYXJ0KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGN1cnJlbnRUZXh0UGFydC50ZXh0ICs9IHZhbHVlO1xuICAgICAgfVxuICAgICAgbWVzc2FnZS5jb250ZW50ICs9IHZhbHVlO1xuICAgICAgZXhlY1VwZGF0ZSgpO1xuICAgIH0sXG4gICAgb25SZWFzb25pbmdQYXJ0KHZhbHVlKSB7XG4gICAgICB2YXIgX2EyO1xuICAgICAgaWYgKGN1cnJlbnRSZWFzb25pbmdUZXh0RGV0YWlsID09IG51bGwpIHtcbiAgICAgICAgY3VycmVudFJlYXNvbmluZ1RleHREZXRhaWwgPSB7IHR5cGU6IFwidGV4dFwiLCB0ZXh0OiB2YWx1ZSB9O1xuICAgICAgICBpZiAoY3VycmVudFJlYXNvbmluZ1BhcnQgIT0gbnVsbCkge1xuICAgICAgICAgIGN1cnJlbnRSZWFzb25pbmdQYXJ0LmRldGFpbHMucHVzaChjdXJyZW50UmVhc29uaW5nVGV4dERldGFpbCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGN1cnJlbnRSZWFzb25pbmdUZXh0RGV0YWlsLnRleHQgKz0gdmFsdWU7XG4gICAgICB9XG4gICAgICBpZiAoY3VycmVudFJlYXNvbmluZ1BhcnQgPT0gbnVsbCkge1xuICAgICAgICBjdXJyZW50UmVhc29uaW5nUGFydCA9IHtcbiAgICAgICAgICB0eXBlOiBcInJlYXNvbmluZ1wiLFxuICAgICAgICAgIHJlYXNvbmluZzogdmFsdWUsXG4gICAgICAgICAgZGV0YWlsczogW2N1cnJlbnRSZWFzb25pbmdUZXh0RGV0YWlsXVxuICAgICAgICB9O1xuICAgICAgICBtZXNzYWdlLnBhcnRzLnB1c2goY3VycmVudFJlYXNvbmluZ1BhcnQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY3VycmVudFJlYXNvbmluZ1BhcnQucmVhc29uaW5nICs9IHZhbHVlO1xuICAgICAgfVxuICAgICAgbWVzc2FnZS5yZWFzb25pbmcgPSAoKF9hMiA9IG1lc3NhZ2UucmVhc29uaW5nKSAhPSBudWxsID8gX2EyIDogXCJcIikgKyB2YWx1ZTtcbiAgICAgIGV4ZWNVcGRhdGUoKTtcbiAgICB9LFxuICAgIG9uUmVhc29uaW5nU2lnbmF0dXJlUGFydCh2YWx1ZSkge1xuICAgICAgaWYgKGN1cnJlbnRSZWFzb25pbmdUZXh0RGV0YWlsICE9IG51bGwpIHtcbiAgICAgICAgY3VycmVudFJlYXNvbmluZ1RleHREZXRhaWwuc2lnbmF0dXJlID0gdmFsdWUuc2lnbmF0dXJlO1xuICAgICAgfVxuICAgIH0sXG4gICAgb25SZWRhY3RlZFJlYXNvbmluZ1BhcnQodmFsdWUpIHtcbiAgICAgIGlmIChjdXJyZW50UmVhc29uaW5nUGFydCA9PSBudWxsKSB7XG4gICAgICAgIGN1cnJlbnRSZWFzb25pbmdQYXJ0ID0ge1xuICAgICAgICAgIHR5cGU6IFwicmVhc29uaW5nXCIsXG4gICAgICAgICAgcmVhc29uaW5nOiBcIlwiLFxuICAgICAgICAgIGRldGFpbHM6IFtdXG4gICAgICAgIH07XG4gICAgICAgIG1lc3NhZ2UucGFydHMucHVzaChjdXJyZW50UmVhc29uaW5nUGFydCk7XG4gICAgICB9XG4gICAgICBjdXJyZW50UmVhc29uaW5nUGFydC5kZXRhaWxzLnB1c2goe1xuICAgICAgICB0eXBlOiBcInJlZGFjdGVkXCIsXG4gICAgICAgIGRhdGE6IHZhbHVlLmRhdGFcbiAgICAgIH0pO1xuICAgICAgY3VycmVudFJlYXNvbmluZ1RleHREZXRhaWwgPSB2b2lkIDA7XG4gICAgICBleGVjVXBkYXRlKCk7XG4gICAgfSxcbiAgICBvbkZpbGVQYXJ0KHZhbHVlKSB7XG4gICAgICBtZXNzYWdlLnBhcnRzLnB1c2goe1xuICAgICAgICB0eXBlOiBcImZpbGVcIixcbiAgICAgICAgbWltZVR5cGU6IHZhbHVlLm1pbWVUeXBlLFxuICAgICAgICBkYXRhOiB2YWx1ZS5kYXRhXG4gICAgICB9KTtcbiAgICAgIGV4ZWNVcGRhdGUoKTtcbiAgICB9LFxuICAgIG9uU291cmNlUGFydCh2YWx1ZSkge1xuICAgICAgbWVzc2FnZS5wYXJ0cy5wdXNoKHtcbiAgICAgICAgdHlwZTogXCJzb3VyY2VcIixcbiAgICAgICAgc291cmNlOiB2YWx1ZVxuICAgICAgfSk7XG4gICAgICBleGVjVXBkYXRlKCk7XG4gICAgfSxcbiAgICBvblRvb2xDYWxsU3RyZWFtaW5nU3RhcnRQYXJ0KHZhbHVlKSB7XG4gICAgICBpZiAobWVzc2FnZS50b29sSW52b2NhdGlvbnMgPT0gbnVsbCkge1xuICAgICAgICBtZXNzYWdlLnRvb2xJbnZvY2F0aW9ucyA9IFtdO1xuICAgICAgfVxuICAgICAgcGFydGlhbFRvb2xDYWxsc1t2YWx1ZS50b29sQ2FsbElkXSA9IHtcbiAgICAgICAgdGV4dDogXCJcIixcbiAgICAgICAgc3RlcCxcbiAgICAgICAgdG9vbE5hbWU6IHZhbHVlLnRvb2xOYW1lLFxuICAgICAgICBpbmRleDogbWVzc2FnZS50b29sSW52b2NhdGlvbnMubGVuZ3RoXG4gICAgICB9O1xuICAgICAgY29uc3QgaW52b2NhdGlvbiA9IHtcbiAgICAgICAgc3RhdGU6IFwicGFydGlhbC1jYWxsXCIsXG4gICAgICAgIHN0ZXAsXG4gICAgICAgIHRvb2xDYWxsSWQ6IHZhbHVlLnRvb2xDYWxsSWQsXG4gICAgICAgIHRvb2xOYW1lOiB2YWx1ZS50b29sTmFtZSxcbiAgICAgICAgYXJnczogdm9pZCAwXG4gICAgICB9O1xuICAgICAgbWVzc2FnZS50b29sSW52b2NhdGlvbnMucHVzaChpbnZvY2F0aW9uKTtcbiAgICAgIHVwZGF0ZVRvb2xJbnZvY2F0aW9uUGFydCh2YWx1ZS50b29sQ2FsbElkLCBpbnZvY2F0aW9uKTtcbiAgICAgIGV4ZWNVcGRhdGUoKTtcbiAgICB9LFxuICAgIG9uVG9vbENhbGxEZWx0YVBhcnQodmFsdWUpIHtcbiAgICAgIGNvbnN0IHBhcnRpYWxUb29sQ2FsbCA9IHBhcnRpYWxUb29sQ2FsbHNbdmFsdWUudG9vbENhbGxJZF07XG4gICAgICBwYXJ0aWFsVG9vbENhbGwudGV4dCArPSB2YWx1ZS5hcmdzVGV4dERlbHRhO1xuICAgICAgY29uc3QgeyB2YWx1ZTogcGFydGlhbEFyZ3MgfSA9IHBhcnNlUGFydGlhbEpzb24ocGFydGlhbFRvb2xDYWxsLnRleHQpO1xuICAgICAgY29uc3QgaW52b2NhdGlvbiA9IHtcbiAgICAgICAgc3RhdGU6IFwicGFydGlhbC1jYWxsXCIsXG4gICAgICAgIHN0ZXA6IHBhcnRpYWxUb29sQ2FsbC5zdGVwLFxuICAgICAgICB0b29sQ2FsbElkOiB2YWx1ZS50b29sQ2FsbElkLFxuICAgICAgICB0b29sTmFtZTogcGFydGlhbFRvb2xDYWxsLnRvb2xOYW1lLFxuICAgICAgICBhcmdzOiBwYXJ0aWFsQXJnc1xuICAgICAgfTtcbiAgICAgIG1lc3NhZ2UudG9vbEludm9jYXRpb25zW3BhcnRpYWxUb29sQ2FsbC5pbmRleF0gPSBpbnZvY2F0aW9uO1xuICAgICAgdXBkYXRlVG9vbEludm9jYXRpb25QYXJ0KHZhbHVlLnRvb2xDYWxsSWQsIGludm9jYXRpb24pO1xuICAgICAgZXhlY1VwZGF0ZSgpO1xuICAgIH0sXG4gICAgYXN5bmMgb25Ub29sQ2FsbFBhcnQodmFsdWUpIHtcbiAgICAgIGNvbnN0IGludm9jYXRpb24gPSB7XG4gICAgICAgIHN0YXRlOiBcImNhbGxcIixcbiAgICAgICAgc3RlcCxcbiAgICAgICAgLi4udmFsdWVcbiAgICAgIH07XG4gICAgICBpZiAocGFydGlhbFRvb2xDYWxsc1t2YWx1ZS50b29sQ2FsbElkXSAhPSBudWxsKSB7XG4gICAgICAgIG1lc3NhZ2UudG9vbEludm9jYXRpb25zW3BhcnRpYWxUb29sQ2FsbHNbdmFsdWUudG9vbENhbGxJZF0uaW5kZXhdID0gaW52b2NhdGlvbjtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlmIChtZXNzYWdlLnRvb2xJbnZvY2F0aW9ucyA9PSBudWxsKSB7XG4gICAgICAgICAgbWVzc2FnZS50b29sSW52b2NhdGlvbnMgPSBbXTtcbiAgICAgICAgfVxuICAgICAgICBtZXNzYWdlLnRvb2xJbnZvY2F0aW9ucy5wdXNoKGludm9jYXRpb24pO1xuICAgICAgfVxuICAgICAgdXBkYXRlVG9vbEludm9jYXRpb25QYXJ0KHZhbHVlLnRvb2xDYWxsSWQsIGludm9jYXRpb24pO1xuICAgICAgZXhlY1VwZGF0ZSgpO1xuICAgICAgaWYgKG9uVG9vbENhbGwpIHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgb25Ub29sQ2FsbCh7IHRvb2xDYWxsOiB2YWx1ZSB9KTtcbiAgICAgICAgaWYgKHJlc3VsdCAhPSBudWxsKSB7XG4gICAgICAgICAgY29uc3QgaW52b2NhdGlvbjIgPSB7XG4gICAgICAgICAgICBzdGF0ZTogXCJyZXN1bHRcIixcbiAgICAgICAgICAgIHN0ZXAsXG4gICAgICAgICAgICAuLi52YWx1ZSxcbiAgICAgICAgICAgIHJlc3VsdFxuICAgICAgICAgIH07XG4gICAgICAgICAgbWVzc2FnZS50b29sSW52b2NhdGlvbnNbbWVzc2FnZS50b29sSW52b2NhdGlvbnMubGVuZ3RoIC0gMV0gPSBpbnZvY2F0aW9uMjtcbiAgICAgICAgICB1cGRhdGVUb29sSW52b2NhdGlvblBhcnQodmFsdWUudG9vbENhbGxJZCwgaW52b2NhdGlvbjIpO1xuICAgICAgICAgIGV4ZWNVcGRhdGUoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgb25Ub29sUmVzdWx0UGFydCh2YWx1ZSkge1xuICAgICAgY29uc3QgdG9vbEludm9jYXRpb25zID0gbWVzc2FnZS50b29sSW52b2NhdGlvbnM7XG4gICAgICBpZiAodG9vbEludm9jYXRpb25zID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwidG9vbF9yZXN1bHQgbXVzdCBiZSBwcmVjZWRlZCBieSBhIHRvb2xfY2FsbFwiKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHRvb2xJbnZvY2F0aW9uSW5kZXggPSB0b29sSW52b2NhdGlvbnMuZmluZEluZGV4KFxuICAgICAgICAoaW52b2NhdGlvbjIpID0+IGludm9jYXRpb24yLnRvb2xDYWxsSWQgPT09IHZhbHVlLnRvb2xDYWxsSWRcbiAgICAgICk7XG4gICAgICBpZiAodG9vbEludm9jYXRpb25JbmRleCA9PT0gLTEpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIFwidG9vbF9yZXN1bHQgbXVzdCBiZSBwcmVjZWRlZCBieSBhIHRvb2xfY2FsbCB3aXRoIHRoZSBzYW1lIHRvb2xDYWxsSWRcIlxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgY29uc3QgaW52b2NhdGlvbiA9IHtcbiAgICAgICAgLi4udG9vbEludm9jYXRpb25zW3Rvb2xJbnZvY2F0aW9uSW5kZXhdLFxuICAgICAgICBzdGF0ZTogXCJyZXN1bHRcIixcbiAgICAgICAgLi4udmFsdWVcbiAgICAgIH07XG4gICAgICB0b29sSW52b2NhdGlvbnNbdG9vbEludm9jYXRpb25JbmRleF0gPSBpbnZvY2F0aW9uO1xuICAgICAgdXBkYXRlVG9vbEludm9jYXRpb25QYXJ0KHZhbHVlLnRvb2xDYWxsSWQsIGludm9jYXRpb24pO1xuICAgICAgZXhlY1VwZGF0ZSgpO1xuICAgIH0sXG4gICAgb25EYXRhUGFydCh2YWx1ZSkge1xuICAgICAgZGF0YS5wdXNoKC4uLnZhbHVlKTtcbiAgICAgIGV4ZWNVcGRhdGUoKTtcbiAgICB9LFxuICAgIG9uTWVzc2FnZUFubm90YXRpb25zUGFydCh2YWx1ZSkge1xuICAgICAgaWYgKG1lc3NhZ2VBbm5vdGF0aW9ucyA9PSBudWxsKSB7XG4gICAgICAgIG1lc3NhZ2VBbm5vdGF0aW9ucyA9IFsuLi52YWx1ZV07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBtZXNzYWdlQW5ub3RhdGlvbnMucHVzaCguLi52YWx1ZSk7XG4gICAgICB9XG4gICAgICBleGVjVXBkYXRlKCk7XG4gICAgfSxcbiAgICBvbkZpbmlzaFN0ZXBQYXJ0KHZhbHVlKSB7XG4gICAgICBzdGVwICs9IDE7XG4gICAgICBjdXJyZW50VGV4dFBhcnQgPSB2YWx1ZS5pc0NvbnRpbnVlZCA/IGN1cnJlbnRUZXh0UGFydCA6IHZvaWQgMDtcbiAgICAgIGN1cnJlbnRSZWFzb25pbmdQYXJ0ID0gdm9pZCAwO1xuICAgICAgY3VycmVudFJlYXNvbmluZ1RleHREZXRhaWwgPSB2b2lkIDA7XG4gICAgfSxcbiAgICBvblN0YXJ0U3RlcFBhcnQodmFsdWUpIHtcbiAgICAgIGlmICghcmVwbGFjZUxhc3RNZXNzYWdlKSB7XG4gICAgICAgIG1lc3NhZ2UuaWQgPSB2YWx1ZS5tZXNzYWdlSWQ7XG4gICAgICB9XG4gICAgICBtZXNzYWdlLnBhcnRzLnB1c2goeyB0eXBlOiBcInN0ZXAtc3RhcnRcIiB9KTtcbiAgICAgIGV4ZWNVcGRhdGUoKTtcbiAgICB9LFxuICAgIG9uRmluaXNoTWVzc2FnZVBhcnQodmFsdWUpIHtcbiAgICAgIGZpbmlzaFJlYXNvbiA9IHZhbHVlLmZpbmlzaFJlYXNvbjtcbiAgICAgIGlmICh2YWx1ZS51c2FnZSAhPSBudWxsKSB7XG4gICAgICAgIHVzYWdlID0gY2FsY3VsYXRlTGFuZ3VhZ2VNb2RlbFVzYWdlKHZhbHVlLnVzYWdlKTtcbiAgICAgIH1cbiAgICB9LFxuICAgIG9uRXJyb3JQYXJ0KGVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3IpO1xuICAgIH1cbiAgfSk7XG4gIG9uRmluaXNoID09IG51bGwgPyB2b2lkIDAgOiBvbkZpbmlzaCh7IG1lc3NhZ2UsIGZpbmlzaFJlYXNvbiwgdXNhZ2UgfSk7XG59XG5cbi8vIHNyYy9wcm9jZXNzLWNoYXQtdGV4dC1yZXNwb25zZS50c1xuaW1wb3J0IHsgZ2VuZXJhdGVJZCBhcyBnZW5lcmF0ZUlkRnVuY3Rpb24yIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXItdXRpbHNcIjtcblxuLy8gc3JjL3Byb2Nlc3MtdGV4dC1zdHJlYW0udHNcbmFzeW5jIGZ1bmN0aW9uIHByb2Nlc3NUZXh0U3RyZWFtKHtcbiAgc3RyZWFtLFxuICBvblRleHRQYXJ0XG59KSB7XG4gIGNvbnN0IHJlYWRlciA9IHN0cmVhbS5waXBlVGhyb3VnaChuZXcgVGV4dERlY29kZXJTdHJlYW0oKSkuZ2V0UmVhZGVyKCk7XG4gIHdoaWxlICh0cnVlKSB7XG4gICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTtcbiAgICBpZiAoZG9uZSkge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGF3YWl0IG9uVGV4dFBhcnQodmFsdWUpO1xuICB9XG59XG5cbi8vIHNyYy9wcm9jZXNzLWNoYXQtdGV4dC1yZXNwb25zZS50c1xuYXN5bmMgZnVuY3Rpb24gcHJvY2Vzc0NoYXRUZXh0UmVzcG9uc2Uoe1xuICBzdHJlYW0sXG4gIHVwZGF0ZSxcbiAgb25GaW5pc2gsXG4gIGdldEN1cnJlbnREYXRlID0gKCkgPT4gLyogQF9fUFVSRV9fICovIG5ldyBEYXRlKCksXG4gIGdlbmVyYXRlSWQ6IGdlbmVyYXRlSWQyID0gZ2VuZXJhdGVJZEZ1bmN0aW9uMlxufSkge1xuICBjb25zdCB0ZXh0UGFydCA9IHsgdHlwZTogXCJ0ZXh0XCIsIHRleHQ6IFwiXCIgfTtcbiAgY29uc3QgcmVzdWx0TWVzc2FnZSA9IHtcbiAgICBpZDogZ2VuZXJhdGVJZDIoKSxcbiAgICBjcmVhdGVkQXQ6IGdldEN1cnJlbnREYXRlKCksXG4gICAgcm9sZTogXCJhc3Npc3RhbnRcIixcbiAgICBjb250ZW50OiBcIlwiLFxuICAgIHBhcnRzOiBbdGV4dFBhcnRdXG4gIH07XG4gIGF3YWl0IHByb2Nlc3NUZXh0U3RyZWFtKHtcbiAgICBzdHJlYW0sXG4gICAgb25UZXh0UGFydDogKGNodW5rKSA9PiB7XG4gICAgICByZXN1bHRNZXNzYWdlLmNvbnRlbnQgKz0gY2h1bms7XG4gICAgICB0ZXh0UGFydC50ZXh0ICs9IGNodW5rO1xuICAgICAgdXBkYXRlKHtcbiAgICAgICAgbWVzc2FnZTogeyAuLi5yZXN1bHRNZXNzYWdlIH0sXG4gICAgICAgIGRhdGE6IFtdLFxuICAgICAgICByZXBsYWNlTGFzdE1lc3NhZ2U6IGZhbHNlXG4gICAgICB9KTtcbiAgICB9XG4gIH0pO1xuICBvbkZpbmlzaCA9PSBudWxsID8gdm9pZCAwIDogb25GaW5pc2gocmVzdWx0TWVzc2FnZSwge1xuICAgIHVzYWdlOiB7IGNvbXBsZXRpb25Ub2tlbnM6IE5hTiwgcHJvbXB0VG9rZW5zOiBOYU4sIHRvdGFsVG9rZW5zOiBOYU4gfSxcbiAgICBmaW5pc2hSZWFzb246IFwidW5rbm93blwiXG4gIH0pO1xufVxuXG4vLyBzcmMvY2FsbC1jaGF0LWFwaS50c1xudmFyIGdldE9yaWdpbmFsRmV0Y2ggPSAoKSA9PiBmZXRjaDtcbmFzeW5jIGZ1bmN0aW9uIGNhbGxDaGF0QXBpKHtcbiAgYXBpLFxuICBib2R5LFxuICBzdHJlYW1Qcm90b2NvbCA9IFwiZGF0YVwiLFxuICBjcmVkZW50aWFscyxcbiAgaGVhZGVycyxcbiAgYWJvcnRDb250cm9sbGVyLFxuICByZXN0b3JlTWVzc2FnZXNPbkZhaWx1cmUsXG4gIG9uUmVzcG9uc2UsXG4gIG9uVXBkYXRlLFxuICBvbkZpbmlzaCxcbiAgb25Ub29sQ2FsbCxcbiAgZ2VuZXJhdGVJZDogZ2VuZXJhdGVJZDIsXG4gIGZldGNoOiBmZXRjaDIgPSBnZXRPcmlnaW5hbEZldGNoKCksXG4gIGxhc3RNZXNzYWdlLFxuICByZXF1ZXN0VHlwZSA9IFwiZ2VuZXJhdGVcIlxufSkge1xuICB2YXIgX2EsIF9iLCBfYztcbiAgY29uc3QgcmVxdWVzdCA9IHJlcXVlc3RUeXBlID09PSBcInJlc3VtZVwiID8gZmV0Y2gyKGAke2FwaX0/Y2hhdElkPSR7Ym9keS5pZH1gLCB7XG4gICAgbWV0aG9kOiBcIkdFVFwiLFxuICAgIGhlYWRlcnM6IHtcbiAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgLi4uaGVhZGVyc1xuICAgIH0sXG4gICAgc2lnbmFsOiAoX2EgPSBhYm9ydENvbnRyb2xsZXIgPT0gbnVsbCA/IHZvaWQgMCA6IGFib3J0Q29udHJvbGxlcigpKSA9PSBudWxsID8gdm9pZCAwIDogX2Euc2lnbmFsLFxuICAgIGNyZWRlbnRpYWxzXG4gIH0pIDogZmV0Y2gyKGFwaSwge1xuICAgIG1ldGhvZDogXCJQT1NUXCIsXG4gICAgYm9keTogSlNPTi5zdHJpbmdpZnkoYm9keSksXG4gICAgaGVhZGVyczoge1xuICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAuLi5oZWFkZXJzXG4gICAgfSxcbiAgICBzaWduYWw6IChfYiA9IGFib3J0Q29udHJvbGxlciA9PSBudWxsID8gdm9pZCAwIDogYWJvcnRDb250cm9sbGVyKCkpID09IG51bGwgPyB2b2lkIDAgOiBfYi5zaWduYWwsXG4gICAgY3JlZGVudGlhbHNcbiAgfSk7XG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVxdWVzdC5jYXRjaCgoZXJyKSA9PiB7XG4gICAgcmVzdG9yZU1lc3NhZ2VzT25GYWlsdXJlKCk7XG4gICAgdGhyb3cgZXJyO1xuICB9KTtcbiAgaWYgKG9uUmVzcG9uc2UpIHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgb25SZXNwb25zZShyZXNwb25zZSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9XG4gIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICByZXN0b3JlTWVzc2FnZXNPbkZhaWx1cmUoKTtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAoX2MgPSBhd2FpdCByZXNwb25zZS50ZXh0KCkpICE9IG51bGwgPyBfYyA6IFwiRmFpbGVkIHRvIGZldGNoIHRoZSBjaGF0IHJlc3BvbnNlLlwiXG4gICAgKTtcbiAgfVxuICBpZiAoIXJlc3BvbnNlLmJvZHkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJUaGUgcmVzcG9uc2UgYm9keSBpcyBlbXB0eS5cIik7XG4gIH1cbiAgc3dpdGNoIChzdHJlYW1Qcm90b2NvbCkge1xuICAgIGNhc2UgXCJ0ZXh0XCI6IHtcbiAgICAgIGF3YWl0IHByb2Nlc3NDaGF0VGV4dFJlc3BvbnNlKHtcbiAgICAgICAgc3RyZWFtOiByZXNwb25zZS5ib2R5LFxuICAgICAgICB1cGRhdGU6IG9uVXBkYXRlLFxuICAgICAgICBvbkZpbmlzaCxcbiAgICAgICAgZ2VuZXJhdGVJZDogZ2VuZXJhdGVJZDJcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjYXNlIFwiZGF0YVwiOiB7XG4gICAgICBhd2FpdCBwcm9jZXNzQ2hhdFJlc3BvbnNlKHtcbiAgICAgICAgc3RyZWFtOiByZXNwb25zZS5ib2R5LFxuICAgICAgICB1cGRhdGU6IG9uVXBkYXRlLFxuICAgICAgICBsYXN0TWVzc2FnZSxcbiAgICAgICAgb25Ub29sQ2FsbCxcbiAgICAgICAgb25GaW5pc2goeyBtZXNzYWdlLCBmaW5pc2hSZWFzb24sIHVzYWdlIH0pIHtcbiAgICAgICAgICBpZiAob25GaW5pc2ggJiYgbWVzc2FnZSAhPSBudWxsKSB7XG4gICAgICAgICAgICBvbkZpbmlzaChtZXNzYWdlLCB7IHVzYWdlLCBmaW5pc2hSZWFzb24gfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBnZW5lcmF0ZUlkOiBnZW5lcmF0ZUlkMlxuICAgICAgfSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGRlZmF1bHQ6IHtcbiAgICAgIGNvbnN0IGV4aGF1c3RpdmVDaGVjayA9IHN0cmVhbVByb3RvY29sO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbmtub3duIHN0cmVhbSBwcm90b2NvbDogJHtleGhhdXN0aXZlQ2hlY2t9YCk7XG4gICAgfVxuICB9XG59XG5cbi8vIHNyYy9jYWxsLWNvbXBsZXRpb24tYXBpLnRzXG52YXIgZ2V0T3JpZ2luYWxGZXRjaDIgPSAoKSA9PiBmZXRjaDtcbmFzeW5jIGZ1bmN0aW9uIGNhbGxDb21wbGV0aW9uQXBpKHtcbiAgYXBpLFxuICBwcm9tcHQsXG4gIGNyZWRlbnRpYWxzLFxuICBoZWFkZXJzLFxuICBib2R5LFxuICBzdHJlYW1Qcm90b2NvbCA9IFwiZGF0YVwiLFxuICBzZXRDb21wbGV0aW9uLFxuICBzZXRMb2FkaW5nLFxuICBzZXRFcnJvcixcbiAgc2V0QWJvcnRDb250cm9sbGVyLFxuICBvblJlc3BvbnNlLFxuICBvbkZpbmlzaCxcbiAgb25FcnJvcixcbiAgb25EYXRhLFxuICBmZXRjaDogZmV0Y2gyID0gZ2V0T3JpZ2luYWxGZXRjaDIoKVxufSkge1xuICB2YXIgX2E7XG4gIHRyeSB7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcih2b2lkIDApO1xuICAgIGNvbnN0IGFib3J0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICBzZXRBYm9ydENvbnRyb2xsZXIoYWJvcnRDb250cm9sbGVyKTtcbiAgICBzZXRDb21wbGV0aW9uKFwiXCIpO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gyKGFwaSwge1xuICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgcHJvbXB0LFxuICAgICAgICAuLi5ib2R5XG4gICAgICB9KSxcbiAgICAgIGNyZWRlbnRpYWxzLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgLi4uaGVhZGVyc1xuICAgICAgfSxcbiAgICAgIHNpZ25hbDogYWJvcnRDb250cm9sbGVyLnNpZ25hbFxuICAgIH0pLmNhdGNoKChlcnIpID0+IHtcbiAgICAgIHRocm93IGVycjtcbiAgICB9KTtcbiAgICBpZiAob25SZXNwb25zZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgb25SZXNwb25zZShyZXNwb25zZSk7XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgdGhyb3cgZXJyO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIChfYSA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKSkgIT0gbnVsbCA/IF9hIDogXCJGYWlsZWQgdG8gZmV0Y2ggdGhlIGNoYXQgcmVzcG9uc2UuXCJcbiAgICAgICk7XG4gICAgfVxuICAgIGlmICghcmVzcG9uc2UuYm9keSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhlIHJlc3BvbnNlIGJvZHkgaXMgZW1wdHkuXCIpO1xuICAgIH1cbiAgICBsZXQgcmVzdWx0ID0gXCJcIjtcbiAgICBzd2l0Y2ggKHN0cmVhbVByb3RvY29sKSB7XG4gICAgICBjYXNlIFwidGV4dFwiOiB7XG4gICAgICAgIGF3YWl0IHByb2Nlc3NUZXh0U3RyZWFtKHtcbiAgICAgICAgICBzdHJlYW06IHJlc3BvbnNlLmJvZHksXG4gICAgICAgICAgb25UZXh0UGFydDogKGNodW5rKSA9PiB7XG4gICAgICAgICAgICByZXN1bHQgKz0gY2h1bms7XG4gICAgICAgICAgICBzZXRDb21wbGV0aW9uKHJlc3VsdCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBjYXNlIFwiZGF0YVwiOiB7XG4gICAgICAgIGF3YWl0IHByb2Nlc3NEYXRhU3RyZWFtKHtcbiAgICAgICAgICBzdHJlYW06IHJlc3BvbnNlLmJvZHksXG4gICAgICAgICAgb25UZXh0UGFydCh2YWx1ZSkge1xuICAgICAgICAgICAgcmVzdWx0ICs9IHZhbHVlO1xuICAgICAgICAgICAgc2V0Q29tcGxldGlvbihyZXN1bHQpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25EYXRhUGFydCh2YWx1ZSkge1xuICAgICAgICAgICAgb25EYXRhID09IG51bGwgPyB2b2lkIDAgOiBvbkRhdGEodmFsdWUpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25FcnJvclBhcnQodmFsdWUpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcih2YWx1ZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgYnJlYWs7XG4gICAgICB9XG4gICAgICBkZWZhdWx0OiB7XG4gICAgICAgIGNvbnN0IGV4aGF1c3RpdmVDaGVjayA9IHN0cmVhbVByb3RvY29sO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gc3RyZWFtIHByb3RvY29sOiAke2V4aGF1c3RpdmVDaGVja31gKTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKG9uRmluaXNoKSB7XG4gICAgICBvbkZpbmlzaChwcm9tcHQsIHJlc3VsdCk7XG4gICAgfVxuICAgIHNldEFib3J0Q29udHJvbGxlcihudWxsKTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9IGNhdGNoIChlcnIpIHtcbiAgICBpZiAoZXJyLm5hbWUgPT09IFwiQWJvcnRFcnJvclwiKSB7XG4gICAgICBzZXRBYm9ydENvbnRyb2xsZXIobnVsbCk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgaWYgKGVyciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICBpZiAob25FcnJvcikge1xuICAgICAgICBvbkVycm9yKGVycik7XG4gICAgICB9XG4gICAgfVxuICAgIHNldEVycm9yKGVycik7XG4gIH0gZmluYWxseSB7XG4gICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gIH1cbn1cblxuLy8gc3JjL2RhdGEtdXJsLnRzXG5mdW5jdGlvbiBnZXRUZXh0RnJvbURhdGFVcmwoZGF0YVVybCkge1xuICBjb25zdCBbaGVhZGVyLCBiYXNlNjRDb250ZW50XSA9IGRhdGFVcmwuc3BsaXQoXCIsXCIpO1xuICBjb25zdCBtaW1lVHlwZSA9IGhlYWRlci5zcGxpdChcIjtcIilbMF0uc3BsaXQoXCI6XCIpWzFdO1xuICBpZiAobWltZVR5cGUgPT0gbnVsbCB8fCBiYXNlNjRDb250ZW50ID09IG51bGwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIGRhdGEgVVJMIGZvcm1hdFwiKTtcbiAgfVxuICB0cnkge1xuICAgIHJldHVybiB3aW5kb3cuYXRvYihiYXNlNjRDb250ZW50KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm9yIGRlY29kaW5nIGRhdGEgVVJMYCk7XG4gIH1cbn1cblxuLy8gc3JjL2V4dHJhY3QtbWF4LXRvb2wtaW52b2NhdGlvbi1zdGVwLnRzXG5mdW5jdGlvbiBleHRyYWN0TWF4VG9vbEludm9jYXRpb25TdGVwKHRvb2xJbnZvY2F0aW9ucykge1xuICByZXR1cm4gdG9vbEludm9jYXRpb25zID09IG51bGwgPyB2b2lkIDAgOiB0b29sSW52b2NhdGlvbnMucmVkdWNlKChtYXgsIHRvb2xJbnZvY2F0aW9uKSA9PiB7XG4gICAgdmFyIF9hO1xuICAgIHJldHVybiBNYXRoLm1heChtYXgsIChfYSA9IHRvb2xJbnZvY2F0aW9uLnN0ZXApICE9IG51bGwgPyBfYSA6IDApO1xuICB9LCAwKTtcbn1cblxuLy8gc3JjL2dldC1tZXNzYWdlLXBhcnRzLnRzXG5mdW5jdGlvbiBnZXRNZXNzYWdlUGFydHMobWVzc2FnZSkge1xuICB2YXIgX2E7XG4gIHJldHVybiAoX2EgPSBtZXNzYWdlLnBhcnRzKSAhPSBudWxsID8gX2EgOiBbXG4gICAgLi4ubWVzc2FnZS50b29sSW52b2NhdGlvbnMgPyBtZXNzYWdlLnRvb2xJbnZvY2F0aW9ucy5tYXAoKHRvb2xJbnZvY2F0aW9uKSA9PiAoe1xuICAgICAgdHlwZTogXCJ0b29sLWludm9jYXRpb25cIixcbiAgICAgIHRvb2xJbnZvY2F0aW9uXG4gICAgfSkpIDogW10sXG4gICAgLi4ubWVzc2FnZS5yZWFzb25pbmcgPyBbXG4gICAgICB7XG4gICAgICAgIHR5cGU6IFwicmVhc29uaW5nXCIsXG4gICAgICAgIHJlYXNvbmluZzogbWVzc2FnZS5yZWFzb25pbmcsXG4gICAgICAgIGRldGFpbHM6IFt7IHR5cGU6IFwidGV4dFwiLCB0ZXh0OiBtZXNzYWdlLnJlYXNvbmluZyB9XVxuICAgICAgfVxuICAgIF0gOiBbXSxcbiAgICAuLi5tZXNzYWdlLmNvbnRlbnQgPyBbeyB0eXBlOiBcInRleHRcIiwgdGV4dDogbWVzc2FnZS5jb250ZW50IH1dIDogW11cbiAgXTtcbn1cblxuLy8gc3JjL2ZpbGwtbWVzc2FnZS1wYXJ0cy50c1xuZnVuY3Rpb24gZmlsbE1lc3NhZ2VQYXJ0cyhtZXNzYWdlcykge1xuICByZXR1cm4gbWVzc2FnZXMubWFwKChtZXNzYWdlKSA9PiAoe1xuICAgIC4uLm1lc3NhZ2UsXG4gICAgcGFydHM6IGdldE1lc3NhZ2VQYXJ0cyhtZXNzYWdlKVxuICB9KSk7XG59XG5cbi8vIHNyYy9pcy1kZWVwLWVxdWFsLWRhdGEudHNcbmZ1bmN0aW9uIGlzRGVlcEVxdWFsRGF0YShvYmoxLCBvYmoyKSB7XG4gIGlmIChvYmoxID09PSBvYmoyKVxuICAgIHJldHVybiB0cnVlO1xuICBpZiAob2JqMSA9PSBudWxsIHx8IG9iajIgPT0gbnVsbClcbiAgICByZXR1cm4gZmFsc2U7XG4gIGlmICh0eXBlb2Ygb2JqMSAhPT0gXCJvYmplY3RcIiAmJiB0eXBlb2Ygb2JqMiAhPT0gXCJvYmplY3RcIilcbiAgICByZXR1cm4gb2JqMSA9PT0gb2JqMjtcbiAgaWYgKG9iajEuY29uc3RydWN0b3IgIT09IG9iajIuY29uc3RydWN0b3IpXG4gICAgcmV0dXJuIGZhbHNlO1xuICBpZiAob2JqMSBpbnN0YW5jZW9mIERhdGUgJiYgb2JqMiBpbnN0YW5jZW9mIERhdGUpIHtcbiAgICByZXR1cm4gb2JqMS5nZXRUaW1lKCkgPT09IG9iajIuZ2V0VGltZSgpO1xuICB9XG4gIGlmIChBcnJheS5pc0FycmF5KG9iajEpKSB7XG4gICAgaWYgKG9iajEubGVuZ3RoICE9PSBvYmoyLmxlbmd0aClcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG9iajEubGVuZ3RoOyBpKyspIHtcbiAgICAgIGlmICghaXNEZWVwRXF1YWxEYXRhKG9iajFbaV0sIG9iajJbaV0pKVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIGNvbnN0IGtleXMxID0gT2JqZWN0LmtleXMob2JqMSk7XG4gIGNvbnN0IGtleXMyID0gT2JqZWN0LmtleXMob2JqMik7XG4gIGlmIChrZXlzMS5sZW5ndGggIT09IGtleXMyLmxlbmd0aClcbiAgICByZXR1cm4gZmFsc2U7XG4gIGZvciAoY29uc3Qga2V5IG9mIGtleXMxKSB7XG4gICAgaWYgKCFrZXlzMi5pbmNsdWRlcyhrZXkpKVxuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIGlmICghaXNEZWVwRXF1YWxEYXRhKG9iajFba2V5XSwgb2JqMltrZXldKSlcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICByZXR1cm4gdHJ1ZTtcbn1cblxuLy8gc3JjL3ByZXBhcmUtYXR0YWNobWVudHMtZm9yLXJlcXVlc3QudHNcbmFzeW5jIGZ1bmN0aW9uIHByZXBhcmVBdHRhY2htZW50c0ZvclJlcXVlc3QoYXR0YWNobWVudHNGcm9tT3B0aW9ucykge1xuICBpZiAoIWF0dGFjaG1lbnRzRnJvbU9wdGlvbnMpIHtcbiAgICByZXR1cm4gW107XG4gIH1cbiAgaWYgKGdsb2JhbFRoaXMuRmlsZUxpc3QgJiYgYXR0YWNobWVudHNGcm9tT3B0aW9ucyBpbnN0YW5jZW9mIGdsb2JhbFRoaXMuRmlsZUxpc3QpIHtcbiAgICByZXR1cm4gUHJvbWlzZS5hbGwoXG4gICAgICBBcnJheS5mcm9tKGF0dGFjaG1lbnRzRnJvbU9wdGlvbnMpLm1hcChhc3luYyAoYXR0YWNobWVudCkgPT4ge1xuICAgICAgICBjb25zdCB7IG5hbWUsIHR5cGUgfSA9IGF0dGFjaG1lbnQ7XG4gICAgICAgIGNvbnN0IGRhdGFVcmwgPSBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTtcbiAgICAgICAgICByZWFkZXIub25sb2FkID0gKHJlYWRlckV2ZW50KSA9PiB7XG4gICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICByZXNvbHZlKChfYSA9IHJlYWRlckV2ZW50LnRhcmdldCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnJlc3VsdCk7XG4gICAgICAgICAgfTtcbiAgICAgICAgICByZWFkZXIub25lcnJvciA9IChlcnJvcikgPT4gcmVqZWN0KGVycm9yKTtcbiAgICAgICAgICByZWFkZXIucmVhZEFzRGF0YVVSTChhdHRhY2htZW50KTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgbmFtZSxcbiAgICAgICAgICBjb250ZW50VHlwZTogdHlwZSxcbiAgICAgICAgICB1cmw6IGRhdGFVcmxcbiAgICAgICAgfTtcbiAgICAgIH0pXG4gICAgKTtcbiAgfVxuICBpZiAoQXJyYXkuaXNBcnJheShhdHRhY2htZW50c0Zyb21PcHRpb25zKSkge1xuICAgIHJldHVybiBhdHRhY2htZW50c0Zyb21PcHRpb25zO1xuICB9XG4gIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgYXR0YWNobWVudHMgdHlwZVwiKTtcbn1cblxuLy8gc3JjL3Byb2Nlc3MtYXNzaXN0YW50LXN0cmVhbS50c1xudmFyIE5FV0xJTkUyID0gXCJcXG5cIi5jaGFyQ29kZUF0KDApO1xuZnVuY3Rpb24gY29uY2F0Q2h1bmtzMihjaHVua3MsIHRvdGFsTGVuZ3RoKSB7XG4gIGNvbnN0IGNvbmNhdGVuYXRlZENodW5rcyA9IG5ldyBVaW50OEFycmF5KHRvdGFsTGVuZ3RoKTtcbiAgbGV0IG9mZnNldCA9IDA7XG4gIGZvciAoY29uc3QgY2h1bmsgb2YgY2h1bmtzKSB7XG4gICAgY29uY2F0ZW5hdGVkQ2h1bmtzLnNldChjaHVuaywgb2Zmc2V0KTtcbiAgICBvZmZzZXQgKz0gY2h1bmsubGVuZ3RoO1xuICB9XG4gIGNodW5rcy5sZW5ndGggPSAwO1xuICByZXR1cm4gY29uY2F0ZW5hdGVkQ2h1bmtzO1xufVxuYXN5bmMgZnVuY3Rpb24gcHJvY2Vzc0Fzc2lzdGFudFN0cmVhbSh7XG4gIHN0cmVhbSxcbiAgb25UZXh0UGFydCxcbiAgb25FcnJvclBhcnQsXG4gIG9uQXNzaXN0YW50TWVzc2FnZVBhcnQsXG4gIG9uQXNzaXN0YW50Q29udHJvbERhdGFQYXJ0LFxuICBvbkRhdGFNZXNzYWdlUGFydFxufSkge1xuICBjb25zdCByZWFkZXIgPSBzdHJlYW0uZ2V0UmVhZGVyKCk7XG4gIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTtcbiAgY29uc3QgY2h1bmtzID0gW107XG4gIGxldCB0b3RhbExlbmd0aCA9IDA7XG4gIHdoaWxlICh0cnVlKSB7XG4gICAgY29uc3QgeyB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTtcbiAgICBpZiAodmFsdWUpIHtcbiAgICAgIGNodW5rcy5wdXNoKHZhbHVlKTtcbiAgICAgIHRvdGFsTGVuZ3RoICs9IHZhbHVlLmxlbmd0aDtcbiAgICAgIGlmICh2YWx1ZVt2YWx1ZS5sZW5ndGggLSAxXSAhPT0gTkVXTElORTIpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChjaHVua3MubGVuZ3RoID09PSAwKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgY29uc3QgY29uY2F0ZW5hdGVkQ2h1bmtzID0gY29uY2F0Q2h1bmtzMihjaHVua3MsIHRvdGFsTGVuZ3RoKTtcbiAgICB0b3RhbExlbmd0aCA9IDA7XG4gICAgY29uc3Qgc3RyZWFtUGFydHMgPSBkZWNvZGVyLmRlY29kZShjb25jYXRlbmF0ZWRDaHVua3MsIHsgc3RyZWFtOiB0cnVlIH0pLnNwbGl0KFwiXFxuXCIpLmZpbHRlcigobGluZSkgPT4gbGluZSAhPT0gXCJcIikubWFwKHBhcnNlQXNzaXN0YW50U3RyZWFtUGFydCk7XG4gICAgZm9yIChjb25zdCB7IHR5cGUsIHZhbHVlOiB2YWx1ZTIgfSBvZiBzdHJlYW1QYXJ0cykge1xuICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgIGNhc2UgXCJ0ZXh0XCI6XG4gICAgICAgICAgYXdhaXQgKG9uVGV4dFBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uVGV4dFBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJlcnJvclwiOlxuICAgICAgICAgIGF3YWl0IChvbkVycm9yUGFydCA9PSBudWxsID8gdm9pZCAwIDogb25FcnJvclBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJhc3Npc3RhbnRfbWVzc2FnZVwiOlxuICAgICAgICAgIGF3YWl0IChvbkFzc2lzdGFudE1lc3NhZ2VQYXJ0ID09IG51bGwgPyB2b2lkIDAgOiBvbkFzc2lzdGFudE1lc3NhZ2VQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwiYXNzaXN0YW50X2NvbnRyb2xfZGF0YVwiOlxuICAgICAgICAgIGF3YWl0IChvbkFzc2lzdGFudENvbnRyb2xEYXRhUGFydCA9PSBudWxsID8gdm9pZCAwIDogb25Bc3Npc3RhbnRDb250cm9sRGF0YVBhcnQodmFsdWUyKSk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJkYXRhX21lc3NhZ2VcIjpcbiAgICAgICAgICBhd2FpdCAob25EYXRhTWVzc2FnZVBhcnQgPT0gbnVsbCA/IHZvaWQgMCA6IG9uRGF0YU1lc3NhZ2VQYXJ0KHZhbHVlMikpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgY29uc3QgZXhoYXVzdGl2ZUNoZWNrID0gdHlwZTtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gc3RyZWFtIHBhcnQgdHlwZTogJHtleGhhdXN0aXZlQ2hlY2t9YCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gc3JjL3NjaGVtYS50c1xuaW1wb3J0IHsgdmFsaWRhdG9yU3ltYm9sIH0gZnJvbSBcIkBhaS1zZGsvcHJvdmlkZXItdXRpbHNcIjtcblxuLy8gc3JjL3pvZC1zY2hlbWEudHNcbmltcG9ydCB6b2RUb0pzb25TY2hlbWEgZnJvbSBcInpvZC10by1qc29uLXNjaGVtYVwiO1xuZnVuY3Rpb24gem9kU2NoZW1hKHpvZFNjaGVtYTIsIG9wdGlvbnMpIHtcbiAgdmFyIF9hO1xuICBjb25zdCB1c2VSZWZlcmVuY2VzID0gKF9hID0gb3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy51c2VSZWZlcmVuY2VzKSAhPSBudWxsID8gX2EgOiBmYWxzZTtcbiAgcmV0dXJuIGpzb25TY2hlbWEoXG4gICAgem9kVG9Kc29uU2NoZW1hKHpvZFNjaGVtYTIsIHtcbiAgICAgICRyZWZTdHJhdGVneTogdXNlUmVmZXJlbmNlcyA/IFwicm9vdFwiIDogXCJub25lXCIsXG4gICAgICB0YXJnZXQ6IFwianNvblNjaGVtYTdcIlxuICAgICAgLy8gbm90ZTogb3BlbmFpIG1vZGUgYnJlYWtzIHZhcmlvdXMgZ2VtaW5pIGNvbnZlcnNpb25zXG4gICAgfSksXG4gICAge1xuICAgICAgdmFsaWRhdGU6ICh2YWx1ZSkgPT4ge1xuICAgICAgICBjb25zdCByZXN1bHQgPSB6b2RTY2hlbWEyLnNhZmVQYXJzZSh2YWx1ZSk7XG4gICAgICAgIHJldHVybiByZXN1bHQuc3VjY2VzcyA/IHsgc3VjY2VzczogdHJ1ZSwgdmFsdWU6IHJlc3VsdC5kYXRhIH0gOiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogcmVzdWx0LmVycm9yIH07XG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vLyBzcmMvc2NoZW1hLnRzXG52YXIgc2NoZW1hU3ltYm9sID0gU3ltYm9sLmZvcihcInZlcmNlbC5haS5zY2hlbWFcIik7XG5mdW5jdGlvbiBqc29uU2NoZW1hKGpzb25TY2hlbWEyLCB7XG4gIHZhbGlkYXRlXG59ID0ge30pIHtcbiAgcmV0dXJuIHtcbiAgICBbc2NoZW1hU3ltYm9sXTogdHJ1ZSxcbiAgICBfdHlwZTogdm9pZCAwLFxuICAgIC8vIHNob3VsZCBuZXZlciBiZSB1c2VkIGRpcmVjdGx5XG4gICAgW3ZhbGlkYXRvclN5bWJvbF06IHRydWUsXG4gICAganNvblNjaGVtYToganNvblNjaGVtYTIsXG4gICAgdmFsaWRhdGVcbiAgfTtcbn1cbmZ1bmN0aW9uIGlzU2NoZW1hKHZhbHVlKSB7XG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT09IFwib2JqZWN0XCIgJiYgdmFsdWUgIT09IG51bGwgJiYgc2NoZW1hU3ltYm9sIGluIHZhbHVlICYmIHZhbHVlW3NjaGVtYVN5bWJvbF0gPT09IHRydWUgJiYgXCJqc29uU2NoZW1hXCIgaW4gdmFsdWUgJiYgXCJ2YWxpZGF0ZVwiIGluIHZhbHVlO1xufVxuZnVuY3Rpb24gYXNTY2hlbWEoc2NoZW1hKSB7XG4gIHJldHVybiBpc1NjaGVtYShzY2hlbWEpID8gc2NoZW1hIDogem9kU2NoZW1hKHNjaGVtYSk7XG59XG5cbi8vIHNyYy9zaG91bGQtcmVzdWJtaXQtbWVzc2FnZXMudHNcbmZ1bmN0aW9uIHNob3VsZFJlc3VibWl0TWVzc2FnZXMoe1xuICBvcmlnaW5hbE1heFRvb2xJbnZvY2F0aW9uU3RlcCxcbiAgb3JpZ2luYWxNZXNzYWdlQ291bnQsXG4gIG1heFN0ZXBzLFxuICBtZXNzYWdlc1xufSkge1xuICB2YXIgX2E7XG4gIGNvbnN0IGxhc3RNZXNzYWdlID0gbWVzc2FnZXNbbWVzc2FnZXMubGVuZ3RoIC0gMV07XG4gIHJldHVybiAoXG4gICAgLy8gY2hlY2sgaWYgdGhlIGZlYXR1cmUgaXMgZW5hYmxlZDpcbiAgICBtYXhTdGVwcyA+IDEgJiYgLy8gZW5zdXJlIHRoZXJlIGlzIGEgbGFzdCBtZXNzYWdlOlxuICAgIGxhc3RNZXNzYWdlICE9IG51bGwgJiYgLy8gZW5zdXJlIHdlIGFjdHVhbGx5IGhhdmUgbmV3IHN0ZXBzICh0byBwcmV2ZW50IGluZmluaXRlIGxvb3BzIGluIGNhc2Ugb2YgZXJyb3JzKTpcbiAgICAobWVzc2FnZXMubGVuZ3RoID4gb3JpZ2luYWxNZXNzYWdlQ291bnQgfHwgZXh0cmFjdE1heFRvb2xJbnZvY2F0aW9uU3RlcChsYXN0TWVzc2FnZS50b29sSW52b2NhdGlvbnMpICE9PSBvcmlnaW5hbE1heFRvb2xJbnZvY2F0aW9uU3RlcCkgJiYgLy8gY2hlY2sgdGhhdCBuZXh0IHN0ZXAgaXMgcG9zc2libGU6XG4gICAgaXNBc3Npc3RhbnRNZXNzYWdlV2l0aENvbXBsZXRlZFRvb2xDYWxscyhsYXN0TWVzc2FnZSkgJiYgLy8gbGltaXQgdGhlIG51bWJlciBvZiBhdXRvbWF0aWMgc3RlcHM6XG4gICAgKChfYSA9IGV4dHJhY3RNYXhUb29sSW52b2NhdGlvblN0ZXAobGFzdE1lc3NhZ2UudG9vbEludm9jYXRpb25zKSkgIT0gbnVsbCA/IF9hIDogMCkgPCBtYXhTdGVwc1xuICApO1xufVxuZnVuY3Rpb24gaXNBc3Npc3RhbnRNZXNzYWdlV2l0aENvbXBsZXRlZFRvb2xDYWxscyhtZXNzYWdlKSB7XG4gIGlmIChtZXNzYWdlLnJvbGUgIT09IFwiYXNzaXN0YW50XCIpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbiAgY29uc3QgbGFzdFN0ZXBTdGFydEluZGV4ID0gbWVzc2FnZS5wYXJ0cy5yZWR1Y2UoKGxhc3RJbmRleCwgcGFydCwgaW5kZXgpID0+IHtcbiAgICByZXR1cm4gcGFydC50eXBlID09PSBcInN0ZXAtc3RhcnRcIiA/IGluZGV4IDogbGFzdEluZGV4O1xuICB9LCAtMSk7XG4gIGNvbnN0IGxhc3RTdGVwVG9vbEludm9jYXRpb25zID0gbWVzc2FnZS5wYXJ0cy5zbGljZShsYXN0U3RlcFN0YXJ0SW5kZXggKyAxKS5maWx0ZXIoKHBhcnQpID0+IHBhcnQudHlwZSA9PT0gXCJ0b29sLWludm9jYXRpb25cIik7XG4gIHJldHVybiBsYXN0U3RlcFRvb2xJbnZvY2F0aW9ucy5sZW5ndGggPiAwICYmIGxhc3RTdGVwVG9vbEludm9jYXRpb25zLmV2ZXJ5KChwYXJ0KSA9PiBcInJlc3VsdFwiIGluIHBhcnQudG9vbEludm9jYXRpb24pO1xufVxuXG4vLyBzcmMvdXBkYXRlLXRvb2wtY2FsbC1yZXN1bHQudHNcbmZ1bmN0aW9uIHVwZGF0ZVRvb2xDYWxsUmVzdWx0KHtcbiAgbWVzc2FnZXMsXG4gIHRvb2xDYWxsSWQsXG4gIHRvb2xSZXN1bHQ6IHJlc3VsdFxufSkge1xuICB2YXIgX2E7XG4gIGNvbnN0IGxhc3RNZXNzYWdlID0gbWVzc2FnZXNbbWVzc2FnZXMubGVuZ3RoIC0gMV07XG4gIGNvbnN0IGludm9jYXRpb25QYXJ0ID0gbGFzdE1lc3NhZ2UucGFydHMuZmluZChcbiAgICAocGFydCkgPT4gcGFydC50eXBlID09PSBcInRvb2wtaW52b2NhdGlvblwiICYmIHBhcnQudG9vbEludm9jYXRpb24udG9vbENhbGxJZCA9PT0gdG9vbENhbGxJZFxuICApO1xuICBpZiAoaW52b2NhdGlvblBhcnQgPT0gbnVsbCkge1xuICAgIHJldHVybjtcbiAgfVxuICBjb25zdCB0b29sUmVzdWx0ID0ge1xuICAgIC4uLmludm9jYXRpb25QYXJ0LnRvb2xJbnZvY2F0aW9uLFxuICAgIHN0YXRlOiBcInJlc3VsdFwiLFxuICAgIHJlc3VsdFxuICB9O1xuICBpbnZvY2F0aW9uUGFydC50b29sSW52b2NhdGlvbiA9IHRvb2xSZXN1bHQ7XG4gIGxhc3RNZXNzYWdlLnRvb2xJbnZvY2F0aW9ucyA9IChfYSA9IGxhc3RNZXNzYWdlLnRvb2xJbnZvY2F0aW9ucykgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLm1hcChcbiAgICAodG9vbEludm9jYXRpb24pID0+IHRvb2xJbnZvY2F0aW9uLnRvb2xDYWxsSWQgPT09IHRvb2xDYWxsSWQgPyB0b29sUmVzdWx0IDogdG9vbEludm9jYXRpb25cbiAgKTtcbn1cbmV4cG9ydCB7XG4gIGFzU2NoZW1hLFxuICBjYWxsQ2hhdEFwaSxcbiAgY2FsbENvbXBsZXRpb25BcGksXG4gIGV4dHJhY3RNYXhUb29sSW52b2NhdGlvblN0ZXAsXG4gIGZpbGxNZXNzYWdlUGFydHMsXG4gIGZvcm1hdEFzc2lzdGFudFN0cmVhbVBhcnQsXG4gIGZvcm1hdERhdGFTdHJlYW1QYXJ0LFxuICBnZW5lcmF0ZUlkLFxuICBnZXRNZXNzYWdlUGFydHMsXG4gIGdldFRleHRGcm9tRGF0YVVybCxcbiAgaXNBc3Npc3RhbnRNZXNzYWdlV2l0aENvbXBsZXRlZFRvb2xDYWxscyxcbiAgaXNEZWVwRXF1YWxEYXRhLFxuICBqc29uU2NoZW1hLFxuICBwYXJzZUFzc2lzdGFudFN0cmVhbVBhcnQsXG4gIHBhcnNlRGF0YVN0cmVhbVBhcnQsXG4gIHBhcnNlUGFydGlhbEpzb24sXG4gIHByZXBhcmVBdHRhY2htZW50c0ZvclJlcXVlc3QsXG4gIHByb2Nlc3NBc3Npc3RhbnRTdHJlYW0sXG4gIHByb2Nlc3NEYXRhU3RyZWFtLFxuICBwcm9jZXNzVGV4dFN0cmVhbSxcbiAgc2hvdWxkUmVzdWJtaXRNZXNzYWdlcyxcbiAgdXBkYXRlVG9vbENhbGxSZXN1bHQsXG4gIHpvZFNjaGVtYVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.11_zod@3.25.76/node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;
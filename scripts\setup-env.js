#!/usr/bin/env node

/**
 * Setup script to help configure environment variables
 * Run with: node scripts/setup-env.js
 */

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve)
  })
}

async function setupEnvironment() {
  console.log('🚀 ChessLLM Environment Setup\n')
  console.log('This script will help you configure your environment variables.\n')

  // Check if .env.local already exists
  const envPath = path.join(process.cwd(), '.env.local')
  if (fs.existsSync(envPath)) {
    const overwrite = await question('⚠️  .env.local already exists. Overwrite? (y/N): ')
    if (overwrite.toLowerCase() !== 'y') {
      console.log('Setup cancelled.')
      rl.close()
      return
    }
  }

  console.log('📋 Please provide your Supabase configuration:')
  console.log('   (Get these from your Supabase project settings)\n')

  const supabaseUrl = await question('🔗 Supabase URL (https://your-project.supabase.co): ')
  const supabaseAnonKey = await question('🔑 Supabase Anon Key: ')
  const supabaseServiceKey = await question('🔐 Supabase Service Role Key: ')

  console.log('\n📋 Optional: LLM API Keys (press Enter to skip)')
  console.log('   (Required for premium models, some free models need OpenRouter)\n')

  const geminiKey = await question('🤖 Google Gemini API Key (optional): ')
  const openrouterKey = await question('🌐 OpenRouter API Key (recommended): ')

  // Create .env.local content
  const envContent = `# Supabase Configuration (Required)
NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${supabaseAnonKey}
SUPABASE_SERVICE_ROLE_KEY=${supabaseServiceKey}

# LLM API Keys (Optional)
${geminiKey ? `GOOGLE_GENERATIVE_AI_API_KEY=${geminiKey}` : '# GOOGLE_GENERATIVE_AI_API_KEY=your_gemini_key_here'}
${openrouterKey ? `OPENROUTER_API_KEY=${openrouterKey}` : '# OPENROUTER_API_KEY=your_openrouter_key_here'}

# Development
NODE_ENV=development
`

  // Write .env.local file
  try {
    fs.writeFileSync(envPath, envContent)
    console.log('\n✅ Environment variables saved to .env.local')
  } catch (error) {
    console.error('\n❌ Failed to write .env.local:', error.message)
    rl.close()
    return
  }

  console.log('\n🎯 Next Steps:')
  console.log('1. Create database tables:')
  console.log('   - Open your Supabase SQL editor')
  console.log('   - Run the SQL commands in scripts/create-tables.sql')
  console.log('2. Start the development server:')
  console.log('   - pnpm dev')
  console.log('3. Open http://localhost:3000 and create your first chess battle!')

  if (!openrouterKey) {
    console.log('\n💡 Tip: Get a free OpenRouter API key at https://openrouter.ai/keys')
    console.log('   This will give you access to many free models like DeepSeek R1!')
  }

  rl.close()
}

// Run the setup
setupEnvironment().catch(console.error)

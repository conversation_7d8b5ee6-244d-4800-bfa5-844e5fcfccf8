"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-find-source-map-url.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-find-source-map-url.js ***!
  \**************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-link-gc.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-link-gc.js ***!
  \**************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router.js ***!
  \************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _usereducer = __webpack_require__(/*! ./use-reducer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/use-reducer.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _appcallserver = __webpack_require__(/*! ../app-call-server */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-call-server.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/segment-cache.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/redirect-error.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null\n    };\n}\n/**\n * Server response that only patches the cache and tree.\n */ function useChangeByServerResponse(dispatch) {\n    return (0, _react.useCallback)((param)=>{\n        let { previousTree, serverResponse } = param;\n        (0, _react.startTransition)(()=>{\n            dispatch({\n                type: _routerreducertypes.ACTION_SERVER_PATCH,\n                previousTree,\n                serverResponse\n            });\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction useNavigate(dispatch) {\n    return (0, _react.useCallback)((href, navigateType, shouldScroll)=>{\n        const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n        if (false) {}\n        return dispatch({\n            type: _routerreducertypes.ACTION_NAVIGATE,\n            url,\n            isExternalUrl: isExternalURL(url),\n            locationSearch: location.search,\n            shouldScroll: shouldScroll != null ? shouldScroll : true,\n            navigateType,\n            allowAliasing: true\n        });\n    }, [\n        dispatch\n    ]);\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    _s();\n    let { actionQueue, assetPrefix, globalError } = param;\n    const [state, dispatch] = (0, _usereducer.useReducer)(actionQueue);\n    const { canonicalUrl } = (0, _usereducer.useUnwrapState)(state);\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    const changeByServerResponse = useChangeByServerResponse(dispatch);\n    const navigate = useNavigate(dispatch);\n    (0, _appcallserver.useServerActionDispatcher)(dispatch);\n    /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */ const appRouter = (0, _react.useMemo)(()=>{\n        const routerInstance = {\n            back: ()=>window.history.back(),\n            forward: ()=>window.history.forward(),\n            prefetch:  false ? // cache. So we don't need to dispatch an action.\n            0 : (href, options)=>{\n                // Use the old prefetch implementation.\n                const url = createPrefetchURL(href);\n                if (url !== null) {\n                    var _options_kind;\n                    // The prefetch reducer doesn't actually update any state or\n                    // trigger a rerender. It just writes to a mutable cache. So we\n                    // shouldn't bother calling setState/dispatch; we can just re-run\n                    // the reducer directly using the current state.\n                    // TODO: Refactor this away from a \"reducer\" so it's\n                    // less confusing.\n                    (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                        type: _routerreducertypes.ACTION_PREFETCH,\n                        url,\n                        kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n                    });\n                }\n            },\n            replace: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'replace', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            push: (href, options)=>{\n                if (options === void 0) options = {};\n                (0, _react.startTransition)(()=>{\n                    var _options_scroll;\n                    navigate(href, 'push', (_options_scroll = options.scroll) != null ? _options_scroll : true);\n                });\n            },\n            refresh: ()=>{\n                (0, _react.startTransition)(()=>{\n                    dispatch({\n                        type: _routerreducertypes.ACTION_REFRESH,\n                        origin: window.location.origin\n                    });\n                });\n            },\n            hmrRefresh: ()=>{\n                if (false) {} else {\n                    (0, _react.startTransition)(()=>{\n                        dispatch({\n                            type: _routerreducertypes.ACTION_HMR_REFRESH,\n                            origin: window.location.origin\n                        });\n                    });\n                }\n            }\n        };\n        return routerInstance;\n    }, [\n        actionQueue,\n        dispatch,\n        navigate\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Exists for debugging purposes. Don't use in application code.\n        if (window.next) {\n            window.next.router = appRouter;\n        }\n    }, [\n        appRouter\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = (0, _usereducer.useUnwrapState)(state);\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: appRouter,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            appRouter,\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            dispatch({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, [\n        dispatch\n    ]);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    appRouter.push(url, {});\n                } else {\n                    appRouter.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, [\n        appRouter\n    ]);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = (0, _usereducer.useUnwrapState)(state);\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location1 = window.location;\n            if (pushRef.pendingPush) {\n                location1.assign(canonicalUrl);\n            } else {\n                location1.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                dispatch({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(window.location.href),\n                    tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n                });\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, [\n        dispatch\n    ]);\n    const { cache, tree, nextUrl, focusAndScrollRef } = (0, _usereducer.useUnwrapState)(state);\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            changeByServerResponse,\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        changeByServerResponse,\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: (0, _usereducer.useUnwrapState)(state)\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: appRouter,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_s(Router, \"bU8t8nCPb2ycaFr1siwKA2Gych0=\", false, function() {\n    return [\n        useChangeByServerResponse,\n        useNavigate\n    ];\n});\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s1();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s1(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleClientError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args, true);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/links.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/links.js ***!
  \*******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    unmountLinkInstance: function() {\n        return unmountLinkInstance;\n    }\n});\nconst _actionqueue = __webpack_require__(/*! ../../shared/lib/router/action-queue */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/router/action-queue.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/segment-cache.js\");\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction mountLinkInstance(element, href, router, kind) {\n    let prefetchUrl = null;\n    try {\n        prefetchUrl = (0, _approuter.createPrefetchURL)(href);\n        if (prefetchUrl === null) {\n            // We only track the link if it's prefetchable. For example, this excludes\n            // links to external URLs.\n            return;\n        }\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return;\n    }\n    const instance = {\n        prefetchHref: prefetchUrl.href,\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1\n    };\n    const existingInstance = links.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountLinkInstance(element);\n    }\n    links.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction unmountLinkInstance(element) {\n    const instance = links.get(element);\n    if (instance !== undefined) {\n        links.delete(element);\n        visibleLinks.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        visibleLinks.add(instance);\n    } else {\n        visibleLinks.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element) {\n    const instance = links.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with bumpPrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    if (existingPrefetchTask === null) {\n        // Initiate a prefetch task.\n        const appRouterState = (0, _actionqueue.getCurrentAppRouterState)();\n        if (appRouterState !== null) {\n            const nextUrl = appRouterState.nextUrl;\n            const treeAtTimeOfPrefetch = appRouterState.tree;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n            instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n        }\n    } else {\n        // We already have an old task object that we can reschedule. This is\n        // effectively the same as canceling the old task and creating a new one.\n        (0, _segmentcache.bumpPrefetchTask)(existingPrefetchTask, priority);\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of visibleLinks){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/is-next-router-error.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors() {\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError, []);\n            }\n        }, [\n            ssrError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {}),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                state: state,\n                isErrorOverlayOpen: isErrorOverlayOpen,\n                setIsErrorOverlayOpen: setIsErrorOverlayOpen\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useSyncDevRenderIndicator\", ({\n    enumerable: true,\n    get: function() {\n        return useSyncDevRenderIndicator;\n    }\n}));\nconst NOOP = (fn)=>fn();\n_c = NOOP;\nconst useSyncDevRenderIndicator = ()=>{\n    let syncDevRenderIndicator = NOOP;\n    if (true) {\n        const { useSyncDevRenderIndicatorInternal } = __webpack_require__(/*! ./use-sync-dev-render-indicator-internal */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator-internal.js\");\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        syncDevRenderIndicator = useSyncDevRenderIndicatorInternal();\n    }\n    return syncDevRenderIndicator;\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-sync-dev-render-indicator.js.map\nvar _c;\n$RefreshReg$(_c, \"NOOP\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeader = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeader !== null ? parseInt(staleTimeHeader, 10) : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority, signal) {\n    const fetchUrl = new URL(url);\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"onRecoverableError\", ({\n    enumerable: true,\n    get: function() {\n        return onRecoverableError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/lib/is-error.js\"));\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    const cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    const stitchedError = (0, _stitchederror.getReactStitchedError)(cause);\n    // In development mode, pass along the component stack to the error\n    if ( true && errorInfo.componentStack) {\n        ;\n        stitchedError._componentStack = errorInfo.componentStack;\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    (0, _reportglobalerror.reportGlobalError)(stitchedError);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjIuNF9Ab3BlbnRlbGVtZXRyeStfYjEzMTFhNzAwNWIwODQ4YTJjZmI4YTI1MzA2NjZhNTgvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2hvb2tzLWNsaWVudC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQU9hQSxpQkFBaUI7ZUFBakJBOztJQURBQyxlQUFlO2VBQWZBOztJQURBQyxtQkFBbUI7ZUFBbkJBOzs7bUNBSGlCO0FBR3ZCLE1BQU1BLHNCQUFzQkMsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBc0M7QUFDbEUsTUFBTUYsa0JBQWtCRSxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUNyRCxNQUFNSCxvQkFBb0JHLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQTZCO0FBRTlELElBQUlDLElBQW9CLEVBQW1CO0lBQ3pDRixvQkFBb0JLLFdBQVcsR0FBRztJQUNsQ04sZ0JBQWdCTSxXQUFXLEdBQUc7SUFDOUJQLGtCQUFrQk8sV0FBVyxHQUFHO0FBQ2xDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlbmRvYXJzYW5kaVxcc3JjXFxzaGFyZWRcXGxpYlxcaG9va3MtY2xpZW50LWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB0eXBlIHsgUGFyYW1zIH0gZnJvbSAnLi4vLi4vc2VydmVyL3JlcXVlc3QvcGFyYW1zJ1xuXG5leHBvcnQgY29uc3QgU2VhcmNoUGFyYW1zQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VVJMU2VhcmNoUGFyYW1zIHwgbnVsbD4obnVsbClcbmV4cG9ydCBjb25zdCBQYXRobmFtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PHN0cmluZyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFBhcmFtcyB8IG51bGw+KG51bGwpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFNlYXJjaFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnU2VhcmNoUGFyYW1zQ29udGV4dCdcbiAgUGF0aG5hbWVDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhuYW1lQ29udGV4dCdcbiAgUGF0aFBhcmFtc0NvbnRleHQuZGlzcGxheU5hbWUgPSAnUGF0aFBhcmFtc0NvbnRleHQnXG59XG4iXSwibmFtZXMiOlsiUGF0aFBhcmFtc0NvbnRleHQiLCJQYXRobmFtZUNvbnRleHQiLCJTZWFyY2hQYXJhbXNDb250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+_b1311a7005b0848a2cfb8a2530666a58/node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});
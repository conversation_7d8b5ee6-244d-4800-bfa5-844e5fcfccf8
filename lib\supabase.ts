import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      games: {
        Row: {
          id: string
          created_at: string
          white_player_llm: string
          black_player_llm: string
          fen: string
          status: "in_progress" | "completed" | "resigned" | "draw"
          winner: string | null
          current_turn: "white" | "black"
          white_illegal_attempts: number
          black_illegal_attempts: number
        }
        Insert: {
          id?: string
          created_at?: string
          white_player_llm?: string
          black_player_llm?: string
          fen?: string
          status?: "in_progress" | "completed" | "resigned" | "draw"
          winner?: string | null
          current_turn?: "white" | "black"
          white_illegal_attempts?: number
          black_illegal_attempts?: number
        }
        Update: {
          id?: string
          created_at?: string
          white_player_llm?: string
          black_player_llm?: string
          fen?: string
          status?: "in_progress" | "completed" | "resigned" | "draw"
          winner?: string | null
          current_turn?: "white" | "black"
          white_illegal_attempts?: number
          black_illegal_attempts?: number
        }
      }
      moves: {
        Row: {
          id: string
          game_id: string
          move_number: number
          player_color: "white" | "black"
          move_pgn: string
          move_san: string
          llm_thinking_process: string | null
          created_at: string
        }
        Insert: {
          id?: string
          game_id: string
          move_number: number
          player_color: "white" | "black"
          move_pgn: string
          move_san: string
          llm_thinking_process?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          game_id?: string
          move_number?: number
          player_color?: "white" | "black"
          move_pgn?: string
          move_san?: string
          llm_thinking_process?: string | null
          created_at?: string
        }
      }
    }
  }
}

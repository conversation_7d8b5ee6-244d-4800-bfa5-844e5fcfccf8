import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { Chess } from "chess.js"
import { google } from "@ai-sdk/google"
import { generateObject } from "ai"
import { z } from "zod"

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!)

const moveSchema = z.object({
  move: z.string().describe("The chess move in standard algebraic notation (e.g., e4, Nf3, O-O)"),
  thinking: z.string().describe("Your detailed thought process for choosing this move"),
  evaluation: z.number().describe("Position evaluation from -10 to +10 (positive favors white)"),
})

const MAX_ILLEGAL_ATTEMPTS = 5

export async function POST(req: Request) {
  try {
    const { gameId } = await req.json()

    if (!gameId) {
      return NextResponse.json({ error: "Game ID is required" }, { status: 400 })
    }

    // Get current game state
    const { data: game, error: gameError } = await supabase.from("games").select("*").eq("id", gameId).single()

    if (gameError || !game) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 })
    }

    if (game.status !== "in_progress") {
      return NextResponse.json({ error: "Game is not in progress" }, { status: 400 })
    }

    // Initialize chess engine with current position
    const chess = new Chess(game.fen)

    // Get move history for context
    const { data: moves } = await supabase
      .from("moves")
      .select("*")
      .eq("game_id", gameId)
      .order("move_number", { ascending: true })

    const moveHistory = moves?.map((m) => m.move_san).join(" ") || ""
    const currentPlayer = game.current_turn
    const currentLLM = currentPlayer === "white" ? game.white_player_llm : game.black_player_llm
    const illegalAttempts = currentPlayer === "white" ? game.white_illegal_attempts : game.black_illegal_attempts

    console.log(`Processing move for ${currentPlayer} using ${currentLLM}`)

    // Check if player has exceeded max attempts
    if (illegalAttempts >= MAX_ILLEGAL_ATTEMPTS) {
      const winner = currentPlayer === "white" ? "black" : "white"

      await supabase
        .from("games")
        .update({
          status: "resigned",
          winner: winner,
        })
        .eq("id", gameId)

      return NextResponse.json({
        message: `${currentPlayer} resigned after ${MAX_ILLEGAL_ATTEMPTS} illegal move attempts`,
        winner,
      })
    }

    // Attempt to get a legal move (with retry logic)
    let attempt = 0
    let successfulMove = false

    while (attempt < MAX_ILLEGAL_ATTEMPTS && !successfulMove) {
      attempt++
      console.log(`Attempt ${attempt} for ${currentPlayer} (${currentLLM})`)

      try {
        // Create prompt based on attempt number
        let prompt = createPrompt(currentPlayer, currentLLM, game.fen, moveHistory, chess.moves())

        if (attempt > 1) {
          prompt += `\n\nIMPORTANT: Your previous ${attempt - 1} attempt(s) resulted in illegal moves. Please be extra careful and only choose from the legal moves listed above.`
        }

        // Get AI response based on the model
        let result
        if (currentLLM.includes("gemini")) {
          console.log(`Calling Gemini with model: ${currentLLM}`)
          // Use Google AI for Gemini - try multiple model variations
          const modelVariations = [currentLLM, "gemini-1.5-flash", "gemini-1.5-pro", "gemini-pro"]

          let geminiError = null
          for (const modelId of modelVariations) {
            try {
              console.log(`Trying Gemini model: ${modelId}`)
              result = await generateObject({
                model: google(modelId, {
                  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY,
                }),
                schema: moveSchema,
                prompt,
              })
              console.log(`Gemini ${modelId} responded successfully`)
              break
            } catch (error) {
              console.error(`Gemini ${modelId} failed:`, error)
              geminiError = error
              continue
            }
          }

          if (!result) {
            throw geminiError || new Error("All Gemini model variations failed")
          }
        } else if (currentLLM.includes("deepseek")) {
          console.log(`Calling DeepSeek with model: ${currentLLM}`)
          // Use OpenRouter for DeepSeek
          result = await callOpenRouter(currentLLM, prompt)
          console.log(`DeepSeek responded successfully`)
        } else {
          throw new Error(`Unsupported model: ${currentLLM}`)
        }

        // ----- helper: clean raw string -> pure SAN -----
        function sanitizeMove(raw: string): string {
          // strip JSON quotes, prefixes like "move:", bullet points, etc.
          const clean = raw
            .replace(/^[^\w]*move[:-]?\s*/i, "") // remove leading "move:"
            .replace(/^[-*\d.\s]+/, "") // bullets / numbers
            .split(/\s+/)[0] // keep first token
            .trim()
          return clean
        }

        const proposedMove = sanitizeMove(result.object.move)

        const legalMoves = chess.moves()
        if (!proposedMove || !legalMoves.includes(proposedMove)) {
          console.log(
            `Attempt ${attempt}: AI proposed illegal/empty move "${proposedMove}". Legal moves:`,
            legalMoves.slice(0, 10),
          )

          // increment illegal counter without throwing
          const updateField = currentPlayer === "white" ? "white_illegal_attempts" : "black_illegal_attempts"

          await supabase
            .from("games")
            .update({ [updateField]: illegalAttempts + 1 })
            .eq("id", gameId)

          continue // retry next loop iteration
        }

        const thinking = result.object.thinking
        const evaluation = result.object.evaluation

        console.log(`${currentPlayer} proposed move: ${proposedMove}`)

        // Validate and make the move
        const moveResult = chess.move(proposedMove)

        if (moveResult) {
          // Legal move! Update database
          successfulMove = true
          console.log(`Legal move confirmed: ${moveResult.san}`)

          const newFen = chess.fen()
          const nextTurn = currentPlayer === "white" ? "black" : "white"
          const moveNumber = (moves?.length || 0) + 1

          // Check for game end conditions
          let gameStatus = "in_progress"
          let winner = null

          if (chess.isCheckmate()) {
            gameStatus = "completed"
            winner = currentPlayer
          } else if (chess.isDraw() || chess.isStalemate()) {
            gameStatus = "draw"
          }

          // Update game state
          await supabase
            .from("games")
            .update({
              fen: newFen,
              current_turn: nextTurn,
              status: gameStatus,
              winner: winner,
              // Reset illegal attempts for current player
              ...(currentPlayer === "white" ? { white_illegal_attempts: 0 } : { black_illegal_attempts: 0 }),
            })
            .eq("id", gameId)

          // Record the move
          await supabase.from("moves").insert({
            game_id: gameId,
            move_number: moveNumber,
            player_color: currentPlayer,
            move_pgn: proposedMove,
            move_san: moveResult.san,
            llm_thinking_process: thinking,
          })

          console.log(`Move recorded successfully: ${moveResult.san}`)

          return NextResponse.json({
            success: true,
            move: moveResult.san,
            thinking,
            evaluation,
            gameStatus,
            winner,
          })
        } else {
          // Illegal move, increment attempt counter
          console.log(`Illegal move attempt ${attempt}: ${proposedMove}`)

          // Update illegal attempts counter
          const updateField = currentPlayer === "white" ? "white_illegal_attempts" : "black_illegal_attempts"
          await supabase
            .from("games")
            .update({ [updateField]: illegalAttempts + attempt })
            .eq("id", gameId)
        }
      } catch (error) {
        console.error(`Error on attempt ${attempt}:`, error)
      }
    }

    // If we get here, all attempts failed
    const winner = currentPlayer === "white" ? "black" : "white"

    await supabase
      .from("games")
      .update({
        status: "resigned",
        winner: winner,
      })
      .eq("id", gameId)

    return NextResponse.json({
      success: false,
      message: `${currentPlayer} resigned after ${MAX_ILLEGAL_ATTEMPTS} illegal move attempts`,
      winner,
    })
  } catch (error) {
    console.error("Error in chess orchestrator:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function callOpenRouter(model: string, prompt: string) {
  console.log(`Calling OpenRouter with model: ${model}`)

  const res = await fetch("https://openrouter.ai/api/v1/chat/completions", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
      "Content-Type": "application/json",
      "X-Title": "Chess AI Battle",
    },
    body: JSON.stringify({
      model,
      stream: false, // ask for JSON but be defensive
      temperature: 0.7,
      max_tokens: 1000,
      messages: [
        {
          role: "system",
          content: "You are a chess grandmaster. Think step-by-step and finally answer ONLY the JSON requested.",
        },
        {
          role: "user",
          content: `${prompt}

Respond with a JSON object EXACTLY like:
{
  "move": "e4",
  "thinking": "…",
  "evaluation": 0.2
}`,
        },
      ],
    }),
  })

  if (!res.ok) {
    throw new Error(`OpenRouter API error ${res.status}: ${res.statusText}`)
  }

  /*  ⚠️  Providers sometimes send invalid JSON or mutate headers.
      Read raw text ALWAYS, then parse defensively.                             */
  const raw = await res.text()
  console.log("↩︎ OpenRouter raw (truncated):", raw.slice(0, 250).replace(/\n/g, " "))

  return parseDeepSeek(raw)
}

/* -------- helper to convert DeepSeek answer -> { object: … } ---------- */
function parseDeepSeek(raw: string) {
  // try strict JSON first
  const jsonMatch = raw.match(/\{[\s\S]*\}/)
  if (jsonMatch) {
    try {
      const p = JSON.parse(jsonMatch[0])
      return {
        object: {
          move: p.move ?? "",
          thinking: p.thinking ?? raw,
          evaluation: Number(p.evaluation) ?? 0,
        },
      }
    } catch (_) {
      /* fallthrough */
    }
  }

  // loose extract single algebraic move
  const moveMatch = raw.match(/(?:move|Move):?\s*([NBRQK]?[a-h]?[1-8]?x?[a-h][1-8](?:=[NBRQ])?|O-O(?:-O)?)/)
  return {
    object: {
      move: moveMatch?.[1] ?? "",
      thinking: raw,
      evaluation: 0,
    },
  }
}

function createPrompt(
  currentPlayer: string,
  llmModel: string,
  fen: string,
  moveHistory: string,
  legalMoves: string[],
): string {
  const modelName = llmModel.includes("gemini") ? "Google Gemini 2.5 Flash" : "DeepSeek R1"

  return `You are a chess Grandmaster representing ${modelName}, playing as ${currentPlayer.toUpperCase()}.

Current Position (FEN): ${fen}
Game History: ${moveHistory || "Game just started"}

Legal moves available: ${legalMoves.join(", ")}

This is a high-stakes AI vs AI chess battle! Analyze this position with maximum depth and choose your best move. Consider:

1. **Tactical Analysis**: Look for immediate threats, pins, forks, skewers, and tactical motifs
2. **Strategic Elements**: Piece activity, pawn structure, king safety, control of key squares
3. **Positional Factors**: Space advantage, weak squares, piece coordination
4. **Endgame Considerations**: If applicable, evaluate the resulting endgame
5. **Psychological Pressure**: This is a battle between AI models - play your strongest move!

Think like a world champion and provide your detailed analysis, then choose your move.

CRITICAL REQUIREMENTS:
- You MUST choose from the legal moves listed above
- Use standard algebraic notation (e.g., e4, Nf3, Bxf7+, O-O, Qh5#)
- Do not use coordinate notation (e.g., e2-e4)
- Double-check that your chosen move is in the legal moves list

Show your analytical prowess and make ${modelName} proud!`
}
